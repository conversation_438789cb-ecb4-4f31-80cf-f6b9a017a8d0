# get_openid.py
from flask import Blueprint, request, jsonify
from applications.extensions import db
from applications.models import Applet_user,ygong
import requests


bp = Blueprint('get_openid', __name__)

# 获取OPENID
APPID = 'wxc29a0e90cbf3c18a'
SECRET = 'b243822605f1cd9259d0d3157dcd8fea'

@bp.route('/get_openid', methods=['POST'])
def get_openid():
    data = request.get_json()
    code = data.get('code')
    print('Received code:', code)  # 调试输出
    if not code:
        return jsonify({'message': '缺少 code 参数', 'status': 400})
    url = f'https://api.weixin.qq.com/sns/jscode2session?appid={APPID}&secret={SECRET}&js_code={code}&grant_type=authorization_code'
    try:
        response = requests.get(url)
        result = response.json()
        if 'openid' in result:
            openid = result['openid']
            # 检查员工是否存在且启用
            personnel = ygong.query.filter_by(openid=openid).first()
            if personnel:
                if personnel.enable != 1:
                    return jsonify({'message': '您没有权限操作该应用', 'status': 403})
            # 检查 openid 是否存在于小程序用户表中
            count = Applet_user.query.filter_by(openid=openid).count()
            if count > 0:
                return jsonify({'message': 'openid 存在', 'exists': True, 'status': 200, 'openid': openid, 'yes': '1'})
            else:
                return jsonify({'message': 'openid 不存在', 'exists': False, 'status': 200, 'openid': openid, 'yes': ''})
        else:
            return jsonify({'message': f'获取 openid 失败: {result.get("errmsg", "未知错误")}', 'status': 500})
    except Exception as e:
        print('请求出错:', e)  # 添加日志输出
        return jsonify({'message': f'请求出错: {str(e)}', 'status': 500})
    
@bp.route('/getlogin', methods=['GET', 'POST'])
def getlogin():
    if request.method == 'POST':
        openid = request.json.get('openid')
        if not openid:
            return jsonify({"error": "openid is missing"}), 400
        try:
            personnel = ygong.query.filter_by(openid=openid).first()
            if personnel:
                # Convert the ygong object to a dictionary
                personnel_dict = {
                    'id': personnel.id,
                    'openid': personnel.openid,
                    'name': personnel.name,
                    'employee_id': personnel.employee_id,
                    'phone': personnel.phone,
                    'dept_id': personnel.dept_id,
                    'position': personnel.position,
                    'enable': personnel.enable,
                    'create_at': personnel.create_at.isoformat() if personnel.create_at else None,
                    'update_at': personnel.update_at.isoformat() if personnel.update_at else None
                }
                return jsonify(personnel_dict)
            else:
                return jsonify({"error": "No record found with the given openid"}), 404
        except Exception as e:
            return jsonify({"error": f"Database error: {str(e)}"}), 500
    return jsonify({"message": "This endpoint accepts POST requests only."}), 405