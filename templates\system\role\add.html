<!DOCTYPE html>
<html>
<head>
    <title>角色新增</title>
    {% include 'system/common/header.html' %}</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="roleName" lay-verify="required" autocomplete="off" placeholder="角色名称"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标识</label>
                    <div class="layui-input-block">
                        <input type="text" name="roleCode" lay-verify="required" autocomplete="off" placeholder="角色标识"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" name="enable" value="1" title="开启">
                        <input type="radio" name="enable" value="0" title="关闭" checked>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="text" value="0" name="sort" lay-verify="required" autocomplete="off"
                               placeholder="排序" min="0" step="1" lay-affix="number" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea placeholder="角色描述" name="details" class="layui-textarea"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="role-save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['form', 'jquery'], function () {
    let form = layui.form
    let $ = layui.jquery

    form.on('submit(role-save)', function (data) {
      $.ajax({
        url: '/system/role/save',
        data: JSON.stringify(data.field),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (result) {
          if (result.success) {
            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
              parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
              parent.layui.table.reload('role-table')
            })
          } else {
            layer.msg(result.msg, { icon: 2, time: 1000 })
          }
        }
      })
      return false
    })
  })
</script>
</body>
</html>