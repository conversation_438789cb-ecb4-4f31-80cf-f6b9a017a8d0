from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, or_, desc
from applications.common.utils.rights import authorize
from applications.common.utils.http import success_api, fail_api, table_api
from applications.common.services.dingtalk_service import DingTalkService


bp = Blueprint('dingding', __name__, url_prefix='/dindin')

@bp.get('/')
@authorize("system:dindin:main", log=True)
def main():
    """钉钉日志管理页面"""
    return render_template('system/dindin/main.html')


@bp.get('/logs')
@authorize("system:dindin:logs", log=True)
def get_logs():
    """获取钉钉日志数据API"""
    try:
        # 获取请求参数
        days = request.args.get('days', 7, type=int)
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        template_name = request.args.get('template_name')
        userid = request.args.get('userid')

        # 初始化钉钉服务
        dingtalk_service = DingTalkService()

        if start_time and end_time:
            # 使用自定义时间范围
            try:
                start_timestamp = int(datetime.fromisoformat(start_time).timestamp() * 1000)
                end_timestamp = int(datetime.fromisoformat(end_time).timestamp() * 1000)
                logs = dingtalk_service.get_logs(start_timestamp, end_timestamp, template_name, userid)
                result = dingtalk_service.format_logs_for_response(logs)
            except ValueError as e:
                return fail_api(f"时间格式错误: {str(e)}")
        else:
            # 使用天数范围
            result = dingtalk_service.get_logs_by_days(days)

        if result["status"] == "success":
            return table_api(
                data=result["logs"],
                count=result["total_count"],
                msg="获取日志成功"
            )
        else:
            return fail_api(result["error_message"])

    except Exception as e:
        return fail_api(f"获取日志失败: {str(e)}")


@bp.post('/test-connection')
@authorize("system:dindin:test", log=True)
def test_connection():
    """测试钉钉API连接"""
    try:
        dingtalk_service = DingTalkService()
        # 尝试获取token来测试连接
        token = dingtalk_service._get_access_token()
        if token:
            return success_api("钉钉API连接测试成功")
        else:
            return fail_api("钉钉API连接测试失败")
    except Exception as e:
        return fail_api(f"连接测试失败: {str(e)}")

