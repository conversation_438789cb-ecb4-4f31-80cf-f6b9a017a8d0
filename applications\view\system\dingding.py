from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import func, or_, desc
from applications.common.utils.rights import authorize


bp = Blueprint('dingding', __name__, url_prefix='/dingding')

@bp.get('/')
@authorize("system:dindin:main", log=True)
def main():
    """项目回款情况一览表页面"""
    return render_template('system/dindin/main.html')

