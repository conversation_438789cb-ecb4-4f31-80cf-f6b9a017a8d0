from flask import session, current_app
from flask_login import current_user
from applications.models import Power


def init_template_directives(app):
    @app.template_global()
    def authorize(power):
        # 对于超级管理员，首先检查菜单项是否存在且启用
        if current_user.username == current_app.config.get("SUPERADMIN"):
            # 如果不是菜单权限代码（例如system:role:edit而不是system:project_cost:dept），直接返回True
            # 菜单权限代码通常是三段式的，如system:module:operation
            if power and len(power.split(':')) == 3:
                # 查找对应的权限记录
                power_obj = Power.query.filter_by(code=power).first()
                # 如果找到权限记录且该权限未启用，返回False
                if power_obj and power_obj.enable == 0:
                    return False
            return True
        else:
            # 对于普通用户，检查session中的permissions
            return bool(power in session.get('permissions', []))
