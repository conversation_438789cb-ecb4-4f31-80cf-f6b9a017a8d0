import json
from flask import Blueprint, render_template, request, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, case

from applications.common import curd
from applications.common.curd import enable_status, disable_status
from applications.common.utils.http import table_api, fail_api, success_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db

from applications.models import LogInfo, ygong

bp = Blueprint('log_info', __name__, url_prefix='/log_info')






@bp.route('/')
@login_required
@authorize("system:log_info:info", log=True)
def index():
    return render_template('system/log_info/log_info.html')


#   用户分页查询
@bp.get('/data')
@login_required
@authorize("system:log_info:main", log=True)
def data():
    name = str_escape(request.args.get('name', type=str))
    projectNumber = str_escape(request.args.get('projectNumber', type=str))
    status = str_escape(request.args.get('status', type=str))

    filters = []
    if name:
        filters.append(LogInfo.name.ilike(f'%{name}%'))
    if projectNumber:
        filters.append(LogInfo.projectNumber.ilike(f'%{projectNumber}%'))
    if status:
        filters.append(LogInfo.status.ilike(f'%{status}%'))
    
    # 获取当前用户的 dept_id
    current_user_dept_id = current_user.dept_id
    
    # 通过 LogInfo 中的 openid 查询 ygong 模型，获取 dept_id
    # 并过滤与当前用户 dept_id 匹配的日志
    if current_user_dept_id:
        # 查询 ygong 模型中与当前用户 dept_id 匹配的员工
        yg_users = ygong.query.filter_by(dept_id=current_user_dept_id).all()
        yg_openids = [yg.openid for yg in yg_users]  # 获取这些员工的 openid
        filters.append(LogInfo.openid.in_(yg_openids))  # 过滤这些员工的日志
    
    # 修改状态排序逻辑，使用新的case语法
    status_order = case(
        (LogInfo.status == 'pending', 1),
        (LogInfo.status == 'approved', 2),
        (LogInfo.status == 'rejected', 3),
        else_=4
    )
    
    query = db.session.query(LogInfo).filter(*filters)\
        .order_by(status_order, desc(LogInfo.created_at))\
        .layui_paginate()

    return table_api(
        data=[{
            'id': log.id,
            'projectPrefix': log.projectPrefix,
            'projectNumber': log.projectNumber,
            'regularWorkingHours': log.regularWorkingHours,
            'overtimeWorkingHours': log.overtimeWorkingHours,
            'projectLocation': log.projectLocation,
            'content': log.content,
            'totalHours': log.totalHours,
            'created_at': log.created_at.strftime('%Y-%m-%d %H:%M:%S') if log.created_at else None,
            'status': log.status,
            'name': log.name
        } for log in query.items],
        count=query.total)


@bp.post('/approve/<int:log_id>')
@login_required
@authorize("system:log_info:approve", log=True)
def approve_log(log_id):
    log = LogInfo.query.get_or_404(log_id)
    log.status = 'approved'
    db.session.commit()
    return success_api(msg="审批成功")

@bp.post('/reject/<int:log_id>')
@login_required
@authorize("system:log_info:reject", log=True)
def reject_log(log_id):
    log = LogInfo.query.get_or_404(log_id)
    log.status = 'rejected'
    db.session.commit()
    return fail_api(msg="拒绝审批")

@bp.get('/notifications')
@login_required
@authorize("system:log_info:notifications", log=True)
def get_notifications():
    # 获取当前用户的 dept_id
    current_user_dept_id = current_user.dept_id
    
    # 如果是管理员，可以看到所有待办
    if current_user.username == current_app.config.get("SUPERADMIN"):
        pending_logs = LogInfo.query.filter_by(status='pending').all()
        pending_count = len(pending_logs)
    else:
        # 非管理员只能看到本部门员工的待办
        if current_user_dept_id:
            # 查询部门内所有员工
            yg_users = ygong.query.filter_by(dept_id=current_user_dept_id).all()
            yg_openids = [yg.openid for yg in yg_users]  # 获取这些员工的 openid
            
            # 只统计本部门员工的待办日志
            pending_logs = LogInfo.query.filter(
                LogInfo.status == 'pending',
                LogInfo.openid.in_(yg_openids)
            ).all()
            pending_count = len(pending_logs)
        else:
            # 没有部门ID的用户不显示任何待办
            pending_logs = []
            pending_count = 0
    
    has_pending = pending_count > 0

    # 构建与message接口一致的返回格式，确保前端能够正确显示通知数量
    return success_api(msg=json.dumps({
        'has_pending': has_pending,
        'pending_count': pending_count,
        'data': [
            {
                "id": 1,
                "title": "代办",
                "children": [{
                        'id': log.id,
                        "avatar": "https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png",
                        'title': f'日志需要审批 - {log.name}',
                        'content': f'项目编号: {log.projectNumber}',
                        'type': 'log_approval',
                        'time': log.created_at.strftime("%Y-%m-%d %H:%M")
                    } for log in pending_logs]
            }
        ]
    }))

@bp.post('/create')
@login_required
@authorize("system:log_info:create", log=True)
def create_log():
    data = request.get_json()
    log = LogInfo(
        openid=current_user.openid,  # 关联当前用户的 openid
        name=data['name'],
        projectPrefix=data['projectPrefix'],
        projectNumber=data['projectNumber'],
        regularWorkingHours=data['regularWorkingHours'],
        overtimeWorkingHours=data['overtimeWorkingHours'],
        projectLocation=data['projectLocation'],
        content=data['content'],
        totalHours=data['totalHours']
    )
    db.session.add(log)
    db.session.commit()
    return success_api(msg="日志创建成功")






































