<!DOCTYPE html>
<html>
<head>
    <title>兑换码编辑</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">

                <div class="layui-form-item">
                    <label class="layui-form-label">编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="id" lay-verify="title" autocomplete="off" placeholder="请输入编号"
                               class="layui-input" value="{{ gift.id }}" disabled>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">兑换码</label>
                    <div class="layui-input-block">
                        <input type="text" name="key" lay-verify="title" autocomplete="off" placeholder="请输入兑换码"
                               class="layui-input" value="{{ gift.key }}">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">兑换内容</label>
                    <div class="layui-input-block">
                        <textarea placeholder="请输入兑换内容" name="content" class="layui-textarea">{{ gift.content }}</textarea>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input type="radio" name="enable" value="1" title="开启" {{ 'checked' if gift.enable == 1 }}>
                        <input type="radio" name="enable" value="0" title="关闭" {{ 'checked' if gift.enable == 0 }}>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form
        let $ = layui.jquery

        form.on('submit(save)', function (data) {

            $.ajax({
                url: '/system/gift/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name))  //关闭当前页
                            parent.layui.table.reload('gift-table')  // 目标表格
                        })
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000})
                    }
                }
            })
            return false
        })
    })
</script>

</body>
</html>