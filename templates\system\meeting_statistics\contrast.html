<!DOCTYPE html>
<html>
<head>
    <title>部门工时与预估工时对比</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
    <style>
        .layui-card {
            margin-bottom: 15px;
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: bold;
        }
        .layui-card-body {
            padding: 15px;
        }
        .layui-table {
            margin-top: 0;
        }
        .echarts-chart {
            height: 300px;
        }
        .positive {
            color: red;
        }
        .negative {
            color: green;
        }
        .neutral {
            color: #FFA500;
        }
        .page-title {
            position: relative;
            padding: 15px 0;
            /* margin-bottom: 20px; */
            text-align: center;
        }
        .page-title h2 {
            font-size: 24px;
            font-weight: 600;
            color: #344767;
            margin: 0;
            padding: 0 0 10px;
            position: relative;
            display: inline-block;
        }
        .page-title h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 3px;
            background: linear-gradient(to right, #1890ff, #52c41a);
            border-radius: 2px;
        }
        .page-title .subtitle {
            font-size: 14px;
            color: #666;
            margin-top: 8px;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="page-title">
                <h2>部门工时与预估工时对比</h2>
                <div class="subtitle">实时监控部门工时使用情况，优化资源配置</div>
            </div>
            <!-- 筛选表单 -->
            <form class="layui-form" action="" method="get">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">项目编号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="project_code" placeholder="请输入项目编号" autocomplete="off" class="layui-input" value="{{ request.args.get('project_code', '') }}">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">项目类型</label>
                        <div class="layui-input-inline">
                            <select name="project_type">
                                <option value="">全部</option>
                                {% for dept in project_manage_depts %}
                                <option value="{{ dept.dept_name }}" {% if request.args.get('project_type') == dept.dept_name %}selected{% endif %}>{{ dept.dept_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">部门名称</label>
                        <div class="layui-input-inline">
                            {% if available_depts|length > 1 %}
                            <select name="dept_name" lay-search>
                                <option value="">全部</option>
                                {% for dept in available_depts %}
                                <option value="{{ dept.dept_name }}" {% if request.args.get('dept_name') == dept.dept_name %}selected{% endif %}>
                                    {{ dept.dept_name }}
                                </option>
                                {% endfor %}
                            </select>
                            {% else %}
                            <input type="text" class="layui-input" value="{{ current_dept.dept_name }}" readonly>
                            <input type="hidden" name="dept_name" value="{{ current_dept.dept_name }}">
                            {% endif %}
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">项目地点</label>
                        <div class="layui-input-inline">
                            <select name="location">
                                <option value="">全部</option>
                                <option value="厂内" {% if request.args.get('location') == '厂内' %}selected{% endif %}>厂内</option>
                                <option value="厂外" {% if request.args.get('location') == '厂外' %}selected{% endif %}>厂外</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" lay-submit lay-filter="search">
                            <i class="layui-icon layui-icon-search"></i>搜索
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                        </button>
                    </div>
                </div>
            </form>

            <table class="layui-table">
                <thead>
                    <tr>
                        <th>项目类型</th>
                        <th>项目编码</th>
                        <th>项目名称</th>
                        <th>部门名称</th>
                        <th>项目地点</th>
                        <th>预估工时</th>
                        <th>实际工时</th>
                        <th>差异值</th>
                        <th>差异百分比</th>
                    </tr>
                </thead>
                <tbody>
                    {% if contrast_results %}
                        {% for result in contrast_results %}
                        <tr>
                            <td>{{ result.project_dept_name }}</td>
                            <td>{{ result.project_code }}</td>
                            <td>{{ result.project_name }}</td>
                            <td>{{ result.dept_name }}</td>
                            <td>{{ result.location }}</td>
                            <td>{{ result.estimate_hours }}</td>
                            <td>
                                {% if result.actual_hours == 0 %}
                                    <span style="color: orange;">0 (无匹配日志)</span>
                                {% else %}
                                    {{ result.actual_hours }}
                                {% endif %}
                            </td>
                            <td class="{% if result.difference > 0 %}positive{% else %}negative{% endif %}">
                                {{ result.difference }}
                            </td>
                            <td class="{% if result.difference_percentage >= 20 %}positive{% elif result.difference_percentage <= -20 %}negative{% else %}neutral{% endif %}">
                                {{ result.difference_percentage }}%
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="9" style="text-align: center;">
                                <div style="display: flex; flex-direction: column; align-items: center; padding: 20px;">
                                    <i class="layui-icon layui-icon-face-cry" style="font-size: 30px; color: #999;"></i>
                                    <p style="margin-top: 10px; color: #666;">暂无数据</p>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>

            <!-- 分页，只在有数据时显示 -->
            {% if contrast_results %}
                <div id="pagination"></div>
            {% endif %}
        </div>
    </div>

    <!-- 图表部分，只在有数据时显示 -->
    {% if contrast_results %}
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">差异值分布</div>
                        <div class="layui-card-body">
                            <div id="differenceBarChart" style="height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="layui-card">
        <div class="layui-card-body">
            <div style="text-align: center; padding: 30px;">
                <i class="layui-icon layui-icon-chart" style="font-size: 50px; color: #999;"></i>
                <p style="margin-top: 10px; color: #666;">暂无数据可供图表展示</p>
            </div>
        </div>
    </div>
    {% endif %}

    {% include 'system/common/footer.html' %}

    <!-- ECharts 脚本，只在有数据时执行 -->
    <script src="{{ url_for('static', filename='index/js/echarts.min.js') }}"></script>
    <script>
        layui.use(['form', 'laypage'], function(){
            var form = layui.form;
            var laypage = layui.laypage;
            
            {% if contrast_results %}
            // 初始化分页
            laypage.render({
                elem: 'pagination',
                count: {{ total }},
                limit: {{ limit }},
                curr: {{ page }},
                limits: [10, 20, 30, 50],
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                jump: function(obj, first){
                    if(!first){
                        var url = new URL(window.location.href);
                        url.searchParams.set('page', obj.curr);
                        url.searchParams.set('limit', obj.limit);
                        window.location.href = url.toString();
                    }
                }
            });
            {% endif %}
            
            // 监听表单提交
            form.on('submit(search)', function(data){
                return true;
            });
            
            // 重置按钮事件
            form.on('reset', function(){
                setTimeout(function(){
                    form.render();
                }, 0);
            });
        });

        {% if contrast_results %}
        // 差异值和差异百分比分布图
        var differenceBarChart = echarts.init(document.getElementById('differenceBarChart'));
        var differenceBarOption = {
            title: {
                text: '差异值和差异百分比分布',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params) {
                    var result = params[0].name + '<br/>';
                    params.forEach(function(param) {
                        var marker = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + param.color + '"></span>';
                        if (param.seriesName === '差异值') {
                            result += marker + param.seriesName + ': ' + param.value + ' 小时<br/>';
                        } else {
                            result += marker + param.seriesName + ': ' + param.value + '%<br/>';
                        }
                    });
                    return result;
                }
            },
            grid: {
                right: '10%'
            },
            legend: {
                data: ['差异值', '差异百分比'],
                top: 30
            },
            xAxis: {
                type: 'category',
                data: [
                    {% for result in contrast_results %}
                        '{{ result.project_dept_name }}{{ result.project_code }}\n{{ result.dept_name }}',
                    {% endfor %}
                ],
                axisLabel: {
                    interval: 0,
                    rotate: 45
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '差异值(小时)',
                    position: 'left'
                },
                {
                    type: 'value',
                    name: '差异百分比(%)',
                    position: 'right'
                }
            ],
            series: [
                {
                    name: '差异值',
                    type: 'bar',
                    data: [
                        {% for result in contrast_results %}
                        {
                            value: {{ result.difference }},
                            itemStyle: {
                                color: {{ result.difference_percentage }} >= 20 
                                    ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#ff6b6b'},
                                        {offset: 1, color: '#ff4757'}
                                    ])
                                    : {{ result.difference_percentage }} <= -20
                                    ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#26de81'},
                                        {offset: 1, color: '#20bf6b'}
                                    ])
                                    : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#ffd66b'},
                                        {offset: 1, color: '#FFA500'}
                                    ])
                            }
                        },
                        {% endfor %}
                    ],
                    barWidth: '30%'
                },
                {
                    name: '差异百分比',
                    type: 'line',
                    yAxisIndex: 1,
                    symbol: 'circle',
                    symbolSize: 8,
                    lineStyle: {
                        width: 2
                    },
                    data: [
                        {% for result in contrast_results %}
                        {
                            value: {{ result.difference_percentage }},
                            itemStyle: {
                                color: {{ result.difference_percentage }} >= 20 ? '#ff4757' : 
                                       {{ result.difference_percentage }} <= -20 ? '#20bf6b' : '#FFA500'
                            }
                        },
                        {% endfor %}
                    ]
                }
            ]
        };
        differenceBarChart.setOption(differenceBarOption);

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            differenceBarChart.resize();
        });
        {% endif %}
    </script>
</body>
</html>
