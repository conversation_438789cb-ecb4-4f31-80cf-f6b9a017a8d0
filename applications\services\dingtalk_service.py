import requests
import time
import json
from typing import List, Dict, Optional
from flask import current_app


class DingTalkService:
    """钉钉API服务类"""

    def __init__(self):
        self.app_key = current_app.config.get('DINGTALK_APP_KEY')
        self.app_secret = current_app.config.get('DINGTALK_APP_SECRET')
        self.access_token = None
        self.token_expire_time = 0

        if not self.app_key or not self.app_secret:
            raise ValueError("钉钉API配置缺失，请检查DINGTALK_APP_KEY和DINGTALK_APP_SECRET配置")

    def _get_access_token(self) -> str:
        """获取/刷新access_token，带缓存机制"""
        if time.time() < self.token_expire_time - 60:  # 提前60秒刷新
            return self.access_token

        url = f"https://oapi.dingtalk.com/gettoken?appkey={self.app_key}&appsecret={self.app_secret}"
        try:
            response = requests.get(url, timeout=5)
            response.raise_for_status()
            data = response.json()

            if data.get('errcode') == 0:
                self.access_token = data['access_token']
                self.token_expire_time = time.time() + 7200  # 有效期2小时
                return self.access_token
            else:
                raise Exception(f"获取token失败: {data.get('errmsg')}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")

    def _call_api(self, endpoint: str, params: Dict) -> Dict:
        """调用钉钉API的通用方法"""
        token = self._get_access_token()
        url = f"https://oapi.dingtalk.com{endpoint}?access_token={token}"

        try:
            response = requests.post(url, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            if result.get('errcode') != 0:
                raise Exception(f"API错误: {result.get('errmsg')}")
            return result

        except requests.exceptions.RequestException as e:
            raise Exception(f"API请求失败: {str(e)}")

    def get_logs(
        self,
        start_time: int,
        end_time: int,
        template_name: Optional[str] = None,
        userid: Optional[str] = None
    ) -> List[Dict]:
        """获取指定时间范围内的日志

        :param start_time: 开始时间戳（毫秒）
        :param end_time: 结束时间戳（毫秒）
        :param template_name: 日志模板名称（可选）
        :param userid: 用户ID（可选）
        :return: 日志数据列表
        """
        all_logs = []
        cursor = 0

        while True:
            params = {
                "start_time": start_time,
                "end_time": end_time,
                "cursor": cursor,
                "size": 20  # 钉钉API单次最大返回数量
            }

            if template_name:
                params["template_name"] = template_name
            if userid:
                params["userid"] = userid

            print(f"调用钉钉API参数: {params}")
            result = self._call_api("/topapi/report/list", params)
            print(f"钉钉API返回结果: {result}")
            data = result.get("result", {})

            if not data.get("data_list"):
                break

            all_logs.extend(data["data_list"])
            next_cursor = data.get("next_cursor", 0)

            if next_cursor == 0:
                break

            cursor = next_cursor

        return all_logs

    def format_logs_for_response(self, logs: List[Dict]) -> Dict:
        """格式化日志数据为标准响应格式"""
        formatted_logs = []

        for log in logs:
            log_data = {
                "report_id": log.get('report_id'),
                "creator_name": log.get('creator_name'),
                "create_time": time.strftime('%Y-%m-%d %H:%M:%S',
                                           time.localtime(log.get('create_time', 0)/1000)),
                "create_timestamp": log.get('create_time'),
                "contents": []
            }

            # 处理日志内容字段
            for content in log.get('contents', []):
                log_data['contents'].append({
                    "key": content.get('key'),
                    "value": content.get('value')
                })

            formatted_logs.append(log_data)

        return {
            "status": "success",
            "total_count": len(formatted_logs),
            "logs": formatted_logs
        }

    def get_logs_by_days(self, days: int = 1) -> Dict:
        """获取最近指定天数的日志

        :param days: 天数，默认7天
        :return: 格式化的日志数据
        """
        end_time = int(time.time() * 1000)
        start_time = end_time - days * 24 * 60 * 60 * 1000

        try:
            logs = self.get_logs(start_time, end_time)
            return self.format_logs_for_response(logs)
        except Exception as e:
            return {
                "status": "error",
                "error_message": str(e),
                "total_count": 0,
                "logs": []
            }

    def get_user_templates(self, userid: Optional[str] = None) -> List[Dict]:
        """获取用户可见的日志模板列表

        :param userid: 用户ID（可选，不传则获取所有模板）
        :return: 模板列表
        """
        params = {}
        if userid:
            params["userid"] = userid

        try:
            result = self._call_api("/topapi/report/template/listbyuserid", params)
            templates = result.get("result", {}).get("template_list", [])

            # 格式化模板数据，使用正确的字段映射
            formatted_templates = []
            for template in templates:
                template_name = template.get("name")  # 实际字段是 'name'
                template_code = template.get("report_code")  # 实际字段是 'report_code'

                # 跳过无效的模板数据
                if not template_name or not template_code:
                    continue

                formatted_templates.append({
                    "template_id": template_code,  # 使用 report_code 作为 ID
                    "template_name": template_name,  # 使用 name 作为名称
                    "template_icon": template.get("icon_url", ""),  # 使用 icon_url
                    "template_url": template.get("url", ""),  # 添加模板URL
                    "report_code": template_code  # 保留原始 report_code
                })

            return formatted_templates

        except Exception as e:
            raise Exception(f"获取模板列表失败: {str(e)}")

    def format_templates_for_response(self, templates: List[Dict]) -> Dict:
        """格式化模板数据为标准响应格式"""
        return {
            "status": "success",
            "total_count": len(templates),
            "templates": templates
        }
