try:
    from flask_session import Session
    sess = Session()
except ImportError:
    # 如果flask_session不可用，使用Flask内置的session
    print("警告：未安装Flask-Session，将使用Flask内置的session")
    sess = None


def init_session(app):
    if sess:
        sess.init_app(app)
    else:
        # 使用Flask内置的session
        app.config['SESSION_TYPE'] = 'filesystem'
        app.config['SESSION_PERMANENT'] = False
        app.config['SESSION_USE_SIGNER'] = True