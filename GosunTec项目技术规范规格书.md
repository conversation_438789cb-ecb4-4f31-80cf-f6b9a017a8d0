# GosunTec项目技术规范规格书

<div style="text-align: center; margin-top: 100px;">
    <h1 style="font-size: 26pt;">GosunTec项目</h1>
    <h2 style="font-size: 24pt;">技术规范规格书</h2>
    <p style="margin-top: 100px; font-size: 14pt;">文档版本：V1.0</p>
    <p style="font-size: 14pt;">编制日期：2023年11月15日</p>
    <p style="font-size: 14pt;">编制人：技术团队</p>
</div>

<div style="page-break-after: always;"></div>

## 文档修订历史

| 版本号 | 修订日期 | 修订人 | 修订内容 |
| ------ | -------- | ------ | -------- |
| V1.0   | 2023-11-15 | 技术团队 | 初始版本 |

<div style="page-break-after: always;"></div>

## 目录

[TOC]

<div style="page-break-after: always;"></div>

## 1. 项目概述

### 1.1 项目背景与目标

GosunTec项目是基于Pear Admin Flask框架开发的企业级项目管理系统，旨在提供全面的项目生命周期管理解决方案。该系统主要用于跟踪和管理企业内部项目的进度、成本和付款情况，为企业提供项目管理的数字化工具。

项目的主要目标包括：

- 建立统一的项目管理平台，实现项目全生命周期的数字化管理
- 提高项目进度和成本的透明度，便于管理层及时了解项目状态
- 优化项目资源分配，提高项目执行效率
- 加强项目成本控制，实现预算与实际成本的对比分析
- 规范项目付款流程，提高财务管理效率
- 提供数据分析和可视化功能，支持管理决策

### 1.2 系统功能概述

GosunTec项目管理系统提供以下核心功能：

1. **用户与权限管理**
   - 用户账户管理
   - 角色权限配置
   - 部门组织管理
   - 操作日志记录

2. **项目基础管理**
   - 项目信息维护
   - 项目类型管理
   - 项目状态跟踪
   - 项目预估成本设置

3. **项目进度管理**
   - 项目里程碑设置
   - 进度状态自动更新
   - 项目时间线展示
   - 进度异常预警

4. **项目成本管理**
   - BOM成本录入与管理
   - 人工成本计算
   - 其他成本管理
   - 成本对比分析

5. **项目付款管理**
   - 付款计划设置
   - 回款记录管理
   - 付款状态跟踪
   - 付款提醒

6. **报表与统计分析**
   - 项目进度统计
   - 成本分析报表
   - 付款情况统计
   - 数据可视化展示

7. **小程序接口**
   - 移动端项目查询
   - 工时记录提交
   - 项目状态查看

### 1.3 用户角色定义

系统定义了以下主要用户角色：

1. **系统管理员**
   - 负责系统配置和维护
   - 管理用户账户和权限
   - 设置系统参数

2. **项目管理员**
   - 创建和管理项目
   - 分配项目资源
   - 监控项目进度和成本

3. **部门管理员**
   - 管理部门内的项目
   - 审核部门工时和成本
   - 查看部门项目报表

4. **财务人员**
   - 管理项目付款记录
   - 跟踪项目成本
   - 生成财务报表

5. **普通用户**
   - 查看被授权的项目信息
   - 提交工时记录
   - 查看个人相关的项目进度

6. **小程序用户**
   - 通过微信小程序访问系统
   - 提交工时和日志
   - 查询项目基本信息

<div style="page-break-after: always;"></div>

## 2. 系统架构

### 2.1 技术栈概述

GosunTec项目采用了现代化的Web应用技术栈，主要包括：

1. **后端技术**
   - **框架**：Flask (Python Web框架)
   - **ORM**：SQLAlchemy (对象关系映射)
   - **数据序列化**：Flask-Marshmallow
   - **用户认证**：Flask-Login
   - **数据库迁移**：Flask-Migrate
   - **表单处理**：Flask-WTF

2. **前端技术**
   - **UI框架**：Pear Admin Layui
   - **JavaScript库**：jQuery
   - **图表库**：ECharts
   - **数据表格**：Layui Table
   - **表单组件**：Layui Form

3. **数据库**
   - **主数据库**：MySQL
   - **备选数据库**：SQLite (开发环境)

4. **部署环境**
   - **Web服务器**：Gunicorn
   - **反向代理**：Nginx
   - **容器化**：Docker

### 2.2 系统架构图

```
+----------------------------------+
|            客户端层               |
|  +------------+  +------------+  |
|  |  Web浏览器  |  |  微信小程序  |  |
|  +------------+  +------------+  |
+----------------------------------+
              |
              | HTTP/HTTPS
              |
+----------------------------------+
|            应用服务层             |
|  +------------+  +------------+  |
|  |   Nginx    |  |  Gunicorn  |  |
|  +------------+  +------------+  |
|           |            |         |
|           +-----+------+         |
|                 |                |
|         +---------------+        |
|         | Flask应用服务器 |        |
|         +---------------+        |
|                 |                |
|    +------------------------+    |
|    |       业务逻辑层        |    |
|    | +---------+ +--------+ |    |
|    | | 用户管理 | | 权限管理| |    |
|    | +---------+ +--------+ |    |
|    | +---------+ +--------+ |    |
|    | | 项目管理 | | 成本管理| |    |
|    | +---------+ +--------+ |    |
|    | +---------+ +--------+ |    |
|    | | 进度管理 | | 付款管理| |    |
|    | +---------+ +--------+ |    |
|    +------------------------+    |
+----------------------------------+
              |
              | SQL
              |
+----------------------------------+
|            数据持久层             |
|  +------------+  +------------+  |
|  | SQLAlchemy |  | Marshmallow|  |
|  +------------+  +------------+  |
|           |            |         |
|           +-----+------+         |
|                 |                |
|         +---------------+        |
|         |    MySQL数据库  |        |
|         +---------------+        |
+----------------------------------+
```

### 2.3 系统组件说明

#### 2.3.1 核心组件

1. **Flask应用服务器**
   - 处理HTTP请求和响应
   - 路由管理和URL映射
   - 视图函数和模板渲染
   - 会话管理和用户认证

2. **SQLAlchemy ORM**
   - 数据库模型定义
   - 数据库查询构建
   - 事务管理
   - 数据库连接池管理

3. **Marshmallow序列化**
   - 数据模型序列化
   - 数据验证和转换
   - API响应格式化

4. **Layui前端框架**
   - 页面布局和样式
   - 表单和表格组件
   - 交互效果和动画
   - 响应式设计

#### 2.3.2 扩展组件

1. **插件系统**
   - 支持动态加载插件
   - 插件生命周期管理
   - 插件事件机制

2. **文件上传组件**
   - 支持多种文件格式
   - 文件存储和管理
   - 图片处理和缩放

3. **邮件发送组件**
   - 邮件模板管理
   - SMTP服务配置
   - 邮件队列处理

4. **微信小程序接口**
   - 用户认证和授权
   - 数据同步和推送
   - 消息通知

### 2.4 开发环境与工具

1. **开发环境**
   - **IDE**：PyCharm/VSCode
   - **版本控制**：Git
   - **包管理**：pip
   - **虚拟环境**：venv/virtualenv

2. **测试工具**
   - **单元测试**：pytest
   - **API测试**：Postman
   - **性能测试**：JMeter

3. **部署工具**
   - **容器化**：Docker
   - **CI/CD**：Jenkins/GitHub Actions
   - **监控**：Prometheus + Grafana

<div style="page-break-after: always;"></div>

## 3. 数据库设计

### 3.1 数据库模型概述

GosunTec项目采用关系型数据库MySQL作为主要的数据存储方案，使用SQLAlchemy ORM框架进行数据库操作。系统的数据库设计遵循规范化原则，确保数据的一致性和完整性。

数据库模型主要分为以下几个部分：

1. **系统管理模块**
   - 用户管理
   - 角色权限管理
   - 部门管理
   - 系统日志

2. **项目管理模块**
   - 项目基本信息
   - 项目类型管理
   - 项目进度管理
   - 项目成本管理
   - 项目付款管理

3. **工时管理模块**
   - 工时记录
   - 工资数据
   - 工时统计

4. **小程序接口模块**
   - 小程序用户
   - 日志记录

### 3.2 核心数据表结构

#### 3.2.1 用户与权限相关表

1. **admin_user (用户表)**
   - id: 用户ID (主键)
   - username: 用户名
   - realname: 真实姓名
   - password_hash: 密码哈希
   - avatar: 头像路径
   - remark: 备注
   - enable: 是否启用
   - dept_id: 部门ID (外键)
   - create_at: 创建时间
   - update_at: 更新时间

2. **admin_role (角色表)**
   - id: 角色ID (主键)
   - name: 角色名称
   - code: 角色编码
   - remark: 备注
   - enable: 是否启用
   - create_at: 创建时间
   - update_at: 更新时间

3. **admin_power (权限表)**
   - id: 权限ID (主键)
   - name: 权限名称
   - type: 权限类型
   - code: 权限编码
   - url: 权限URL
   - open_type: 打开方式
   - parent_id: 父权限ID
   - icon: 图标
   - sort: 排序
   - create_time: 创建时间
   - enable: 是否启用

4. **admin_user_role (用户角色关联表)**
   - user_id: 用户ID (外键)
   - role_id: 角色ID (外键)

5. **admin_role_power (角色权限关联表)**
   - role_id: 角色ID (外键)
   - power_id: 权限ID (外键)

#### 3.2.2 项目管理相关表

1. **import_project (项目表)**
   - id: 项目ID (主键)
   - project_name: 项目名称
   - project_code: 项目编码
   - project_status: 项目状态
   - price: 项目价格
   - currency: 币种
   - machine_number: 机台数量
   - estimate_cost: 预估成本
   - bom_cost: BOM成本
   - mechanical_bom_cost: 机械BOM成本
   - electrical_bom_cost: 电气BOM成本
   - labor_cost: 人工成本
   - other_cost: 其他成本
   - dept_id: 项目类型ID (外键)
   - dept_ids: 参与部门ID列表
   - project_manager: 项目负责人
   - delivery_date: 计划交货时间
   - create_at: 创建时间
   - update_at: 更新时间

2. **project_manage_dept (项目类型表)**
   - id: 类型ID (主键)
   - dept_name: 类型名称
   - parent_id: 父类型ID
   - sort: 排序
   - status: 状态
   - leader: 负责人
   - phone: 联系电话
   - email: 电子邮箱
   - create_at: 创建时间
   - update_at: 更新时间

3. **project_progress (项目进度表)**
   - id: 进度ID (主键)
   - project_id: 项目ID (外键)
   - project_description: 项目业务简介
   - project_start_date: 项目开始日期
   - shipping_start_date: 发货开始日期
   - shipping_end_date: 发货完成日期
   - installation_start_date: 安装开始日期
   - installation_end_date: 安装完成日期
   - acceptance_date: 验收日期
   - created_at: 创建时间
   - updated_at: 更新时间

4. **project_payment (项目付款表)**
   - id: 付款ID (主键)
   - project_id: 项目ID (外键)
   - prepayment: 预付款金额
   - delivery_payment: 发货款金额
   - acceptance_payment: 验收款金额
   - warranty_payment: 质保金金额
   - actual_prepayment: 实际预付款回款金额
   - actual_delivery_payment: 实际发货款回款金额
   - actual_acceptance_payment: 实际验收款回款金额
   - actual_warranty_payment: 实际质保金回款金额
   - prepayment_date: 预付款付款日期
   - delivery_payment_date: 发货款付款日期
   - acceptance_payment_date: 验收款付款日期
   - warranty_payment_date: 质保金付款日期
   - created_at: 创建时间
   - updated_at: 更新时间

5. **payment_history (付款历史记录表)**
   - id: 记录ID (主键)
   - project_id: 项目ID (外键)
   - payment_id: 付款ID (外键)
   - payment_type: 付款类型
   - amount: 回款金额
   - payment_date: 付款日期
   - remark: 备注
   - created_at: 创建时间

#### 3.2.3 成本管理相关表

1. **project_actual_cost (项目实际成本表)**
   - id: 成本ID (主键)
   - project_id: 项目ID (外键)
   - year_month: 年月
   - total_cost: 总成本
   - bom_cost: BOM成本
   - labor_cost: 人工成本
   - other_cost: 其他成本
   - created_at: 创建时间
   - updated_at: 更新时间

2. **bom_cost_import (BOM成本导入记录表)**
   - id: 记录ID (主键)
   - project_id: 项目ID (外键)
   - year_month: 年月
   - amount: 金额
   - import_file: 导入文件名
   - import_by: 导入人ID (外键)
   - created_at: 创建时间

3. **project_labor_cost (项目人工成本表)**
   - id: 记录ID (主键)
   - project_id: 项目ID (外键)
   - work_month: 工作月份
   - salary_month: 工资发放月份
   - total_regular_hours: 总正常工时
   - total_overtime_hours: 总加班工时
   - total_hours: 总工时
   - total_cost: 总人工成本
   - cost_details: 成本详情
   - calculation_method: 计算方法
   - calculated_at: 计算时间

#### 3.2.4 工时与薪资相关表

1. **log_info (工时日志表)**
   - id: 日志ID (主键)
   - projectPrefix: 项目前缀
   - projectNumber: 项目编号
   - regularWorkingHours: 正常工作时间
   - overtimeWorkingHours: 加班工作时间
   - projectLocation: 项目地点
   - openid: 用户OpenID
   - content: 工作内容
   - totalHours: 总工时
   - created_at: 创建时间
   - status: 状态

2. **employee_salary (员工薪资表)**
   - id: 记录ID (主键)
   - employee_id: 员工ID (外键)
   - employee_number: 员工工号
   - year_month: 年月
   - base_salary: 基本工资
   - overtime_pay: 加班工资
   - should_pay: 应发工资
   - actual_pay: 实发工资
   - created_at: 创建时间
   - updated_at: 更新时间

### 3.3 数据库关系图

```
+---------------+       +---------------+       +---------------+
| admin_user    |       | admin_role    |       | admin_power   |
+---------------+       +---------------+       +---------------+
| id            |<---+  | id            |<---+  | id            |
| username      |    |  | name          |    |  | name          |
| password_hash |    |  | code          |    |  | code          |
| ...           |    |  | ...           |    |  | ...           |
+---------------+    |  +---------------+    |  +---------------+
       |             |         |             |         ^
       |             |         |             |         |
       v             |         v             |         |
+---------------+    |  +---------------+    |  +---------------+
| user_role     |----+  | role_power    |----+  | admin_dept    |
+---------------+       +---------------+       +---------------+
| user_id       |       | role_id       |       | id            |
| role_id       |       | power_id      |       | dept_name     |
+---------------+       +---------------+       | ...           |
                                                +---------------+
                                                      ^
                                                      |
+---------------+       +---------------+             |
| import_project|       | project_      |             |
+---------------+       | manage_dept   |-------------+
| id            |       +---------------+
| project_name  |       | id            |
| project_code  |       | dept_name     |
| dept_id       |------>| ...           |
| ...           |       +---------------+
+---------------+
       |
       |
       +-----------------+------------------+------------------+
       |                 |                  |                  |
       v                 v                  v                  v
+---------------+ +---------------+ +---------------+ +---------------+
| project_      | | project_      | | project_      | | project_labor_|
| progress      | | payment       | | actual_cost   | | cost          |
+---------------+ +---------------+ +---------------+ +---------------+
| id            | | id            | | id            | | id            |
| project_id    | | project_id    | | project_id    | | project_id    |
| ...           | | ...           | | ...           | | ...           |
+---------------+ +---------------+ +---------------+ +---------------+
                          |
                          |
                          v
                  +---------------+
                  | payment_      |
                  | history       |
                  +---------------+
                  | id            |
                  | project_id    |
                  | payment_id    |
                  | ...           |
                  +---------------+
```

### 3.4 数据库优化策略

1. **索引优化**
   - 为常用查询字段创建索引
   - 为外键关系创建索引
   - 使用复合索引优化多字段查询

2. **查询优化**
   - 使用延迟加载减少不必要的数据获取
   - 针对大数据量查询实现分页
   - 使用适当的连接类型优化关联查询

3. **数据库连接池**
   - 配置适当的连接池大小
   - 设置连接超时和回收策略
   - 监控连接池状态

4. **数据库备份策略**
   - 定期全量备份
   - 实时增量备份
   - 备份数据加密存储

<div style="page-break-after: always;"></div>

## 4. 功能模块

### 4.1 用户与权限管理模块

#### 4.1.1 功能概述

用户与权限管理模块负责系统的用户账户管理、角色权限配置和部门组织管理，是系统安全和访问控制的基础。该模块采用基于角色的访问控制（RBAC）模型，通过角色分配权限，再将角色分配给用户，实现灵活的权限管理。

#### 4.1.2 核心功能

1. **用户管理**
   - 用户创建、编辑和删除
   - 用户状态管理（启用/禁用）
   - 用户密码管理
   - 用户部门分配
   - 用户角色分配

2. **角色管理**
   - 角色创建、编辑和删除
   - 角色权限分配
   - 角色状态管理

3. **权限管理**
   - 权限创建、编辑和删除
   - 权限分类管理
   - 菜单权限配置
   - 操作权限配置

4. **部门管理**
   - 部门创建、编辑和删除
   - 部门层级结构管理
   - 部门负责人设置

5. **日志管理**
   - 操作日志记录
   - 登录日志记录
   - 日志查询和导出

#### 4.1.3 技术实现

1. **用户认证**
   - 使用Flask-Login实现用户认证
   - 密码加密存储
   - 会话管理和记住登录状态

2. **权限验证**
   - 基于装饰器的权限验证
   - 菜单权限动态加载
   - 按钮权限控制

3. **日志记录**
   - 使用装饰器自动记录操作日志
   - 记录操作类型、操作内容和操作结果
   - 记录操作用户和操作时间

### 4.2 项目管理模块

#### 4.2.1 功能概述

项目管理模块是系统的核心模块，负责项目的创建、分类和基本信息管理。该模块提供项目全生命周期的基础数据管理，为其他模块提供数据支持。

#### 4.2.2 核心功能

1. **项目基本信息管理**
   - 项目创建、编辑和删除
   - 项目编码和名称管理
   - 项目价格和币种设置
   - 项目状态管理
   - 项目负责人分配

2. **项目类型管理**
   - 项目类型创建、编辑和删除
   - 项目类型层级结构管理
   - 项目类型负责人设置

3. **项目导入导出**
   - Excel批量导入项目
   - 项目数据导出
   - 导入模板下载

4. **项目查询与筛选**
   - 多条件组合查询
   - 项目状态筛选
   - 项目类型筛选
   - 项目负责人筛选

#### 4.2.3 技术实现

1. **项目数据管理**
   - 使用SQLAlchemy ORM进行数据操作
   - 事务管理确保数据一致性
   - 数据验证和清洗

2. **Excel导入导出**
   - 使用pandas处理Excel文件
   - 数据验证和错误处理
   - 批量数据处理

3. **查询优化**
   - 动态SQL查询构建
   - 查询缓存优化
   - 分页查询实现

### 4.3 项目进度管理模块

#### 4.3.1 功能概述

项目进度管理模块负责跟踪和管理项目的执行进度，通过记录项目的关键时间节点，自动计算项目状态和进度百分比，提供直观的进度展示和异常预警。

#### 4.3.2 核心功能

1. **进度信息管理**
   - 项目开始日期设置
   - 发货开始/完成日期记录
   - 安装开始/完成日期记录
   - 验收日期记录

2. **进度状态自动计算**
   - 基于时间节点自动计算项目状态
   - 进度百分比自动计算
   - 状态变更历史记录

3. **进度可视化**
   - 项目进度时间线展示
   - 进度状态图表展示
   - 项目进度对比分析

4. **异常项目管理**
   - 进度异常项目识别
   - 异常项目预警
   - 异常原因记录和跟踪

#### 4.3.3 技术实现

1. **状态计算逻辑**
   - 基于日期字段的状态计算算法
   - 进度百分比计算公式
   - 状态变更触发机制

2. **可视化实现**
   - 使用ECharts实现图表展示
   - 使用Layui实现进度条展示
   - 使用自定义组件实现时间线展示

3. **异常处理机制**
   - 异常规则配置
   - 异常检测算法
   - 异常通知机制

### 4.4 项目成本管理模块

#### 4.4.1 功能概述

项目成本管理模块负责跟踪和管理项目的成本信息，包括预估成本和实际成本的记录、计算和分析，为项目成本控制提供数据支持。

#### 4.4.2 核心功能

1. **预估成本管理**
   - BOM成本设置
   - 机械BOM成本和电气BOM成本分别设置
   - 人工成本预估
   - 其他成本预估
   - 总预估成本自动计算

2. **实际成本记录**
   - BOM成本导入
   - 人工成本计算
   - 其他成本录入
   - 月度成本汇总

3. **成本分析**
   - 预估成本与实际成本对比
   - 成本构成分析
   - 成本趋势分析
   - 成本异常识别

4. **成本报表**
   - 项目成本明细报表
   - 成本汇总报表
   - 成本对比报表
   - 报表导出功能

#### 4.4.3 技术实现

1. **成本计算逻辑**
   - 基于工时和薪资的人工成本计算
   - 多币种成本换算
   - 成本汇总算法

2. **数据导入处理**
   - Excel文件解析
   - 数据验证和匹配
   - 批量数据处理

3. **分析图表实现**
   - 使用ECharts实现成本分析图表
   - 动态数据加载
   - 交互式图表配置

### 4.5 项目付款管理模块

#### 4.5.1 功能概述

项目付款管理模块负责跟踪和管理项目的付款计划和实际回款情况，包括不同阶段的付款记录、付款状态管理和付款提醒，为财务管理提供支持。

#### 4.5.2 核心功能

1. **付款计划管理**
   - 预付款设置
   - 发货款设置
   - 验收款设置
   - 质保金设置
   - 付款比例配置

2. **回款记录管理**
   - 实际回款金额记录
   - 回款日期记录
   - 回款备注记录
   - 回款历史查询

3. **付款状态管理**
   - 付款状态自动计算（未付款/部分付款/已付款）
   - 付款状态变更历史
   - 付款状态统计

4. **付款提醒与统计**
   - 未付款项目提醒
   - 付款到期提醒
   - 付款率统计
   - 回款金额统计

#### 4.5.3 技术实现

1. **付款状态计算**
   - 基于计划金额和实际金额的状态计算
   - 多阶段付款状态综合判断
   - 状态变更触发机制

2. **付款记录管理**
   - 历史记录存储
   - 记录关联关系
   - 数据一致性保证

3. **统计与可视化**
   - 使用聚合查询实现统计
   - 使用ECharts实现付款统计图表
   - 使用进度条展示付款率

### 4.6 报表与统计分析模块

#### 4.6.1 功能概述

报表与统计分析模块负责系统的数据汇总、统计和可视化展示，为管理决策提供数据支持。该模块整合各个业务模块的数据，生成多维度的统计报表和分析图表。

#### 4.6.2 核心功能

1. **项目概览统计**
   - 项目总数统计
   - 项目状态分布
   - 项目类型分布
   - 项目进度统计

2. **成本分析报表**
   - 成本构成分析
   - 成本趋势分析
   - 预估与实际成本对比
   - 成本异常项目分析

3. **付款情况报表**
   - 付款状态统计
   - 回款金额统计
   - 付款率分析
   - 未付款项目分析

4. **数据导出功能**
   - Excel格式导出
   - PDF格式导出
   - 自定义导出字段
   - 批量导出功能

#### 4.6.3 技术实现

1. **数据查询与处理**
   - 复杂SQL查询构建
   - 数据聚合和计算
   - 多表关联查询

2. **图表可视化**
   - 使用ECharts实现多种图表
   - 图表交互功能
   - 动态数据更新

3. **报表生成**
   - 使用pandas处理数据
   - Excel文件生成
   - PDF文件生成

### 4.7 小程序接口模块

#### 4.7.1 功能概述

小程序接口模块为微信小程序提供后端API支持，实现移动端的项目查询、工时记录和状态查看功能，提高系统的移动访问能力和数据录入便捷性。

#### 4.7.2 核心功能

1. **用户认证接口**
   - 微信登录认证
   - 用户信息获取
   - 用户绑定与注册

2. **项目信息接口**
   - 项目列表查询
   - 项目详情查询
   - 项目状态查询

3. **工时记录接口**
   - 工时提交
   - 工时查询
   - 工时统计

4. **日志管理接口**
   - 日志提交
   - 日志查询
   - 日志审核状态查询

#### 4.7.3 技术实现

1. **接口认证与安全**
   - 微信小程序登录机制
   - 接口签名验证
   - 数据加密传输

2. **数据交互格式**
   - JSON数据格式
   - 统一响应结构
   - 错误码规范

3. **接口性能优化**
   - 接口缓存策略
   - 数据分页加载
   - 请求频率控制

<div style="page-break-after: always;"></div>

## 5. 安全性设计

### 5.1 用户认证机制

#### 5.1.1 登录认证流程

GosunTec项目采用基于Flask-Login的用户认证机制，实现安全可靠的用户登录和会话管理。认证流程如下：

1. **用户登录请求**
   - 用户提交用户名、密码和验证码
   - 系统验证验证码是否正确
   - 系统验证用户名和密码

2. **登录验证处理**
   - 查询用户信息并验证密码哈希
   - 检查用户状态是否启用
   - 记录登录日志（成功/失败）

3. **会话管理**
   - 创建用户会话
   - 设置会话过期时间
   - 支持"记住我"功能

4. **权限加载**
   - 加载用户角色
   - 加载用户权限
   - 将权限存入会话

#### 5.1.2 密码安全策略

1. **密码存储**
   - 使用bcrypt算法进行密码哈希
   - 不存储明文密码
   - 使用随机盐值增强安全性

2. **密码策略**
   - 密码复杂度要求
   - 密码定期更换机制
   - 密码重试限制

3. **密码重置**
   - 安全的密码重置流程
   - 重置链接有效期限制
   - 重置操作日志记录

### 5.2 权限控制设计

#### 5.2.1 RBAC权限模型

GosunTec项目采用基于角色的访问控制（RBAC）模型，通过角色分配权限，再将角色分配给用户，实现灵活的权限管理。

1. **权限定义**
   - 菜单权限：控制菜单项的可见性
   - 操作权限：控制功能操作的执行权限
   - 数据权限：控制数据访问范围

2. **角色管理**
   - 预定义角色（系统管理员、项目管理员等）
   - 自定义角色创建
   - 角色权限分配

3. **用户授权**
   - 用户角色分配
   - 多角色支持
   - 角色继承关系

#### 5.2.2 权限验证机制

1. **装饰器验证**
   - 使用`@authorize`装饰器进行权限验证
   - 支持多级权限代码
   - 自动记录权限验证日志

2. **前端权限控制**
   - 菜单项动态显示/隐藏
   - 按钮权限控制
   - 页面元素权限控制

3. **API权限验证**
   - 接口权限验证
   - 请求参数验证
   - 操作权限检查

### 5.3 数据安全措施

#### 5.3.1 数据传输安全

1. **HTTPS加密传输**
   - 配置SSL证书
   - 强制HTTPS访问
   - 证书定期更新

2. **数据加密**
   - 敏感数据传输加密
   - API请求签名验证
   - 防止中间人攻击

3. **跨域安全**
   - CORS配置
   - 来源验证
   - 请求方法限制

#### 5.3.2 数据存储安全

1. **敏感数据加密**
   - 密码哈希存储
   - 敏感信息加密存储
   - 加密密钥管理

2. **数据备份**
   - 定期数据备份
   - 备份数据加密
   - 备份恢复机制

3. **数据访问控制**
   - 数据库访问权限控制
   - 最小权限原则
   - 数据库连接加密

### 5.4 防攻击措施

#### 5.4.1 常见攻击防护

1. **SQL注入防护**
   - 使用ORM框架参数化查询
   - 输入数据验证和过滤
   - 错误信息安全处理

2. **XSS攻击防护**
   - 输入数据过滤和转义
   - 内容安全策略（CSP）
   - 输出编码

3. **CSRF攻击防护**
   - CSRF令牌验证
   - 同源策略检查
   - 敏感操作二次验证

4. **请求伪造防护**
   - 请求来源验证
   - 请求频率限制
   - 异常请求监控

#### 5.4.2 安全监控与审计

1. **操作日志记录**
   - 用户操作日志
   - 系统关键事件日志
   - 安全事件日志

2. **异常监控**
   - 登录失败监控
   - 异常操作监控
   - 系统异常监控

3. **安全审计**
   - 定期安全审计
   - 权限使用审计
   - 数据访问审计

<div style="page-break-after: always;"></div>

## 6. 接口设计

### 6.1 API接口规范

#### 6.1.1 接口设计原则

GosunTec项目的API接口设计遵循以下原则：

1. **RESTful设计风格**
   - 使用HTTP方法表示操作类型（GET、POST、PUT、DELETE）
   - 使用URL表示资源
   - 使用HTTP状态码表示操作结果

2. **统一响应格式**
   - 所有接口返回统一的JSON格式
   - 包含状态码、消息和数据三部分
   - 错误信息明确且有意义

3. **版本控制**
   - 接口URL包含版本信息
   - 向后兼容性保证
   - 版本升级策略

4. **安全性考虑**
   - 接口权限验证
   - 数据验证和过滤
   - 敏感数据保护

#### 6.1.2 通用响应格式

```json
// 成功响应
{
  "success": true,
  "msg": "操作成功",
  "data": {
    // 具体数据
  }
}

// 失败响应
{
  "success": false,
  "msg": "操作失败的具体原因",
  "data": null
}

// 分页数据响应
{
  "success": true,
  "msg": "获取成功",
  "data": {
    "items": [
      // 数据项列表
    ],
    "total": 100,
    "page": 1,
    "limit": 10
  }
}
```

#### 6.1.3 错误码规范

| 错误码 | 说明 | 示例消息 |
| ------ | ---- | -------- |
| 200 | 成功 | 操作成功 |
| 400 | 请求参数错误 | 参数不完整或格式错误 |
| 401 | 未授权 | 请先登录 |
| 403 | 权限不足 | 没有操作权限 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 500 | 服务器内部错误 | 服务器处理请求出错 |

### 6.2 核心业务接口

#### 6.2.1 用户认证接口

1. **用户登录**
   - 请求方式：POST
   - 请求路径：/passport/login
   - 请求参数：
     ```json
     {
       "username": "用户名",
       "password": "密码",
       "captcha": "验证码",
       "remember-me": true
     }
     ```
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "登录成功",
       "data": null
     }
     ```

2. **获取验证码**
   - 请求方式：GET
   - 请求路径：/passport/getCaptcha
   - 响应数据：图片数据

3. **用户登出**
   - 请求方式：GET
   - 请求路径：/passport/logout
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "登出成功",
       "data": null
     }
     ```

#### 6.2.2 项目管理接口

1. **获取项目列表**
   - 请求方式：POST
   - 请求路径：/import_project/data
   - 请求参数：
     ```json
     {
       "page": 1,
       "limit": 10,
       "project_name": "项目名称",
       "project_code": "项目编码",
       "dept_id": 1
     }
     ```
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "获取成功",
       "data": {
         "items": [
           {
             "id": 1,
             "project_name": "项目名称",
             "project_code": "项目编码",
             "project_status": 1,
             "price": 100000,
             "currency": "CNY",
             "dept_id": 1,
             "dept_name": "项目类型名称"
           }
         ],
         "total": 100
       }
     }
     ```

2. **获取项目详情**
   - 请求方式：GET
   - 请求路径：/import_project/edit?projectId=1
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "获取成功",
       "data": {
         "id": 1,
         "project_name": "项目名称",
         "project_code": "项目编码",
         "project_status": 1,
         "price": 100000,
         "currency": "CNY",
         "dept_id": 1,
         "dept_name": "项目类型名称",
         "estimate_cost": 80000,
         "bom_cost": 50000,
         "labor_cost": 20000,
         "other_cost": 10000
       }
     }
     ```

3. **保存项目信息**
   - 请求方式：POST
   - 请求路径：/import_project/save
   - 请求参数：
     ```json
     {
       "project_name": "项目名称",
       "project_code": "项目编码",
       "dept_id": 1,
       "project_status": 0,
       "price": 100000,
       "currency": "CNY",
       "machine_number": 2,
       "delivery_date": "2023-12-31",
       "estimate_cost": 80000,
       "bom_cost": 50000,
       "labor_cost": 20000,
       "other_cost": 10000
     }
     ```
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "保存成功",
       "data": null
     }
     ```

#### 6.2.3 项目进度接口

1. **获取项目进度列表**
   - 请求方式：POST
   - 请求路径：/project_progress/data
   - 请求参数：
     ```json
     {
       "page": 1,
       "limit": 10,
       "project_name": "项目名称",
       "project_code": "项目编码",
       "project_status": 1
     }
     ```
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "获取成功",
       "data": {
         "items": [
           {
             "id": 1,
             "project_code": "项目编码",
             "project_name": "项目名称",
             "project_type": "项目类型",
             "status_name": "进行中",
             "project_status": 1,
             "delivery_date": "2023-12-31",
             "project_manager": "项目经理",
             "progress": 50
           }
         ],
         "total": 100
       }
     }
     ```

2. **获取项目进度详情**
   - 请求方式：GET
   - 请求路径：/project_progress/api/project/1
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "获取成功",
       "data": {
         "id": 1,
         "project_code": "项目编码",
         "project_name": "项目名称",
         "project_type_name": "项目类型",
         "status_name": "进行中",
         "status_value": 1,
         "price": 100000,
         "machine_number": 2,
         "delivery_date": "2023-12-31",
         "project_start_date": "2023-01-01",
         "shipping_start_date": "2023-02-01",
         "shipping_end_date": "2023-03-01",
         "installation_start_date": "2023-04-01",
         "installation_end_date": "2023-05-01",
         "acceptance_date": null
       }
     }
     ```

3. **更新项目进度**
   - 请求方式：POST
   - 请求路径：/project_progress/update
   - 请求参数：
     ```json
     {
       "project_id": 1,
       "project_start_date": "2023-01-01",
       "shipping_start_date": "2023-02-01",
       "shipping_end_date": "2023-03-01",
       "installation_start_date": "2023-04-01",
       "installation_end_date": "2023-05-01",
       "acceptance_date": "2023-06-01"
     }
     ```
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "更新成功",
       "data": null
     }
     ```

#### 6.2.4 项目成本接口

1. **获取项目成本列表**
   - 请求方式：POST
   - 请求路径：/project_cost/data
   - 请求参数：
     ```json
     {
       "page": 1,
       "limit": 10,
       "project_name": "项目名称",
       "project_code": "项目编码",
       "dept_id": 1
     }
     ```
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "获取成功",
       "data": {
         "items": [
           {
             "id": 1,
             "project_code": "项目编码",
             "project_name": "项目名称",
             "project_type": "项目类型",
             "status_name": "进行中",
             "estimate_cost": 80000,
             "actual_cost": 75000,
             "cost_ratio": 93.75,
             "bom_cost": 50000,
             "labor_cost": 20000,
             "other_cost": 5000
           }
         ],
         "total": 100
       }
     }
     ```

2. **获取项目成本详情**
   - 请求方式：GET
   - 请求路径：/project_cost/api/project/1
   - 响应数据：
     ```json
     {
       "success": true,
       "msg": "获取成功",
       "data": {
         "project": {
           "id": 1,
           "project_code": "项目编码",
           "project_name": "项目名称",
           "project_type_name": "项目类型",
           "status_name": "进行中",
           "estimate_cost": 80000,
           "bom_cost": 50000,
           "mechanical_bom_cost": 30000,
           "electrical_bom_cost": 20000,
           "labor_cost": 20000,
           "other_cost": 10000
         },
         "actual_costs": [
           {
             "year_month": "2023-01",
             "total_cost": 25000,
             "bom_cost": 15000,
             "labor_cost": 8000,
             "other_cost": 2000
           },
           {
             "year_month": "2023-02",
             "total_cost": 30000,
             "bom_cost": 20000,
             "labor_cost": 7000,
             "other_cost": 3000
           }
         ],
         "cost_summary": {
           "total_estimate": 80000,
           "total_actual": 55000,
           "cost_ratio": 68.75
         }
       }
     }
     ```

### 6.3 小程序接口

#### 6.3.1 用户认证接口

1. **微信登录**
   - 请求方式：POST
   - 请求路径：/applet/get_openid
   - 请求参数：
     ```json
     {
       "code": "微信登录code"
     }
     ```
   - 响应数据：
     ```json
     {
       "data": {
         "openid": "用户openid"
       },
       "status": 200
     }
     ```

2. **用户注册**
   - 请求方式：POST
   - 请求路径：/applet/register
   - 请求参数：
     ```json
     {
       "openid": "用户openid",
       "name": "用户姓名",
       "phone": "手机号码"
     }
     ```
   - 响应数据：
     ```json
     {
       "message": "注册成功",
       "status": 200
     }
     ```

#### 6.3.2 项目信息接口

1. **获取项目列表**
   - 请求方式：GET
   - 请求路径：/applet/get_projects
   - 响应数据：
     ```json
     {
       "data": [
         {
           "id": 1,
           "name": "项目类型名称",
           "numbers": ["项目编码1", "项目编码2"]
         }
       ],
       "status": 200
     }
     ```

2. **获取项目详情**
   - 请求方式：GET
   - 请求路径：/applet/get_project_info?project_code=项目编码
   - 响应数据：
     ```json
     {
       "data": {
         "id": 1,
         "project_name": "项目名称",
         "project_code": "项目编码",
         "project_status": 1,
         "status_name": "进行中",
         "project_manager": "项目经理"
       },
       "status": 200
     }
     ```

#### 6.3.3 工时记录接口

1. **提交工时记录**
   - 请求方式：POST
   - 请求路径：/applet/save_log
   - 请求参数：
     ```json
     {
       "projectPrefix": "项目前缀",
       "projectNumber": "项目编号",
       "regularWorkingHours": 8,
       "overtimeWorkingHours": 2,
       "projectLocation": "厂内",
       "openid": "用户openid",
       "content": "工作内容描述"
     }
     ```
   - 响应数据：
     ```json
     {
       "message": "提交成功",
       "status": 200
     }
     ```

2. **获取工时记录列表**
   - 请求方式：GET
   - 请求路径：/applet/get_log_list?openid=用户openid
   - 响应数据：
     ```json
     {
       "data": [
         {
           "id": 1,
           "projectPrefix": "项目前缀",
           "projectNumber": "项目编号",
           "regularWorkingHours": 8,
           "overtimeWorkingHours": 2,
           "totalHours": 10,
           "projectLocation": "厂内",
           "content": "工作内容描述",
           "created_at": "2023-01-01 09:00:00",
           "status": 1
         }
       ],
       "status": 200
     }
     ```

3. **获取工时记录详情**
   - 请求方式：GET
   - 请求路径：/applet/get_log/1
   - 响应数据：
     ```json
     {
       "data": {
         "id": 1,
         "projectPrefix": "项目前缀",
         "projectNumber": "项目编号",
         "regularWorkingHours": 8,
         "overtimeWorkingHours": 2,
         "totalHours": 10,
         "projectLocation": "厂内",
         "openid": "用户openid",
         "content": "工作内容描述",
         "created_at": "2023-01-01 09:00:00",
         "status": 1
       },
       "status": 200
     }
     ```

<div style="page-break-after: always;"></div>

## 7. 部署要求

### 7.1 服务器环境要求

#### 7.1.1 硬件要求

GosunTec项目的部署需要满足以下硬件要求：

1. **生产环境服务器**
   - **CPU**：至少4核心，推荐8核心
   - **内存**：至少8GB，推荐16GB
   - **存储**：至少100GB SSD，根据数据量增长可扩展
   - **网络**：千兆网卡，稳定的网络连接

2. **测试环境服务器**
   - **CPU**：至少2核心，推荐4核心
   - **内存**：至少4GB，推荐8GB
   - **存储**：至少50GB SSD
   - **网络**：稳定的网络连接

3. **开发环境**
   - **CPU**：至少2核心
   - **内存**：至少4GB
   - **存储**：至少20GB可用空间
   - **网络**：稳定的网络连接

#### 7.1.2 软件环境

1. **操作系统**
   - **推荐**：Ubuntu 20.04 LTS 或 CentOS 8
   - **最低要求**：支持Docker的Linux发行版

2. **容器环境**
   - Docker 20.10+
   - Docker Compose 2.0+

3. **数据库**
   - MySQL 8.0+
   - 或 MariaDB 10.5+

4. **Web服务器**
   - Nginx 1.18+

5. **Python环境**
   - Python 3.8+
   - pip 20.0+
   - virtualenv 或 venv

### 7.2 部署架构

#### 7.2.1 单机部署架构

适用于中小型部署场景，所有组件部署在同一台服务器上。

```
+----------------------------------+
|            服务器                 |
|  +------------+  +------------+  |
|  |   Nginx    |  |  Gunicorn  |  |
|  +------------+  +------------+  |
|         |              |         |
|         +------+-------+         |
|                |                 |
|        +---------------+         |
|        | Flask应用服务器 |         |
|        +---------------+         |
|                |                 |
|        +---------------+         |
|        |  MySQL数据库   |         |
|        +---------------+         |
+----------------------------------+
```

#### 7.2.2 分布式部署架构

适用于大型部署场景，各组件分布在不同服务器上。

```
+----------------------------------+
|         负载均衡服务器            |
|  +------------+                 |
|  |   Nginx    |                 |
|  +------------+                 |
+----------------------------------+
           |
           +------------+------------+
           |            |            |
+------------------+ +------------------+ +------------------+
|  应用服务器 1     | |  应用服务器 2     | |  应用服务器 3     |
|  +------------+  | |  +------------+  | |  +------------+  |
|  |  Gunicorn  |  | |  |  Gunicorn  |  | |  |  Gunicorn  |  |
|  +------------+  | |  +------------+  | |  +------------+  |
|  |Flask应用服务器| | |  |Flask应用服务器| | |  |Flask应用服务器| |
|  +------------+  | |  +------------+  | |  +------------+  |
+------------------+ +------------------+ +------------------+
           |            |            |
           +------------+------------+
                        |
                +------------------+
                |  数据库服务器     |
                |  +------------+  |
                |  |    MySQL   |  |
                |  +------------+  |
                +------------------+
```

### 7.3 安装与配置

#### 7.3.1 使用Docker部署

1. **准备Docker环境**
   ```bash
   # 安装Docker
   sudo apt-get update
   sudo apt-get install docker.io docker-compose

   # 启动Docker服务
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

2. **获取项目代码**
   ```bash
   git clone https://github.com/your-organization/gosuntec-project.git
   cd gosuntec-project
   ```

3. **配置环境变量**
   - 复制`.env.example`为`.env`
   - 修改`.env`文件中的配置参数

4. **构建和启动容器**
   ```bash
   docker-compose build
   docker-compose up -d
   ```

5. **初始化数据库**
   ```bash
   docker-compose exec app flask db init
   docker-compose exec app flask db migrate
   docker-compose exec app flask db upgrade
   docker-compose exec app flask admin init
   ```

#### 7.3.2 传统部署方式

1. **安装Python环境**
   ```bash
   sudo apt-get update
   sudo apt-get install python3 python3-pip python3-venv
   ```

2. **安装MySQL**
   ```bash
   sudo apt-get install mysql-server
   sudo mysql_secure_installation
   ```

3. **创建数据库**
   ```sql
   CREATE DATABASE gosuntec CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'gosuntec'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON gosuntec.* TO 'gosuntec'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **安装Nginx**
   ```bash
   sudo apt-get install nginx
   ```

5. **配置项目**
   ```bash
   # 克隆项目
   git clone https://github.com/your-organization/gosuntec-project.git
   cd gosuntec-project

   # 创建虚拟环境
   python3 -m venv venv
   source venv/bin/activate

   # 安装依赖
   pip install -r requirements.txt

   # 修改配置文件
   cp applications/config.py.example applications/config.py
   # 编辑配置文件，设置数据库连接等参数
   ```

6. **初始化数据库**
   ```bash
   flask db init
   flask db migrate
   flask db upgrade
   flask admin init
   ```

7. **配置Gunicorn**
   ```bash
   pip install gunicorn
   ```

8. **配置Nginx**
   ```nginx
   server {
       listen 80;
       server_name your_domain.com;

       location / {
           proxy_pass http://127.0.0.1:5000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

9. **启动服务**
   ```bash
   # 启动Gunicorn
   gunicorn -w 4 -b 127.0.0.1:5000 app:app

   # 启动Nginx
   sudo systemctl restart nginx
   ```

### 7.4 性能优化建议

#### 7.4.1 应用层优化

1. **数据库查询优化**
   - 为常用查询添加索引
   - 优化复杂查询
   - 使用查询缓存

2. **静态资源优化**
   - 启用静态资源缓存
   - 使用CDN加速静态资源
   - 压缩CSS和JavaScript文件

3. **应用缓存策略**
   - 使用Redis缓存频繁访问的数据
   - 实现页面片段缓存
   - 缓存API响应数据

#### 7.4.2 服务器优化

1. **Nginx优化**
   - 启用Gzip压缩
   - 配置适当的缓冲区大小
   - 优化工作进程数量

2. **Gunicorn优化**
   - 根据CPU核心数调整工作进程数
   - 配置适当的超时时间
   - 启用异步工作模式

3. **数据库优化**
   - 配置适当的缓冲池大小
   - 优化查询缓存
   - 定期维护和优化表

### 7.5 备份与恢复策略

#### 7.5.1 数据备份

1. **数据库备份**
   - 每日全量备份
   - 定时增量备份
   - 备份文件加密存储

2. **文件备份**
   - 定期备份上传文件
   - 备份配置文件
   - 备份日志文件

#### 7.5.2 恢复流程

1. **数据库恢复**
   - 恢复最近的全量备份
   - 应用增量备份
   - 验证数据完整性

2. **应用恢复**
   - 恢复代码和配置
   - 恢复上传文件
   - 重启应用服务

### 7.6 监控与维护

#### 7.6.1 系统监控

1. **服务器监控**
   - CPU、内存、磁盘使用率监控
   - 网络流量监控
   - 系统日志监控

2. **应用监控**
   - 请求响应时间监控
   - 错误率监控
   - 用户活动监控

3. **数据库监控**
   - 连接数监控
   - 查询性能监控
   - 存储空间监控

#### 7.6.2 日常维护

1. **定期更新**
   - 系统安全更新
   - 依赖包更新
   - 功能更新

2. **日志管理**
   - 日志轮转
   - 日志分析
   - 异常监控

3. **性能调优**
   - 定期性能评估
   - 瓶颈识别和解决
   - 容量规划

<div style="page-break-after: always;"></div>

## 附录

### 附录A：术语表

| 术语 | 定义 |
| ---- | ---- |
| BOM | Bill of Materials，物料清单，指项目所需的物料成本清单 |
| RBAC | Role-Based Access Control，基于角色的访问控制，一种安全模型 |
| ORM | Object-Relational Mapping，对象关系映射，用于数据库操作的编程技术 |
| API | Application Programming Interface，应用程序编程接口 |
| SSD | Solid State Drive，固态硬盘，一种存储设备 |
| CDN | Content Delivery Network，内容分发网络，用于加速静态资源访问 |
| CSRF | Cross-Site Request Forgery，跨站请求伪造，一种网络攻击方式 |
| XSS | Cross-Site Scripting，跨站脚本攻击，一种网络攻击方式 |
| SQL注入 | 一种通过在输入字段中注入SQL代码来攻击数据库的方法 |
| RESTful | Representational State Transfer，一种API设计风格 |

### 附录B：参考文档

1. Flask官方文档：https://flask.palletsprojects.com/
2. SQLAlchemy文档：https://docs.sqlalchemy.org/
3. Pear Admin Layui文档：http://www.pearadmin.com/
4. MySQL 8.0参考手册：https://dev.mysql.com/doc/refman/8.0/en/
5. Docker文档：https://docs.docker.com/
6. Nginx文档：https://nginx.org/en/docs/
7. Gunicorn文档：https://docs.gunicorn.org/
8. Python 3.8文档：https://docs.python.org/3.8/

### 附录C：版本历史

| 版本号 | 发布日期 | 主要变更 |
| ------ | -------- | -------- |
| V1.0.0 | 2023-01-15 | 初始版本发布 |
| V1.1.0 | 2023-03-20 | 添加项目进度管理模块 |
| V1.2.0 | 2023-05-10 | 添加项目成本管理模块 |
| V1.3.0 | 2023-07-25 | 添加项目付款管理模块 |
| V1.4.0 | 2023-09-15 | 添加小程序接口模块 |
| V2.0.0 | 2023-11-01 | 重构系统架构，优化性能 |

### 附录D：常见问题解答

#### D.1 系统安装问题

**Q: 安装过程中出现"ImportError"错误怎么办？**

A: 这通常是由于Python依赖包版本不匹配导致的。请确保严格按照`requirements.txt`中指定的版本安装依赖包。如果问题仍然存在，可以尝试在虚拟环境中重新安装所有依赖。

**Q: 数据库初始化失败怎么办？**

A: 请检查数据库连接配置是否正确，包括用户名、密码、主机和端口。确保数据库用户具有创建表的权限。如果使用MySQL 8.0+，可能需要调整认证插件设置。

#### D.2 使用问题

**Q: 如何添加新的用户角色？**

A: 在系统管理员账户下，进入"角色管理"页面，点击"添加"按钮，填写角色信息并分配权限，然后保存即可。

**Q: 如何修改项目状态计算规则？**

A: 项目状态计算规则定义在`applications/view/system/project_progress.py`文件的`calculate_project_status`函数中，可以根据需要修改该函数的逻辑。

**Q: 如何自定义成本计算方法？**

A: 成本计算方法定义在`applications/view/system/Project_cost.py`文件中，可以根据需要修改相关函数的计算逻辑。

#### D.3 性能问题

**Q: 系统在大量数据下响应缓慢怎么办？**

A: 可以从以下几个方面优化：
1. 优化数据库查询，添加适当的索引
2. 实现数据缓存，减少数据库访问
3. 优化前端代码，减少不必要的请求
4. 增加服务器资源，如CPU和内存

**Q: 如何优化大型报表的生成速度？**

A: 可以考虑以下优化方法：
1. 实现报表数据的异步生成和缓存
2. 优化SQL查询，避免复杂的连接和子查询
3. 实现分页加载，减少单次数据量
4. 对于定期报表，可以预先计算并存储结果

