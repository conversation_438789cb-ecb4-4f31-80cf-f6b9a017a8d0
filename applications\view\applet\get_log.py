from flask import Blueprint, jsonify
from applications.extensions import db
from applications.models import LogInfo  # 假设有一个 LogInfo 模型

bp = Blueprint('get_log', __name__)

@bp.route('/get_log/<int:log_id>', methods=['GET'])
def get_log(log_id):
    try:
        log = LogInfo.query.get(log_id)
        if log:
            return jsonify({'data': {
                'id': log.id,
                'projectPrefix': log.projectPrefix,
                'projectNumber': log.projectNumber,
                'regularWorkingHours': log.regularWorkingHours,
                'overtimeWorkingHours': log.overtimeWorkingHours,
                'projectLocation': log.projectLocation,
                'openid': log.openid,
                'content': log.content,
                'totalHours': log.totalHours,
                'created_at': log.created_at,
                'status': log.status
            }, 'status': 200})
        else:
            return jsonify({'message': '日志不存在', 'status': 404})
    except Exception as e:
        return jsonify({'message': f'获取日志信息失败: {str(e)}', 'status': 500})