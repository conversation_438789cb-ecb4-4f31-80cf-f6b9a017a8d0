import datetime
from applications.extensions import db


class PaymentHistory(db.Model):
    """项目付款历史记录表"""
    __tablename__ = 'payment_history'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='记录ID')
    project_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('import_project.id'), comment='项目ID')
    payment_id = db.Column(db.Integer, db.ForeignKey('project_payment.id'), comment='付款ID')
    payment_type = db.Column(db.String(50), comment='付款类型')  # prepayment, delivery_payment, acceptance_payment, warranty_payment
    amount = db.Column(db.Float, default=0, comment='回款金额')
    payment_date = db.Column(db.Date, comment='付款日期')
    remark = db.Column(db.String(255), comment='备注')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    
    # 关联项目表和付款表
    project = db.relationship('Import_project', backref='payment_history')
    payment = db.relationship('ProjectPayment', backref='payment_history')

    def __init__(self, **kwargs):
        super(PaymentHistory, self).__init__(**kwargs)
