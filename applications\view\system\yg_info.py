from flask import Blueprint, render_template, request
from flask_login import login_required, current_user
from sqlalchemy import desc
import pandas as pd
from io import BytesIO
import datetime
import re

from applications.common import curd
from applications.common.curd import enable_status, disable_status
from applications.common.utils.http import table_api, fail_api, success_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.models import Role, Dept, EmployeeSalary
from applications.models import ygong

bp = Blueprint('yg_info', __name__, url_prefix='/yg_info')






@bp.route('/')
@login_required
@authorize("system:YG:info", log=True)
def index():
    return render_template('system/YG/yg.html')

# 用户增加

@bp.get('/add')
# @authorize("system:user:add", log=True)
def add():
    roles = Role.query.all()
    return render_template('system/YG/add.html', roles=roles)


@bp.post('/save')
# @authorize("system:user:add", log=True)
def save():
    req_json = request.get_json(force=True)
    a = req_json.get("roleIds")
    name = str_escape(req_json.get('name'))
    employee_id = str_escape(req_json.get('employee_id'))
    phone = str_escape(req_json.get('phone'))
    dept_id = str_escape(req_json.get('deptId'))
    position = str_escape(req_json.get('position'))
    hire_date = str_escape(req_json.get('hire_date'))
    gender = str_escape(req_json.get('gender'))
    birth_date = str_escape(req_json.get('birth_date'))
    age = str_escape(req_json.get('age'))
    id_card = str_escape(req_json.get('id_card'))
    is_formal = str_escape(req_json.get('is_formal'))
    base_salary = str_escape(req_json.get('base_salary'))
    performance_salary = str_escape(req_json.get('performance_salary'))
    supervisor_assessment = str_escape(req_json.get('supervisor_assessment'))
    
    role_ids = a.split(',')
    
    if not name or not phone:
        return fail_api(msg="姓名和手机号不得为空")
     
    if len(phone) != 11:
        return fail_api(msg="请输入正确的手机号")
    
    if bool(ygong.query.filter_by(name=name).count()):
        return fail_api(msg="用户已经存在")

    user = ygong(
        name=name,
        employee_id=employee_id,
        phone=phone,
        enable=1,
        dept_id=dept_id,
        position=position,
        hire_date=hire_date,
        gender=gender,
        birth_date=birth_date,
        age=age,
        id_card=id_card,
        is_formal=is_formal,
        base_salary=base_salary,
        performance_salary=performance_salary,
        supervisor_assessment=supervisor_assessment
    )
    db.session.add(user)
    roles = Role.query.filter(Role.id.in_(role_ids)).all()
    for r in roles:
        user.role.append(r)

    db.session.commit()
    return success_api(msg="增加成功")

# 删除用户
@bp.delete('/remove/<int:id>')
# @authorize("system:user:remove", log=True)
def delete(id):
    user = ygong.query.filter_by(id=id).first()
    user.role = []

    res = ygong.query.filter_by(id=id).delete()
    db.session.commit()
    if not res:
        return fail_api(msg="删除失败")
    return success_api(msg="删除成功")


#  编辑用户
@bp.get('/edit/<int:id>')
# @authorize("system:YG:edit", log=True)
def edit(id):
    yg = curd.get_one_by_id(ygong, id)
    roles = Role.query.all()
    checked_roles = []
    for r in yg.role:
        checked_roles.append(r.id)
    return render_template('system/YG/edit.html', user=yg, roles=roles, checked_roles=checked_roles)

@bp.put('/update')
# @authorize("system:user:edit", log=True)
def update():
    req_json = request.get_json(force=True)
    a = str_escape(req_json.get("roleIds", ""))
    id = str_escape(req_json.get("id"))
    name = str_escape(req_json.get('name'))
    employee_id = str_escape(req_json.get('employee_id'))
    phone = str_escape(req_json.get('phone'))
    dept_id = str_escape(req_json.get('deptId'))
    position = str_escape(req_json.get('position'))
    hire_date = str_escape(req_json.get('hire_date'))
    gender = str_escape(req_json.get('gender'))
    birth_date = str_escape(req_json.get('birth_date'))
    age = str_escape(req_json.get('age'))
    id_card = str_escape(req_json.get('id_card'))
    is_formal = str_escape(req_json.get('is_formal'))
    base_salary = str_escape(req_json.get('base_salary'))
    performance_salary = str_escape(req_json.get('performance_salary'))
    supervisor_assessment = str_escape(req_json.get('supervisor_assessment'))
    
    role_ids = a.split(',') if a else []
    
    ygong.query.filter_by(id=id).update({
        'name': name,
        'employee_id': employee_id,
        'phone': phone,
        'dept_id': dept_id,
        'position': position,
        'hire_date': hire_date,
        'gender': gender,
        'birth_date': birth_date,
        'age': age,
        'id_card': id_card,
        'is_formal': is_formal,
        'base_salary': base_salary,
        'performance_salary': performance_salary,
        'supervisor_assessment': supervisor_assessment
    })
    
    u = ygong.query.filter_by(id=id).first()
    roles = Role.query.filter(Role.id.in_(role_ids)).all()
    u.role = roles

    db.session.commit()
    return success_api(msg="更新成功")


#   用户分页查询
@bp.get('/data')
# @authorize("system:user:main")
def data():
    # 获取请求参数
    name = str_escape(request.args.get('name', type=str))

    phone = str_escape(request.args.get('phone', type=str))
    dept_id = request.args.get('deptId', type=int)

    filters = []
    if name:
        filters.append(ygong.name.contains(name))
    if phone:
        filters.append(ygong.phone.contains(phone))
    if dept_id:
        filters.append(ygong.dept_id == dept_id)

    # print(*filters)
    query = db.session.query(
        ygong,
        Dept
    ).filter(*filters).outerjoin(Dept, ygong.dept_id == Dept.id).layui_paginate()

    return table_api(
        data=[{
            'id': user.id,
            'name': user.name,
            'phone': user.phone,
            'enable': user.enable,
            'create_at': user.create_at,
            'update_at': user.update_at,
            'dept_name': dept.dept_name if dept else None,
            'name': user.name,
            'employee_id': user.employee_id,
            'dept_id': dept.id,  # 使用部门ID
            'position': user.position,
            'hire_date': user.hire_date,
            'gender': user.gender,
            'birth_date': user.birth_date,
            'age': user.age,
            'id_card': user.id_card,
            'phone': user.phone,
            'is_formal': user.is_formal,
            'base_salary': user.base_salary,
            'performance_salary': user.performance_salary,
            'supervisor_assessment': user.supervisor_assessment,
            'enable': user.enable
        } for user, dept in query.items],
        count=query.total)






































# 启用用户
@bp.put('/enable')
# @authorize("system:user:edit", log=True)
def enable():
    try:
        print("Raw request data:", request.data)  # 打印原始请求数据
        req_json = request.get_json(force=True)
        print("Parsed JSON:", req_json)  # 打印解析后的JSON
        
        if not req_json:
            return fail_api(msg="请求数据为空")
            
        _id = req_json.get('id')
        if _id is None:
            return fail_api(msg="请求数据中缺少id字段，请检查请求格式")
            
        if not isinstance(_id, (int, str)):
            return fail_api(msg="ID格式不正确，必须是数字或字符串")
            
        _id = int(_id)  # 确保ID是整数
        if _id <= 0:
            return fail_api(msg="ID必须为正整数")
            
        res = enable_status(model=ygong, id=_id)
        if not res:
            return fail_api(msg="启用失败，用户可能不存在")
        return success_api(msg="启用成功")
    except ValueError:
        return fail_api(msg="ID必须是数字")
    except Exception as e:
        return fail_api(msg=f"启用出错: {str(e)}")


# 禁用用户
@bp.put('/disable')
# @authorize("system:user:edit", log=True)
def dis_enable():
    try:
        print("Raw request data:", request.data)  # 打印原始请求数据
        req_json = request.get_json(force=True)
        print("Parsed JSON:", req_json)  # 打印解析后的JSON
        _id = req_json.get('id')
        if not _id:
            return fail_api(msg="ID字段不存在")
        res = disable_status(model=ygong, id=_id)
        if not res:
            return fail_api(msg="出错啦")
        return success_api(msg="禁用成功")
    except Exception as e:
        return fail_api(msg=f"禁用出错: {str(e)}")


@bp.post('/import_excel')
def import_excel():
    try:
        if 'file' not in request.files:
            return fail_api(msg="请选择文件")
        
        file = request.files['file']
        if not file.filename.endswith(('.xlsx', '.xls')):
            return fail_api(msg="仅支持Excel文件")
        
        # 读取Excel文件，指定部门、手机号和身份证号码字段为字符串类型
        df = pd.read_excel(file, dtype={'部门': str, '电话号码': str, '身份证号码': str})
        
        # 在导入前添加验证
        for _, row in df.iterrows():
            # 检查部门字段是否为空
            if pd.isna(row['部门']) or not row['部门'].strip():
                return fail_api(msg="部门字段不能为空")
            
            # 清理部门字段
            dept_name = str(row['部门']).strip()
            
            # 验证部门
            dept = Dept.query.filter_by(dept_name=dept_name).first()
            if not dept:
                return fail_api(msg=f"部门 {dept_name} 不存在")
            
            # 验证手机号
            phone = re.sub(r'\D', '', str(row['电话号码']))  # 移除非数字字符
            if not re.match(r'^1[3-9]\d{9}$', phone):
                return fail_api(msg="手机号格式不正确，必须为11位数字且以1开头")
            
            # 验证身份证号码
            id_card = re.sub(r'\s', '', str(row['身份证号码']))  # 移除空格
            if not re.match(r'^\d{17}[\dXx]$', id_card):
                return fail_api(msg="身份证号码格式不正确，必须为18位数字且最后一位可以是X")
        
        # 处理导入数据
        for _, row in df.iterrows():
            # 清理部门字段
            dept_name = str(row['部门']).strip()
            
            # 获取部门ID
            dept = Dept.query.filter_by(dept_name=dept_name).first()
            if not dept:
                return fail_api(msg=f"部门 {dept_name} 不存在")
            
            # 处理手机号
            phone = re.sub(r'\D', '', str(row['电话号码']))
            
            # 处理身份证号码
            id_card = re.sub(r'\s', '', str(row['身份证号码']))
            
            # 处理出生日期
            birth_date_str = str(row['出生日期']).strip()
            try:
                # 尝试解析日期格式
                if len(birth_date_str) == 8 and birth_date_str.isdigit():  # 格式为19850501
                    birth_date = datetime.datetime.strptime(birth_date_str, '%Y%m%d').date()
                else:  # 其他格式（如2023-01-01）
                    birth_date = pd.to_datetime(birth_date_str).date()
            except Exception as e:
                return fail_api(msg=f"出生日期格式不正确: {birth_date_str}")
            
            # 计算年龄
            today = datetime.date.today()
            age = today.year - birth_date.year
            if (today.month, today.day) < (birth_date.month, birth_date.day):  # 如果生日还没到，年龄减1
                age -= 1
            
            # 检查是否已存在
            existing_user = ygong.query.filter_by(employee_id=row['工号']).first()
            if existing_user:
                # 更新现有员工数据
                existing_user.name = row['姓名']
                existing_user.phone = phone
                existing_user.dept_id = dept.id
                existing_user.position = row['职位']
                existing_user.hire_date = pd.to_datetime(row['入职日期']).date()
                existing_user.gender = row['性别']
                existing_user.birth_date = birth_date
                existing_user.age = age  # 更新年龄
                existing_user.id_card = id_card
                existing_user.is_formal = row['是否正式']
                existing_user.base_salary = row['基本工资']
                existing_user.performance_salary = row['绩效工资']
                existing_user.supervisor_assessment = row['主管考核项']
            else:
                # 创建新员工
                user = ygong(
                    name=row['姓名'],
                    employee_id=row['工号'],
                    dept_id=dept.id,
                    position=row['职位'],
                    hire_date=pd.to_datetime(row['入职日期']).date(),
                    gender=row['性别'],
                    birth_date=birth_date,
                    age=age,  # 使用计算出的年龄
                    id_card=id_card,
                    phone=phone,
                    is_formal=row['是否正式'],
                    base_salary=row['基本工资'],
                    performance_salary=row['绩效工资'],
                    supervisor_assessment=row['主管考核项'],
                    enable=1
                )
                db.session.add(user)
        
        db.session.commit()
        return success_api(msg="导入成功")
    
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"导入失败: {str(e)}")



