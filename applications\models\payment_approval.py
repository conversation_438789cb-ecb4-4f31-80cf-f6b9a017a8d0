import datetime
from applications.extensions import db


class PaymentApproval(db.Model):
    """项目回款审批表"""
    __tablename__ = 'payment_approval'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='审批ID')
    project_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON>('import_project.id'), comment='项目ID')
    payment_id = db.Column(db.Integer, db.ForeignKey('project_payment.id'), comment='付款ID')
    payment_type = db.Column(db.String(50), comment='付款类型')  # prepayment, delivery_payment, acceptance_payment, warranty_payment
    actual_amount = db.Column(db.Float, default=0, comment='实际回款金额')
    payment_date = db.Column(db.Date, comment='付款日期')
    remark = db.Column(db.String(255), comment='备注')
    is_accumulate = db.Column(db.<PERSON>, default=False, comment='是否累计回款')
    
    # 审批状态：0-待审批，1-已批准，2-已拒绝
    status = db.Column(db.Integer, default=0, comment='审批状态')
    
    # 审批人信息
    approver_id = db.Column(db.Integer, db.ForeignKey('admin_user.id'), nullable=True, comment='审批人ID')
    approval_time = db.Column(db.DateTime, nullable=True, comment='审批时间')
    approval_remark = db.Column(db.String(255), nullable=True, comment='审批备注')
    
    # 创建和更新时间
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 关联项目表、付款表和用户表
    project = db.relationship('Import_project', backref='payment_approvals')
    payment = db.relationship('ProjectPayment', backref='payment_approvals')
    approver = db.relationship('User', backref='approved_payments')
    
    def __init__(self, **kwargs):
        super(PaymentApproval, self).__init__(**kwargs)
