<!DOCTYPE html>
<html>
<head>
    <title>项目进度管理</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
    <style>
        /* 全局样式 */
        body.pear-container {
            background-color: #f5f7fa;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            width: 100%;
            overflow-x: hidden;
        }

        .pear-container {
            width: 100%;
            padding: 0;
            box-sizing: border-box;
        }

        /* 卡片样式优化 */
        .layui-card {
            margin-bottom: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            overflow: hidden;
        }

        .layui-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            transform: translateY(-5px);
        }

        .layui-card-body {
            padding: 20px;
        }

        .layui-card-header {
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* 页面标题样式 */
        .page-title {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            color: #333;
            padding: 10px 0;
            position: relative;
            display: inline-block;
        }

        .page-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background: #009688;
            border-radius: 3px;
        }

        /* 统计卡片样式 */
        .stat-card {
            text-align: center;
            padding: 20px 10px;
            position: relative;
            overflow: hidden;
            height: 180px; /* 固定高度 */
            min-height: 180px; /* 最小高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        /* 自定义的五等分列 */
        .layui-col-md2-4 {
            width: 20%;
        }

        .stat-card-total {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .stat-card-not-started {
            background: linear-gradient(135deg, #FF9800 0%, #FFEB3B 100%);
            color: white;
        }

        .stat-card-in-progress {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .stat-card-completed {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card-progress {
            background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
            color: white;
        }

        .stat-icon {
            margin-bottom: 15px;
        }

        .stat-icon i {
            font-size: 36px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 70px;
            height: 70px;
            line-height: 70px;
            display: inline-block;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin: 12px 0 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            margin-bottom: 5px;
        }

        .stat-trend {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 5px;
        }

        /* 进度环样式优化 */
        .progress-ring {
            position: relative;
            display: inline-block;
            width: 100px;
            height: 100px;
            text-align: center;
            margin: 0;
        }

        .progress-ring .circle {
            transform: rotate(-90deg);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            width: 100%;
            height: 100%;
        }

        .progress-ring .circle-bg {
            fill: none;
            stroke: rgba(255, 255, 255, 0.3);
            stroke-width: 8;
        }

        .progress-ring .circle-progress {
            fill: none;
            stroke: #ffffff;
            stroke-width: 8;
            stroke-linecap: round;
            stroke-dasharray: 314.159;
            stroke-dashoffset: calc(314.159 - (314.159 * var(--progress) / 100));
            transition: stroke-dashoffset 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .progress-ring .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 22px;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .progress-ring .progress-label {
            position: absolute;
            top: 70%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }

        /* 确保表格单元格内容可见 */
        .layui-table-cell {
            height: auto !important;
            line-height: 28px !important;
            padding: 6px 15px !important;
            position: relative !important;
            overflow: visible !important;
            text-overflow: clip !important;
            white-space: normal !important;
            box-sizing: border-box !important;
        }

        /* 确保表格内容居中 */
        .layui-table-view .layui-table td[align="center"] .layui-table-cell {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            height: 100% !important;
        }

        /* 添加浏览器兼容性样式 */
        @-webkit-keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        /* 项目状态基础样式 - 增加选择器优先级和!important标记 */
        .layui-table-view .layui-table .project-status,
        .project-status {
            display: inline-block !important;
            padding: 4px 10px !important;
            border-radius: 3px !important;
            font-size: 13px !important;
            font-weight: bold !important;
            color: #fff !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
            -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
            -moz-box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
            text-align: center !important;
            min-width: 80px !important;
            transition: all 0.3s ease !important;
            -webkit-transition: all 0.3s ease !important;
            -moz-transition: all 0.3s ease !important;
            text-shadow: 0 1px 1px rgba(0,0,0,0.3) !important;
            margin: 0 auto !important;
            line-height: 1.5 !important;
        }

        /* 鼠标悬停效果 */
        .layui-table-view .layui-table .project-status:hover,
        .project-status:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 3px 6px rgba(0,0,0,0.3) !important;
            -webkit-box-shadow: 0 3px 6px rgba(0,0,0,0.3) !important;
            -moz-box-shadow: 0 3px 6px rgba(0,0,0,0.3) !important;
        }
        /* 未开始 - 橙黄色 */
        .layui-table-view .layui-table .project-status.status-pending,
        .project-status.status-pending {
            background-color: #FF9800 !important;
            color: #fff !important;
            border-left: 3px solid #E65100 !important;
        }
        /* 生产中/生产完成 - 绿色 */
        .layui-table-view .layui-table .project-status.status-production,
        .project-status.status-production {
            background-color: #4CAF50 !important;
            color: #fff !important;
            border-left: 3px solid #1B5E20 !important;
        }
        /* 发货中/发货完成 - 蓝色 */
        .layui-table-view .layui-table .project-status.status-shipping,
        .project-status.status-shipping {
            background-color: #2196F3 !important;
            color: #fff !important;
            border-left: 3px solid #0D47A1 !important;
        }
        /* 安装中/安装完成 - 青色 */
        .layui-table-view .layui-table .project-status.status-installation,
        .project-status.status-installation {
            background-color: #00BCD4 !important;
            color: #fff !important;
            border-left: 3px solid #006064 !important;
        }
        /* 调试中/调试完成 - 紫色 */
        .layui-table-view .layui-table .project-status.status-debugging,
        .project-status.status-debugging {
            background-color: #673AB7 !important;
            color: #fff !important;
            border-left: 3px solid #311B92 !important;
        }
        /* 验收中/验收完成 - 粉色 */
        .layui-table-view .layui-table .project-status.status-acceptance,
        .project-status.status-acceptance {
            background-color: #E91E63 !important;
            color: #fff !important;
            border-left: 3px solid #880E4F !important;
        }
        /* 项目已完成 - 深灰色 */
        .layui-table-view .layui-table .project-status.status-completed,
        .project-status.status-completed {
            background-color: #455A64 !important;
            color: #fff !important;
            border-left: 3px solid #263238 !important;
        }

        .clickable {
            cursor: pointer;
            color: #1E9FFF;
            text-decoration: underline;
        }
        .clickable:hover {
            color: #01AAED;
        }

        /* 响应式设计 */
        @media screen and (max-width: 992px) {
            .layui-col-md2-4 {
                width: 50%;
            }

            .stat-card {
                padding: 15px 10px;
            }

            .stat-icon i {
                font-size: 32px;
                width: 60px;
                height: 60px;
                line-height: 60px;
            }

            .stat-number {
                font-size: 28px;
            }

            .stat-title {
                font-size: 14px;
            }

            .progress-ring {
                width: 100px;
                height: 100px;
            }

            .progress-ring .progress-text {
                font-size: 22px;
            }

            .progress-ring .progress-label {
                font-size: 14px;
            }
        }

        @media screen and (max-width: 576px) {
            .layui-col-md2-4 {
                width: 100%;
            }

            .stat-card {
                padding: 15px;
            }

            .stat-icon i {
                font-size: 36px;
                width: 70px;
                height: 70px;
                line-height: 70px;
            }

            .stat-number {
                font-size: 32px;
            }

            .progress-ring {
                width: 120px;
                height: 120px;
            }

            .progress-ring .progress-text {
                font-size: 24px;
            }
        }
    </style>
</head>
<body class="pear-container">
    <!-- 页面标题和操作按钮 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <h2 class="page-title">项目进度管理</h2>
                </div>
                <div class="layui-col-md6" style="text-align: right;">
                    <button class="layui-btn layui-btn-normal" id="update-all-status-btn">
                        <i class="layui-icon layui-icon-refresh-3"></i> 更新所有项目状态
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="layui-row layui-col-space15" style="width: 100%; margin: 0; clear: both;">
        <!-- 总项目数 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-total">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-app"></i>
                    </div>
                    <div class="stat-title">总项目数</div>
                    <div class="stat-number" id="total-projects">--</div>
                    <div class="stat-trend" id="total-growth">较上月 --</div>
                </div>
            </div>
        </div>

        <!-- 未开始项目 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-not-started">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-time"></i>
                    </div>
                    <div class="stat-title">未开始项目</div>
                    <div class="stat-number" id="not-started-projects">--</div>
                    <div class="stat-trend" id="not-started-growth">较上月 --</div>
                </div>
            </div>
        </div>

        <!-- 进行中项目 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-in-progress">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-loading"></i>
                    </div>
                    <div class="stat-title">进行中项目</div>
                    <div class="stat-number" id="in-progress-projects">--</div>
                    <div class="stat-trend" id="in-progress-growth">较上月 --</div>
                </div>
            </div>
        </div>

        <!-- 已完成项目 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-completed">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-ok-circle"></i>
                    </div>
                    <div class="stat-title">已完成项目</div>
                    <div class="stat-number" id="completed-projects">--</div>
                    <div class="stat-trend" id="completed-growth">较上月 --</div>
                </div>
            </div>
        </div>

        <!-- 完成率 -->
        <div class="layui-col-md2-4 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-progress">
                    <div class="progress-ring">
                        <svg class="circle" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                            <circle class="circle-bg" cx="50" cy="50" r="42"></circle>
                            <circle class="circle-progress" cx="50" cy="50" r="42" style="--progress: 0"></circle>
                        </svg>
                        <div class="progress-text" id="completion-rate">0%</div>
                        <div class="progress-label">完成率</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目列表区域 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 新的筛选栏 -->
            <div class="layui-form" style="margin-bottom: 10px; display: flex; align-items: center;">
                <div style="display: flex; flex: 1; align-items: center;">
                    <div style="margin-right: 10px;">
                        <select id="status-filter" name="status" lay-filter="status" style="width: 120px;">
                            <option value="">项目状态</option>
                            <option value="0">未开始</option>
                            <option value="1">生产中</option>
                       <!--      <option value="2">生产完成</option> -->
                            <option value="3">发货中</option>
                            <option value="4">发货完成</option>
                            <option value="5">安装调试中</option>
                            <option value="6">安装调试完成</option>
                 <!--            <option value="7">调试中</option>
                            <option value="8">调试完成</option> -->
                            <option value="9">验收中</option>
                         <!--    <option value="10">验收完成</option> -->
                            <option value="11">项目已完成</option>
                        </select>
                    </div>
                    <div style="margin-right: 10px;">
                        <select id="category-filter" name="category" lay-filter="category" style="width: 120px;">
                            <option value="">项目类别</option>
                            <option value="2">GS</option>
                            <option value="3">GSCC</option>
                            <option value="4">GSBJ</option>
                            <option value="5">GSSZ</option>
                            <option value="6">会议</option>
                            <option value="7">其他</option>
                        </select>
                    </div>
                    <div style="margin-right: 10px; flex: 1;">
                        <input type="text" id="search-input" placeholder="搜索项目名称/编号" class="layui-input">
                    </div>
                    <div>
                        <button class="layui-btn" id="search-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <button class="layui-btn layui-btn-primary" id="reset-btn">
                            <i class="layui-icon layui-icon-refresh"></i> 重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 项目表格 -->
            <table id="project-table" lay-filter="project-table"></table>
        </div>
    </div>


</body>


<script src="{{ url_for('static', filename='index/js/echarts.min.js') }}"></script>
<script>
    layui.use(['table', 'layer', 'jquery', 'form'], function() {
        let table = layui.table;
        let layer = layui.layer;
        let $ = layui.jquery;
        let form = layui.form;

        // 加载项目统计数据
        function loadStatistics() {
            $.ajax({
                url: '/system/project_progress/statistics',
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        let data = response.data;

                        // 更新总项目数
                        $('#total-projects').text(data.total_projects);
                        $('#total-growth').text('较上月 ' + (data.total_growth >= 0 ? '+' : '') + data.total_growth + '%');
                        $('#total-growth').removeClass('up down').addClass(data.total_growth >= 0 ? 'up' : 'down');

                        // 更新未开始项目数
                        $('#not-started-projects').text(data.not_started_projects);
                        $('#not-started-growth').text('较上月 ' + (data.not_started_growth >= 0 ? '+' : '') + data.not_started_growth + '%');
                        $('#not-started-growth').removeClass('up down').addClass(data.not_started_growth >= 0 ? 'up' : 'down');

                        // 更新进行中项目数
                        $('#in-progress-projects').text(data.in_progress_projects);
                        $('#in-progress-growth').text('较上月 ' + (data.in_progress_growth >= 0 ? '+' : '') + data.in_progress_growth + '%');
                        $('#in-progress-growth').removeClass('up down').addClass(data.in_progress_growth >= 0 ? 'up' : 'down');

                        // 更新已完成项目数
                        $('#completed-projects').text(data.completed_projects);
                        $('#completed-growth').text('较上月 ' + (data.completed_growth >= 0 ? '+' : '') + data.completed_growth + '%');
                        $('#completed-growth').removeClass('up down').addClass(data.completed_growth >= 0 ? 'up' : 'down');

                        // 更新完成率
                        $('#completion-rate').text(data.completion_rate + '%');
                        $('.circle-progress').css('--progress', data.completion_rate);
                    } else {
                        layer.msg('获取统计数据失败: ' + response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('获取统计数据失败', {icon: 2});
                }
            });
        }

        // 在页面加载时调用统计函数
        loadStatistics();

        // 渲染表单元素
        form.render();

        // 更新所有项目状态按钮点击事件
        $('#update-all-status-btn').on('click', function() {
            layer.confirm('确定要更新所有项目的状态吗？', {icon: 3, title: '提示'}, function(index) {
                layer.close(index);
                layer.msg('正在更新所有项目状态，请稍候...', {icon: 16, time: 0});

                // 发送更新请求
                $.ajax({
                    url: '/system/project_progress/api/update_all_project_status',
                    type: 'get',
                    success: function(result) {
                        layer.closeAll('loading');
                        if (result.success) {
                            layer.msg(result.message, {icon: 1, time: 2000}, function() {
                                // 刷新表格
                                table.reload('project-table');
                                // 刷新统计数据
                                loadStatistics();
                            });
                        } else {
                            layer.msg(result.message, {icon: 2, time: 2000});
                        }
                    },
                    error: function() {
                        layer.closeAll('loading');
                        layer.msg('更新失败，请稍后重试', {icon: 2, time: 2000});
                    }
                });
            });
        });

        // 初始化项目表格
        table.render({
            elem: '#project-table',
            url: '/system/project_progress/data',  // 这里需要后端提供数据接口
            page: true,
            where: {
                keyword: '',
                status: ''
            },
            done: function(res) {
                // 表格加载完成后进行前端过滤
                filterTableData();
            },
            cols: [[
                {title: '项目类型', field: 'project_type', align: 'center'},
                {title: '项目编号', field: 'project_code', align: 'center'},
                {title: '项目名称', field: 'project_name', align: 'center'},
                {title: '计划交付日期', field: 'delivery_date', align: 'center'},
                {title: '项目负责人', field: 'project_manager', align: 'center', templet: function(d) {
                    return '<span class="clickable project-manager" data-id="' + d.id + '" lay-event="manager">' + (d.project_manager || '--') + '</span>';
                }},
                {title: '项目状态', field: 'status_name', align: 'center', templet: function(d) {
                    // 状态样式映射
                    const statusClassMap = {
                        '未开始': 'status-pending',
                        '生产中': 'status-production',
                        '生产完成': 'status-production',
                        '发货中': 'status-shipping',
                        '发货完成': 'status-shipping',
                        '安装中': 'status-installation',
                        '安装完成': 'status-installation',
                        '调试中': 'status-debugging',
                        '调试完成': 'status-debugging',
                        '验收中': 'status-acceptance',
                        '验收完成': 'status-acceptance',
                        '项目已完成': 'status-completed'
                    };

                    // 状态背景颜色映射
                    const statusBgColorMap = {
                        '未开始': '#FF9800',
                        '生产中': '#4CAF50',
                        '生产完成': '#4CAF50',
                        '发货中': '#2196F3',
                        '发货完成': '#2196F3',
                        '安装中': '#00BCD4',
                        '安装完成': '#00BCD4',
                        '调试中': '#673AB7',
                        '调试完成': '#673AB7',
                        '验收中': '#E91E63',
                        '验收完成': '#E91E63',
                        '项目已完成': '#455A64'
                    };

                    // 状态边框颜色映射
                    const statusBorderColorMap = {
                        '未开始': '#E65100',
                        '生产中': '#1B5E20',
                        '生产完成': '#1B5E20',
                        '发货中': '#0D47A1',
                        '发货完成': '#0D47A1',
                        '安装中': '#006064',
                        '安装完成': '#006064',
                        '调试中': '#311B92',
                        '调试完成': '#311B92',
                        '验收中': '#880E4F',
                        '验收完成': '#880E4F',
                        '项目已完成': '#263238'
                    };

                    const statusClass = statusClassMap[d.status_name] || '';
                    const bgColor = statusBgColorMap[d.status_name] || '#1E9FFF';
                    const borderColor = statusBorderColorMap[d.status_name] || '#0D47A1';

                    // 使用内联样式确保样式被正确应用
                    return '<span class="project-status ' + statusClass + '" ' +
                           'style="display:inline-block !important; padding:4px 10px !important; border-radius:3px !important; ' +
                           'font-size:13px !important; font-weight:bold !important; color:#fff !important; ' +
                           'background-color:' + bgColor + ' !important; border-left:3px solid ' + borderColor + ' !important; ' +
                           'box-shadow:0 1px 3px rgba(0,0,0,0.2) !important; text-align:center !important; ' +
                           'min-width:80px !important; text-shadow:0 1px 1px rgba(0,0,0,0.3) !important; ' +
                           'margin:0 auto !important; line-height:1.5 !important;">' +
                           d.status_name + '</span>';
                }},
                {title: '进度', field: 'progress', align: 'center', templet: function(d) {
                    return '<div class="layui-progress" lay-showpercent="true">' +
                           '<div class="layui-progress-bar" lay-percent="' + d.progress + '%" style="width: ' + d.progress + '%;"></div>' +
                           '</div>' +
                           '<div style="text-align: center; margin-top: 5px;">' + d.progress + '%</div>';
                }},
                {title: '操作', toolbar: '#project-bar', align: 'center', width: 180}
            ]],
            skin: 'line',
            text: {none: '暂无项目信息'},
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, {layEvent: 'update_all_status', icon: 'layui-icon-refresh-3', title: '更新所有项目状态'}, 'filter', 'print', 'exports']
        });

        // 模拟数据（实际应从后端获取）
        let mockData = [];

        // 项目负责人点击事件已移至表格工具条事件处理函数中

        // 添加项目按钮点击事件
        $('#add-project-btn').on('click', function() {
            layer.open({
                type: 2,
                title: '添加项目',
                shade: 0.1,
                area: ['500px', '500px'],
                content: '/system/project_progress/add'
            });
        });

        // 导出按钮点击事件
        $('#export-btn').on('click', function() {
            layer.msg('正在导出数据，请稍候...', {icon: 16, time: 2000}, function() {
                layer.msg('导出成功', {icon: 1, time: 2000});
            });
        });

        // 页面加载时初始化

        // 搜索按钮点击事件
        $('#search-btn').on('click', function() {
            doSearch();
        });

        // 重置按钮点击事件
        $('#reset-btn').on('click', function() {
            // 重置所有筛选条件
            $('#status-filter').val('');
            $('#category-filter').val('');
            $('#search-input').val('');
            form.render('select'); // 重新渲染select

            // 重置自定义筛选条件
            customFilters.statusValue = '';
            customFilters.deptId = '';

            // 重新加载表格数据
            table.reload('project-table', {
                where: {
                    keyword: '',
                    status: ''
                },
                page: {
                    curr: 1
                },
                done: function() {
                    // 表格加载完成后显示所有行
                    $('.layui-table-body tr').show();
                }
            });
        });

        // 监听筛选条件变化
        form.on('select', function(data) {
            // 当下拉框值变化时，不自动搜索，等用户点击搜索按钮
        });

        // 全局变量，用于存储自定义筛选条件
        let customFilters = {
            statusValue: '',
            deptId: ''
        };

        // 执行搜索函数
        function doSearch() {
            let searchKeyword = $('#search-input').val();
            let statusValue = $('#status-filter').val();
            let deptId = $('#category-filter').val();

            // 保存自定义筛选条件
            customFilters.statusValue = statusValue;
            customFilters.deptId = deptId;

            // 直接使用状态值进行筛选，后端已经修改为支持按计算后的状态筛选
            let statusParam = statusValue;

            // 项目类型将在表格加载完成后进行前端过滤
            table.reload('project-table', {
                where: {
                    keyword: searchKeyword,
                    status: statusParam
                },
                page: {
                    curr: 1
                },
                done: function(res) {
                    // 表格加载完成后进行前端过滤（仅针对项目类型）
                    filterTableData();
                }
            });
        }

        // 前端过滤表格数据
        function filterTableData() {
            // 获取表格中的所有行
            let tableBody = $('.layui-table-body');
            let tableRows = tableBody.find('tr');

            // 如果没有项目类型筛选条件，则显示所有行
            if (!customFilters.deptId) {
                tableRows.show();
                return;
            }

            // 添加调试输出
            console.log('开始筛选，条件：', customFilters);
            console.log('总行数：', tableRows.length);

            // 遍历表格行
            tableRows.each(function() {
                let row = $(this);
                let showRow = true;

                // 项目类型筛选
                if (customFilters.deptId) {
                    // 直接从表格中获取项目类型信息
                    let projectTypeCell = row.find('td[data-field="project_type"]');
                    let projectType = projectTypeCell.text().trim();

                    // 获取选中的部门名称
                    let deptText = $('#category-filter option[value="' + customFilters.deptId + '"]').text();
                    console.log('部门名称：', deptText, '项目类型：', projectType);

                    // 检查项目类型是否匹配
                    if (projectType !== deptText) {
                        showRow = false;
                    }
                }

                // 显示或隐藏行
                if (showRow) {
                    row.show();
                } else {
                    row.hide();
                }
            });

            // 添加筛选结果统计
            let visibleRows = $('.layui-table-body tr:visible').length;
            console.log('筛选后可见行数：', visibleRows);

            // 如果没有可见行，显示提示信息
            if (visibleRows === 0) {
                layer.msg('没有符合条件的项目', {icon: 0});
            }
        }

        // 回车键触发搜索
        $('#search-input').on('keypress', function(e) {
            if(e.which === 13) {
                doSearch();
            }
        });

        // 监听表格头部工具栏事件
        table.on('toolbar(project-table)', function(obj) {
            let layEvent = obj.event;

            if (layEvent === 'refresh') {
                // 刷新表格
                table.reload('project-table');
            } else if (layEvent === 'update_all_status') {
                // 更新所有项目状态
                layer.confirm('确定要更新所有项目的状态吗？', {icon: 3, title: '提示'}, function(index) {
                    layer.close(index);
                    layer.msg('正在更新所有项目状态，请稍候...', {icon: 16, time: 0});

                    // 发送更新请求
                    $.ajax({
                        url: '/system/project_progress/api/update_all_project_status',
                        type: 'get',
                        success: function(result) {
                            layer.closeAll('loading');
                            if (result.success) {
                                layer.msg(result.message, {icon: 1, time: 2000}, function() {
                                    // 刷新表格
                                    table.reload('project-table');
                                });
                            } else {
                                layer.msg(result.message, {icon: 2, time: 2000});
                            }
                        },
                        error: function() {
                            layer.closeAll('loading');
                            layer.msg('更新失败，请稍后重试', {icon: 2, time: 2000});
                        }
                    });
                });
            }
        });

        // 监听表格行工具条事件
        table.on('tool(project-table)', function(obj) {
            let data = obj.data;
            let layEvent = obj.event;

            if (layEvent === 'detail') {
                // 查看详情
                layer.open({
                    type: 2,
                    title: '项目进度详情',
                    shade: 0.1,
                    area: ['90%', '90%'],
                    content: '/system/project_progress/detail/' + data.id
                });
            } else if (layEvent === 'edit') {
                // 编辑项目
                layer.open({
                    type: 2,
                    title: '编辑项目',
                    shade: 0.1,
                    area: ['500px', '500px'],
                    content: '/system/import_project/edit?id=' + data.id
                });
            } else if (layEvent === 'del') {
                // 删除项目
                layer.confirm('确定要删除该项目吗？', {icon: 3, title: '提示'}, function(index) {
                    layer.close(index);
                    // 发送删除请求
                    $.ajax({
                        url: '/system/import_project/remove',
                        data: {
                            id: data.id
                        },
                        type: 'post',
                        success: function(result) {
                            if (result.success) {
                                layer.msg(result.msg, {icon: 1, time: 1000}, function() {
                                    obj.del();
                                });
                            } else {
                                layer.msg(result.msg, {icon: 2, time: 1000});
                            }
                        }
                    });
                });
            } else if (layEvent === 'manager') {
                // 项目负责人管理
                let projectId = data.id;
                let currentManager = data.project_manager || '--';

                // 打开项目负责人编辑弹窗
                layer.open({
                    type: 1,
                    title: '项目负责人管理',
                    area: ['700px', '500px'],
                    content: `
                        <div style="padding: 20px;">
                            <div class="layui-form" lay-filter="managerForm">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">当前负责人</label>
                                    <div class="layui-input-block">
                                        <input type="text" id="currentManager" class="layui-input" readonly value="${currentManager}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">搜索员工</label>
                                    <div class="layui-input-block">
                                        <input type="text" id="employeeSearch" class="layui-input" placeholder="输入姓名搜索">
                                        <button type="button" class="layui-btn layui-btn-primary" id="searchBtn" style="margin-top: 10px;">搜索</button>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">员工列表</label>
                                    <div class="layui-input-block">
                                        <table class="layui-table" id="employeeTable">
                                            <thead>
                                                <tr>
                                                    <th>姓名</th>
                                                    <th>工号</th>
                                                    <th>职位</th>
                                                    <th>部门</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="employeeList">
                                                <!-- 员工列表将通过AJAX加载 -->
                                            </tbody>
                                        </table>
                                        <!-- 分页控制器 -->
                                        <div id="employeePagination" style="text-align: center; margin-top: 10px;"></div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn layui-btn-primary" id="viewHistoryBtn">查看变更历史</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `,
                    success: function(layero, index) {
                        // 加载员工列表
                        loadEmployees('');

                        // 搜索按钮点击事件
                        $('#searchBtn').on('click', function() {
                            let keyword = $('#employeeSearch').val();
                            loadEmployees(keyword);
                        });

                        // 查看历史按钮点击事件
                        $('#viewHistoryBtn').on('click', function() {
                            // 加载历史记录
                            $.ajax({
                                url: '/system/project_progress/api/project/' + projectId + '/manager_logs_detailed',
                                success: function(response) {
                                    if (response.success) {
                                        let logs = response.data;
                                        let historyHtml = '';

                                        if (logs.length === 0) {
                                            historyHtml = '<div class="layui-none">暂无变更记录</div>';
                                        } else {
                                            historyHtml = '<table class="layui-table">';
                                            historyHtml += '<thead><tr><th>操作时间</th><th>操作人</th><th>操作内容</th></tr></thead>';
                                            historyHtml += '<tbody>';

                                            logs.forEach(function(log) {
                                                historyHtml += '<tr>';
                                                historyHtml += '<td>' + log.operation_time + '</td>';
                                                historyHtml += '<td>' + log.operator + '</td>';
                                                historyHtml += '<td>' + log.description + '</td>';
                                                historyHtml += '</tr>';
                                            });

                                            historyHtml += '</tbody></table>';
                                        }

                                        // 打开历史记录弹窗
                                        layer.open({
                                            type: 1,
                                            title: '项目负责人变更历史',
                                            area: ['800px', '500px'],
                                            content: '<div style="padding: 20px;">' + historyHtml + '</div>'
                                        });
                                    } else {
                                        layer.msg('获取历史记录失败: ' + response.message, {icon: 2});
                                    }
                                },
                                error: function() {
                                    layer.msg('获取历史记录失败', {icon: 2});
                                }
                            });
                        });

                        // 加载员工列表函数
                        function loadEmployees(keyword) {
                            $.ajax({
                                url: '/system/project_progress/api/employees',
                                data: { keyword: keyword },
                                success: function(response) {
                                    if (response.success) {
                                        let employees = response.data;

                                        // 分页变量
                                        let pageSize = 10; // 每页显示10条
                                        let currentPage = 1; // 当前页码
                                        let totalPages = Math.ceil(employees.length / pageSize); // 总页数

                                        // 渲染员工列表函数
                                        function renderEmployeeList(page) {
                                            let html = '';

                                            if (employees.length === 0) {
                                                html = '<tr><td colspan="5" class="layui-none">暂无员工数据</td></tr>';
                                            } else {
                                                // 计算当前页的数据范围
                                                let start = (page - 1) * pageSize;
                                                let end = Math.min(start + pageSize, employees.length);

                                                // 只渲染当前页的数据
                                                for (let i = start; i < end; i++) {
                                                    let emp = employees[i];
                                                    html += '<tr>';
                                                    html += '<td>' + emp.name + '</td>';
                                                    html += '<td>' + (emp.employee_id || '--') + '</td>';
                                                    html += '<td>' + (emp.position || '--') + '</td>';
                                                    html += '<td>' + (emp.dept_name || '--') + '</td>';
                                                    html += '<td><button class="layui-btn layui-btn-xs select-employee" data-name="' + emp.name + '">选择</button></td>';
                                                    html += '</tr>';
                                                }
                                            }

                                            $('#employeeList').html(html);

                                            // 更新分页控制器
                                            updatePagination(page, totalPages);

                                            // 选择员工按钮点击事件
                                            $('.select-employee').on('click', function() {
                                                let newManager = $(this).data('name');

                                                // 提交更新请求
                                                $.ajax({
                                                    url: '/system/project_progress/api/project/' + projectId + '/update_manager_with_log',
                                                    type: 'POST',
                                                    contentType: 'application/json',
                                                    data: JSON.stringify({manager: newManager}),
                                                    success: function(response) {
                                                        if (response.success) {
                                                            layer.msg('更新成功', {icon: 1});
                                                            layer.close(index);

                                                            // 重新加载项目表格
                                                            table.reload('project-table');
                                                        } else {
                                                            layer.msg('更新失败: ' + response.message, {icon: 2});
                                                        }
                                                    },
                                                    error: function() {
                                                        layer.msg('更新失败', {icon: 2});
                                                    }
                                                });
                                            });
                                        }

                                        // 更新分页控制器
                                        function updatePagination(page, totalPages) {
                                            let paginationHtml = '';

                                            if (totalPages > 1) {
                                                paginationHtml += '<div class="layui-box layui-laypage layui-laypage-default">';

                                                // 上一页按钮
                                                if (page > 1) {
                                                    paginationHtml += '<a href="javascript:;" class="layui-laypage-prev" data-page="' + (page - 1) + '">上一页</a>';
                                                } else {
                                                    paginationHtml += '<a href="javascript:;" class="layui-laypage-prev layui-disabled">上一页</a>';
                                                }

                                                // 页码按钮
                                                let startPage = Math.max(1, page - 2);
                                                let endPage = Math.min(startPage + 4, totalPages);

                                                if (startPage > 1) {
                                                    paginationHtml += '<a href="javascript:;" data-page="1">1</a>';
                                                    if (startPage > 2) {
                                                        paginationHtml += '<span class="layui-laypage-spr">…</span>';
                                                    }
                                                }

                                                for (let i = startPage; i <= endPage; i++) {
                                                    if (i === page) {
                                                        paginationHtml += '<span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>' + i + '</em></span>';
                                                    } else {
                                                        paginationHtml += '<a href="javascript:;" data-page="' + i + '">' + i + '</a>';
                                                    }
                                                }

                                                if (endPage < totalPages) {
                                                    if (endPage < totalPages - 1) {
                                                        paginationHtml += '<span class="layui-laypage-spr">…</span>';
                                                    }
                                                    paginationHtml += '<a href="javascript:;" data-page="' + totalPages + '">' + totalPages + '</a>';
                                                }

                                                // 下一页按钮
                                                if (page < totalPages) {
                                                    paginationHtml += '<a href="javascript:;" class="layui-laypage-next" data-page="' + (page + 1) + '">下一页</a>';
                                                } else {
                                                    paginationHtml += '<a href="javascript:;" class="layui-laypage-next layui-disabled">下一页</a>';
                                                }

                                                paginationHtml += '</div>';
                                            }

                                            $('#employeePagination').html(paginationHtml);

                                            // 绑定页码点击事件
                                            $('#employeePagination a[data-page]').on('click', function() {
                                                let page = parseInt($(this).data('page'));
                                                renderEmployeeList(page);
                                            });
                                        }

                                        // 初始渲染第一页
                                        renderEmployeeList(currentPage);

                                    } else {
                                        layer.msg('获取员工列表失败: ' + response.message, {icon: 2});
                                    }
                                },
                                error: function() {
                                    layer.msg('获取员工列表失败', {icon: 2});
                                }
                            });
                        }
                    }
                });
            }
        });
    });
</script>

<!-- 表格操作列模板 -->
<script type="text/html" id="project-bar">
    <button class="pear-btn pear-btn-primary pear-btn-sm" lay-event="detail">
        <i class="layui-icon layui-icon-form"></i>
    </button>
<!--     <button class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit">
        <i class="layui-icon layui-icon-edit"></i>
    </button>
    <button class="pear-btn pear-btn-danger pear-btn-sm" lay-event="del">
        <i class="layui-icon layui-icon-delete"></i>
    </button> -->
</script>
</html>