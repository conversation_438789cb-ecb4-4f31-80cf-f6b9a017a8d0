<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>预估项目导入</title>
        {% include 'system/common/header.html' %}
        <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
        <script src="{{ url_for('static', filename='index/js/jquery.min.js') }}"></script>
        <script src="{{ url_for('static', filename='index/layui/layui.js') }}"></script>
    </head>
    <body class="pear-container">
        <!-- 导入表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form" id="uploadForm" enctype="multipart/form-data">
                    <div class="layui-form-item" style="margin-bottom: unset;">
                        <label class="layui-form-label">选择文件</label>
                        <div class="layui-input-inline">
                            <input type="file" name="file" id="fileInput" class="layui-input" accept=".xlsx,.xls" required>
                        </div>
                        <button class="layui-btn" lay-submit lay-filter="import">导入</button>
                        <a class="layui-btn layui-btn-normal" href="{{ url_for('system.import_project.yugu_download_template') }}">下载模板</a>
                    </div>
                </form>
            </div>
        </div>

        <script>
            layui.use(['form', 'upload'], function () {
                let form = layui.form;
                let upload = layui.upload;
                let $ = layui.jquery;

                // 导入表单提交
                form.on('submit(import)', function (data) {
                    var fileInput = document.getElementById('fileInput');
                    if (!fileInput.files || fileInput.files.length === 0) {
                        layer.msg('请先选择文件');
                        return false;
                    }

                    var formData = new FormData();
                    formData.append('file', fileInput.files[0]);

                    var loading = layer.load(1, {
                        shade: [0.1, '#fff']
                    });

                    $.ajax({
                        url: '{{ url_for("system.import_project.import_yugu_data") }}',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        dataType: 'json',
                        success: function (res) {
                            layer.close(loading);
                            if (res.code === 200) {
                                // 显示详细导入结果
                                let msg = res.msg;
                                if (res.data.duplicate > 0 || res.data.error > 0) {
                                    msg += '\n\n点击确定查看详情';
                                }

                                layer.alert(msg, {
                                    title: '导入结果',
                                    btn: ['确定'],
                                    yes: function(index) {
                                        layer.close(index);
                                        if (res.data.duplicate > 0 || res.data.error > 0) {
                                            layer.open({
                                                type: 1,
                                                title: '导入详情',
                                                area: ['80%', '80%'],
                                                content: '<div style="padding: 20px; line-height: 1.8; max-height: 70vh; overflow-y: auto;">' +
                                                         res.msg.replace(/\n/g, '<br>') + '</div>'
                                            });
                                        }
                                    }
                                });

                                // 刷新父页面表格
                                parent.layui.table.reload('yugu-table');
                            } else {
                                layer.msg('导入失败：' + res.msg);
                            }
                        },
                        error: function (xhr, status, error) {
                            layer.close(loading);
                            layer.msg('请求失败：' + error);
                        }
                    });
                    return false;
                });
            });
        </script>
    </body>
</html>
