from applications.extensions import db

# 创建中间表
role_power = db.<PERSON>(
    "admin_role_power",  # 中间表名称
    db.<PERSON>umn("id", db.Integer, primary_key=True, autoincrement=True, comment='标识'),  # 主键
    db.<PERSON>umn("power_id", db.<PERSON><PERSON>, db.<PERSON>("admin_power.id"), comment='用户编号'),  # 属性 外键
    db.<PERSON>umn("role_id", db.<PERSON><PERSON>, db.<PERSON>("admin_role.id"), comment='角色编号'),  # 属性 外键
)
