<!DOCTYPE html>
<html>
<head>
    <title>项目进度详情</title>
    {% include 'system/common/header.html' %}
    <style>
        .project-info {
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .project-info .project-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .project-info p {
            margin: 5px 0;
            color: #666;
        }
        .project-info .code {
            color: #009688;
            font-weight: 600;
        }
        .project-info .status {
            color: #fff;
            background-color: #01AAED;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .progress-card {
            padding: 15px;
            margin-bottom: 15px;
            border-left: 3px solid #1E9FFF;
            background-color: #f8f8f8;
        }
        .progress-card .title {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }
        .progress-card .date {
            font-size: 18px;
            font-weight: 600;
            color: #1E9FFF;
            margin-bottom: 5px;
        }
        .chart-container {
            height: 350px;
            margin-bottom: 20px;
        }
        .timeline-container {
            position: relative;
            padding: 20px 0;
        }
        .timeline-item {
            position: relative;
            padding-left: 40px;
            margin-bottom: 20px;
        }
        .timeline-item:before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: #e8e8e8;
        }
        .timeline-item:last-child:before {
            height: 50%;
        }
        .timeline-item:first-child:before {
            top: 50%;
            height: 50%;
        }
        .timeline-item .timeline-dot {
            position: absolute;
            left: 0;
            top: 10px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #1E9FFF;
            text-align: center;
            line-height: 20px;
            color: #fff;
            z-index: 1;
        }
        .timeline-item .timeline-content {
            padding: 10px 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
            border-left: 3px solid #1E9FFF;
        }
        .timeline-item .timeline-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        .timeline-item .timeline-date {
            font-size: 14px;
            color: #999;
            margin-bottom: 5px;
        }
        .timeline-item.completed .timeline-dot {
            background-color: #5FB878;
        }
        .timeline-item.pending .timeline-dot {
            background-color: #FFB800;
        }
        .timeline-item.in-progress .timeline-dot {
            background-color: #1E9FFF;
        }
        .progress-detail-table .layui-table-cell {
            height: auto;
            line-height: 24px;
        }
        /* 项目状态颜色样式 */
        .status-draft {
            background-color: #BDBDBD !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-reviewing {
            background-color: #1E9FFF !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-pending {
            background-color: #FFB800 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-production {
            background-color: #5FB878 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-completed {
            background-color: #2F4056 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-cancelled {
            background-color: #FF5722 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-acceptance {
            background-color: #01AAED !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-debugging {
            background-color: #9966CC !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-modification {
            background-color: #FF9966 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .edit-btn {
            margin-left: 10px;
            cursor: pointer;
            color: #1E9FFF;
        }
        .edit-btn:hover {
            color: #01AAED;
        }
        .clickable {
            cursor: pointer;
            color: #1E9FFF;
            text-decoration: underline;
        }
        .clickable:hover {
            color: #01AAED;
        }
        .layui-form-label {
            position: relative;
            float: left;
            display: block;
            padding: 9px 15px;
            width: 130px;
            font-weight: 400;
            line-height: 20px;
            text-align: right;
        }
        .layui-input-block {
            margin-left: 160px;
            min-height: 36px;
        }
    </style>
</head>
<body class="pear-container" style="padding: 10px;">
    <!-- 项目基本信息 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="project-info">
                <div class="project-title">
                    项目编号：<span class="code"><span id="projectType">--</span>-{{ project.project_code }}</span> - {{ project.project_name }}
                    <span class="status" id="projectStatus">--</span>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md3">
                        <p><i class="layui-icon layui-icon-rmb"></i> 项目价格：¥<span id="projectPrice">--</span></p>
                    </div>
                    <div class="layui-col-md3">
                        <p><i class="layui-icon layui-icon-component"></i> 机台数量：<span id="machineNumber">--</span></p>
                    </div>
                    <div class="layui-col-md3">
                        <p><i class="layui-icon layui-icon-date"></i> 计划完成日期：<span id="deliveryDate">--</span></p>
                    </div>
                    <div class="layui-col-md3">
                        <p><i class="layui-icon layui-icon-user"></i> 项目负责人：<span id="projectManager" class="clickable">--</span></p>
                    </div>
                </div>
                <div class="layui-row" style="margin-top: 10px;">
                    <div class="layui-col-md12">
                        <p><i class="layui-icon layui-icon-about"></i> 项目业务简介：<span id="projectDescription">--</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目进度信息 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md4">
            <div class="progress-card">
                <div class="title">项目开始</div>
                <div class="date">合同开始日：<span id="projectStartDate">--</span></div>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="progress-card">
                <div class="title">发货与安装</div>
                <div class="date">发货完成日：<span id="shippingEndDate">--</span></div>
                <div class="date">开始安装日：<span id="installationStartDate">--</span></div>
                <div class="date">安装完成日：<span id="installationEndDate">--</span></div>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="progress-card">
                <div class="title">项目验收</div>
                <div class="date">验收完成日：<span id="acceptanceDate">--</span></div>
            </div>
        </div>
    </div>

    <!-- 项目进度时间线 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    项目进度时间线
                    <button class="layui-btn layui-btn-sm layui-btn-normal" style="float: right;" id="editProgressBtn">
                        <i class="layui-icon layui-icon-edit"></i> 编辑进度信息
                    </button>
                </div>
                <div class="layui-card-body">
                    <div class="timeline-container" id="progressTimeline">
                        <!-- 时间线内容将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目进度明细表格 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">进度明细</div>
                <div class="layui-card-body">
                    <table id="progress-detail-table" lay-filter="progress-detail-table"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑进度信息弹窗 -->
    <div id="editProgressForm" style="display: none; padding: 20px;">
        <form class="layui-form" lay-filter="progressForm">
            <div class="layui-form-item">
                <label class="layui-form-label">项目业务简介</label>
                <div class="layui-input-block">
                    <textarea name="project_description" placeholder="请输入项目业务简介" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">合同开始日期</label>
                <div class="layui-input-block">
                    <input type="text" name="project_start_date" id="projectStartDateInput" placeholder="请选择日期" class="layui-input date-picker">
                </div>
            </div>
            <!-- 隐藏的字段，保留但不显示 -->
            <div style="display: none;">
                <input type="hidden" name="production_start_date" id="productionStartDateInput">
                <input type="hidden" name="production_end_date" id="productionEndDateInput">
                <input type="hidden" name="shipping_start_date" id="shippingStartDateInput">
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">发货完成日期</label>
                <div class="layui-input-block">
                    <input type="text" name="shipping_end_date" id="shippingEndDateInput" placeholder="请选择日期" class="layui-input date-picker">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">安装调试开始日期</label>
                <div class="layui-input-block">
                    <input type="text" name="installation_start_date" id="installationStartDateInput" placeholder="请选择日期" class="layui-input date-picker">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">安装调试完成日期</label>
                <div class="layui-input-block">
                    <input type="text" name="installation_end_date" id="installationEndDateInput" placeholder="请选择日期" class="layui-input date-picker">
                </div>
            </div>



            <!-- 隐藏的字段，保留但不显示 -->
            <div style="display: none;">
                <input type="hidden" name="debugging_start_date" id="debuggingStartDateInput">
                <input type="hidden" name="debugging_end_date" id="debuggingEndDateInput">
            </div>


            <div class="layui-form-item">
                <label class="layui-form-label">验收完成日期</label>
                <div class="layui-input-block">
                    <input type="text" name="acceptance_date" id="acceptanceDateInput" placeholder="请选择日期" class="layui-input date-picker">
                </div>
            </div>

            <!-- 隐藏的字段，保留但不显示 -->
            <div style="display: none;">
                <input type="hidden" name="project_end_date" id="projectEndDateInput">
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn" lay-submit lay-filter="saveProgressForm">保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</body>

{% include 'system/common/footer.html' %}
<script>
    layui.use(['echarts', 'table', 'jquery', 'element', 'laydate', 'form'], function() {
        let echarts = layui.echarts;
        let table = layui.table;
        let $ = layui.jquery;
        let element = layui.element;
        let laydate = layui.laydate;
        let form = layui.form;

        // 将项目ID作为数值传递给JavaScript
        let projectId = parseInt('{{ project.id }}');

        // 更新项目状态
        updateProjectStatus();

        // 加载项目详情数据
        loadProjectDetails();

        // 初始化日期选择器
        initDatePickers();

        // 日期选择器初始化函数
        function initDatePickers() {
            // 合同开始日期
            laydate.render({
                elem: '#projectStartDateInput',
                type: 'date',
                format: 'yyyy-MM-dd',
                done: function(value, date) {
                    // 更新发货完成日期的最小值
                    if (value) {
                        laydate.render({
                            elem: '#shippingEndDateInput',
                            type: 'date',
                            format: 'yyyy-MM-dd',
                            min: value
                        });
                    }
                }
            });

            // 发货完成日期
            laydate.render({
                elem: '#shippingEndDateInput',
                type: 'date',
                format: 'yyyy-MM-dd',
                min: $('#projectStartDateInput').val() || '1900-01-01',
                done: function(value, date) {
                    // 更新安装开始日期的最小值
                    if (value) {
                        laydate.render({
                            elem: '#installationStartDateInput',
                            type: 'date',
                            format: 'yyyy-MM-dd',
                            min: value
                        });
                    }
                }
            });

            // 安装开始日期
            laydate.render({
                elem: '#installationStartDateInput',
                type: 'date',
                format: 'yyyy-MM-dd',
                min: $('#shippingEndDateInput').val() || '1900-01-01',
                done: function(value, date) {
                    // 更新安装完成日期的最小值
                    if (value) {
                        laydate.render({
                            elem: '#installationEndDateInput',
                            type: 'date',
                            format: 'yyyy-MM-dd',
                            min: value
                        });
                    }
                }
            });

            // 安装完成日期
            laydate.render({
                elem: '#installationEndDateInput',
                type: 'date',
                format: 'yyyy-MM-dd',
                min: $('#installationStartDateInput').val() || '1900-01-01',
                done: function(value, date) {
                    // 更新验收完成日期的最小值
                    if (value) {
                        laydate.render({
                            elem: '#acceptanceDateInput',
                            type: 'date',
                            format: 'yyyy-MM-dd',
                            min: value
                        });
                    }
                }
            });

            // 验收完成日期
            laydate.render({
                elem: '#acceptanceDateInput',
                type: 'date',
                format: 'yyyy-MM-dd',
                min: $('#installationEndDateInput').val() || '1900-01-01'
            });
        }

        // 编辑按钮点击事件
        $('#editProgressBtn').on('click', function() {
            // 打开编辑表单弹窗
            let editIndex = layer.open({
                type: 1,
                title: '编辑项目进度信息',
                area: ['600px', '80%'],
                content: $('#editProgressForm'),
                success: function() {
                    // 获取当前进度信息并填充表单
                    $.ajax({
                        url: '/system/project_progress/api/project/' + projectId,
                        success: function(response) {
                            if (response.success) {
                                let progress = response.data.progress_details;

                                // 填充表单
                                form.val('progressForm', {
                                    'project_description': progress.project_description || '',
                                    'production_start_date': progress.production_start_date || '',
                                    'production_end_date': progress.production_end_date || '',
                                    'shipping_start_date': progress.shipping_start_date || '',
                                    'shipping_end_date': progress.shipping_end_date || '',
                                    'installation_start_date': progress.installation_start_date || '',
                                    'installation_end_date': progress.installation_end_date || '',
                                    'debugging_start_date': progress.debugging_start_date || '',
                                    'debugging_end_date': progress.debugging_end_date || '',
                                    'project_start_date': progress.project_start_date || '',
                                    'acceptance_date': progress.acceptance_date || '',
                                    'project_end_date': progress.project_end_date || ''
                                });

                                form.render();
                            } else {
                                layer.msg('获取项目进度信息失败: ' + response.message, {icon: 2});
                            }
                        },
                        error: function() {
                            layer.msg('获取项目进度信息失败', {icon: 2});
                        }
                    });
                }
            });
        });

        // 项目负责人点击事件 - 先解绑再绑定，确保只绑定一次
        $('#projectManager').off('click').on('click', function() {
            // 打开项目负责人编辑弹窗
            layer.open({
                type: 1,
                title: '项目负责人管理',
                area: ['700px', '500px'],
                content: `
                    <div style="padding: 20px;">
                        <div class="layui-form" lay-filter="managerForm">
                            <div class="layui-form-item">
                                <label class="layui-form-label">当前负责人</label>
                                <div class="layui-input-block">
                                    <input type="text" id="currentManager" class="layui-input" readonly value="${$(this).text()}">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">搜索员工</label>
                                <div class="layui-input-block">
                                    <input type="text" id="employeeSearch" class="layui-input" placeholder="输入姓名搜索">
                                    <button type="button" class="layui-btn layui-btn-primary" id="searchBtn" style="margin-top: 10px;">搜索</button>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">员工列表</label>
                                <div class="layui-input-block">
                                    <table class="layui-table" id="employeeTable">
                                        <thead>
                                            <tr>
                                                <th>姓名</th>
                                                <th>工号</th>
                                                <th>职位</th>
                                                <th>部门</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="employeeList">
                                            <!-- 员工列表将通过AJAX加载 -->
                                        </tbody>
                                    </table>
                                    <!-- 分页控制器 -->
                                    <div id="employeePagination" style="text-align: center; margin-top: 10px;"></div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-primary" id="viewHistoryBtn">查看变更历史</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                success: function(layero, index) {
                    // 加载员工列表
                    loadEmployees('');

                    // 搜索按钮点击事件
                    $('#searchBtn').on('click', function() {
                        let keyword = $('#employeeSearch').val();
                        loadEmployees(keyword);
                    });

                    // 查看历史按钮点击事件
                    $('#viewHistoryBtn').on('click', function() {
                        // 加载历史记录
                        $.ajax({
                            url: '/system/project_progress/api/project/' + projectId + '/manager_logs_detailed',
                            success: function(response) {
                                if (response.success) {
                                    let logs = response.data;
                                    let historyHtml = '';

                                    if (logs.length === 0) {
                                        historyHtml = '<div class="layui-none">暂无变更记录</div>';
                                    } else {
                                        historyHtml = '<table class="layui-table">';
                                        historyHtml += '<thead><tr><th>操作时间</th><th>操作人</th><th>操作内容</th></tr></thead>';
                                        historyHtml += '<tbody>';

                                        logs.forEach(function(log) {
                                            historyHtml += '<tr>';
                                            historyHtml += '<td>' + log.operation_time + '</td>';
                                            historyHtml += '<td>' + log.operator + '</td>';
                                            historyHtml += '<td>' + log.description + '</td>';
                                            historyHtml += '</tr>';
                                        });

                                        historyHtml += '</tbody></table>';
                                    }

                                    // 打开历史记录弹窗
                                    layer.open({
                                        type: 1,
                                        title: '项目负责人变更历史',
                                        area: ['800px', '500px'],
                                        content: '<div style="padding: 20px;">' + historyHtml + '</div>'
                                    });
                                } else {
                                    layer.msg('获取历史记录失败: ' + response.message, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('获取历史记录失败', {icon: 2});
                            }
                        });
                    });

                    // 加载员工列表函数
                    function loadEmployees(keyword) {
                        $.ajax({
                            url: '/system/project_progress/api/employees',
                            data: { keyword: keyword },
                            success: function(response) {
                                if (response.success) {
                                    let employees = response.data;

                                    // 分页变量
                                    let pageSize = 10; // 每页显示10条
                                    let currentPage = 1; // 当前页码
                                    let totalPages = Math.ceil(employees.length / pageSize); // 总页数

                                    // 渲染员工列表函数
                                    function renderEmployeeList(page) {
                                        let html = '';

                                        if (employees.length === 0) {
                                            html = '<tr><td colspan="5" class="layui-none">暂无员工数据</td></tr>';
                                        } else {
                                            // 计算当前页的数据范围
                                            let start = (page - 1) * pageSize;
                                            let end = Math.min(start + pageSize, employees.length);

                                            // 只渲染当前页的数据
                                            for (let i = start; i < end; i++) {
                                                let emp = employees[i];
                                                html += '<tr>';
                                                html += '<td>' + emp.name + '</td>';
                                                html += '<td>' + (emp.employee_id || '--') + '</td>';
                                                html += '<td>' + (emp.position || '--') + '</td>';
                                                html += '<td>' + (emp.dept_name || '--') + '</td>';
                                                html += '<td><button class="layui-btn layui-btn-xs select-employee" data-name="' + emp.name + '">选择</button></td>';
                                                html += '</tr>';
                                            }
                                        }

                                        $('#employeeList').html(html);

                                        // 更新分页控制器
                                        updatePagination(page, totalPages);

                                        // 选择员工按钮点击事件
                                        $('.select-employee').on('click', function() {
                                            let newManager = $(this).data('name');

                                            // 提交更新请求
                                            $.ajax({
                                                url: '/system/project_progress/api/project/' + projectId + '/update_manager_with_log',
                                                type: 'POST',
                                                contentType: 'application/json',
                                                data: JSON.stringify({manager: newManager}),
                                                success: function(response) {
                                                    if (response.success) {
                                                        layer.msg('更新成功', {icon: 1});
                                                        layer.close(index);

                                                        // 重新加载项目详情
                                                        loadProjectDetails();
                                                    } else {
                                                        layer.msg('更新失败: ' + response.message, {icon: 2});
                                                    }
                                                },
                                                error: function() {
                                                    layer.msg('更新失败', {icon: 2});
                                                }
                                            });
                                        });
                                    }

                                    // 更新分页控制器
                                    function updatePagination(page, totalPages) {
                                        let paginationHtml = '';

                                        if (totalPages > 1) {
                                            paginationHtml += '<div class="layui-box layui-laypage layui-laypage-default">';

                                            // 上一页按钮
                                            if (page > 1) {
                                                paginationHtml += '<a href="javascript:;" class="layui-laypage-prev" data-page="' + (page - 1) + '">上一页</a>';
                                            } else {
                                                paginationHtml += '<a href="javascript:;" class="layui-laypage-prev layui-disabled">上一页</a>';
                                            }

                                            // 页码按钮
                                            let startPage = Math.max(1, page - 2);
                                            let endPage = Math.min(startPage + 4, totalPages);

                                            if (startPage > 1) {
                                                paginationHtml += '<a href="javascript:;" data-page="1">1</a>';
                                                if (startPage > 2) {
                                                    paginationHtml += '<span class="layui-laypage-spr">…</span>';
                                                }
                                            }

                                            for (let i = startPage; i <= endPage; i++) {
                                                if (i === page) {
                                                    paginationHtml += '<span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>' + i + '</em></span>';
                                                } else {
                                                    paginationHtml += '<a href="javascript:;" data-page="' + i + '">' + i + '</a>';
                                                }
                                            }

                                            if (endPage < totalPages) {
                                                if (endPage < totalPages - 1) {
                                                    paginationHtml += '<span class="layui-laypage-spr">…</span>';
                                                }
                                                paginationHtml += '<a href="javascript:;" data-page="' + totalPages + '">' + totalPages + '</a>';
                                            }

                                            // 下一页按钮
                                            if (page < totalPages) {
                                                paginationHtml += '<a href="javascript:;" class="layui-laypage-next" data-page="' + (page + 1) + '">下一页</a>';
                                            } else {
                                                paginationHtml += '<a href="javascript:;" class="layui-laypage-next layui-disabled">下一页</a>';
                                            }

                                            paginationHtml += '</div>';
                                        }

                                        $('#employeePagination').html(paginationHtml);

                                        // 绑定页码点击事件
                                        $('#employeePagination a[data-page]').on('click', function() {
                                            let page = parseInt($(this).data('page'));
                                            renderEmployeeList(page);
                                        });
                                    }

                                    // 初始渲染第一页
                                    renderEmployeeList(currentPage);

                                } else {
                                    layer.msg('获取员工列表失败: ' + response.message, {icon: 2});
                                }
                            },
                            error: function() {
                                layer.msg('获取员工列表失败', {icon: 2});
                            }
                        });
                    }

                    // 查看历史按钮事件已在上面定义
                }
            });
        });

        // 表单提交事件
        form.on('submit(saveProgressForm)', function(data) {
            // 验证日期的合理性
            if (!validateDates(data.field)) {
                return false; // 阻止表单提交
            }

            // 提交表单数据
            $.ajax({
                url: '/system/project_progress/api/project/' + projectId + '/progress',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data.field),
                success: function(response) {
                    if (response.success) {
                        layer.msg('保存成功', {icon: 1});
                        layer.closeAll('page');

                        // 更新项目状态
                        updateProjectStatus();

                        // 重新加载项目详情
                        loadProjectDetails();

                        // 如果在iframe中，通知父窗口刷新表格
                        if (window.parent && window.parent.layui && window.parent.layui.table) {
                            window.parent.layui.table.reload('project-table');
                        }
                    } else {
                        layer.msg('保存失败: ' + response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('保存失败', {icon: 2});
                }
            });

            return false; // 阻止表单默认提交
        });

        // 验证日期的合理性
        function validateDates(formData) {
            // 定义日期字段及其依赖关系
            const dateFields = [
                { name: 'project_start_date', label: '合同开始日期' },
                { name: 'shipping_end_date', label: '发货完成日期', depends: 'project_start_date' },
                { name: 'installation_start_date', label: '开始安装日期', depends: 'shipping_end_date' },
                { name: 'installation_end_date', label: '安装完成日期', depends: 'installation_start_date' },
                { name: 'acceptance_date', label: '验收完成日期', depends: 'installation_end_date' }
            ];

            // 验证每个日期字段
            for (let i = 0; i < dateFields.length; i++) {
                const field = dateFields[i];
                const value = formData[field.name];

                // 如果字段有值且有依赖字段
                if (value && field.depends && formData[field.depends]) {
                    const dependValue = formData[field.depends];

                    // 验证日期顺序
                    if (new Date(value) < new Date(dependValue)) {
                        layer.msg(`${field.label}不能早于${dateFields.find(f => f.name === field.depends).label}`, {icon: 2});
                        return false;
                    }
                }
            }

            return true;
        }

        function updateProjectStatus() {
            $.ajax({
                url: '/system/project_progress/api/project/' + projectId + '/update_status',
                success: function(response) {
                    if (!response.success) {
                        layer.msg('更新项目状态失败: ' + response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('更新项目状态失败', {icon: 2});
                }
            });
        }

        function loadProjectDetails() {
            $.ajax({
                url: '/system/project_progress/api/project/' + projectId,
                success: function(response) {
                    if (response.success) {
                        let data = response.data;

                        // 更新项目基本信息
                        $('#projectStatus').text(data.status_name);

                        // 根据状态设置不同的样式类
                        const $statusElement = $('#projectStatus');
                        $statusElement.removeClass('status');

                        // 状态映射
                        const statusValueMapping = {
                            '0': 'status-pending',    // 未开始
                            '1': 'status-production', // 生产中
                            '2': 'status-production', // 安装中
                            '3': 'status-acceptance', // 验收中
                            '4': 'status-debugging',  // 调试中
                            '5': 'status-modification', // 整改中
                            '7': 'status-completed'   // 已完成
                        };

                        let statusClass = statusValueMapping[data.status_value] || 'status';
                        $statusElement.addClass(statusClass);

                        $('#projectType').text(data.project_type_name || '');
                        $('#projectPrice').text(data.price ? data.price.toLocaleString() : 0);
                        $('#machineNumber').text(data.machine_number || 0);
                        $('#deliveryDate').text(data.delivery_date || '--');
                        $('#projectManager').text(data.project_manager || '--');

                        // 更新项目进度信息
                        let progress = data.progress_details;
                        $('#projectDescription').text(progress.project_description || '--');
                        $('#projectStartDate').text(progress.project_start_date || '--');
                        $('#shippingEndDate').text(progress.shipping_end_date || '--');
                        $('#installationStartDate').text(progress.installation_start_date || '--');
                        $('#installationEndDate').text(progress.installation_end_date || '--');
                        $('#acceptanceDate').text(progress.acceptance_date || '--');

                        // 渲染进度时间线
                        renderProgressTimeline(progress);

                        // 加载进度明细表格
                        loadProgressDetailsTable();
                    } else {
                        layer.msg('加载项目详情数据失败: ' + response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('加载项目详情数据失败', {icon: 2});
                }
            });
        }

        function renderProgressTimeline(progress) {
            // 清空时间线容器
            $('#progressTimeline').empty();

            // 定义时间线项
            let timelineItems = [
                {
                    title: '合同开始',
                    date: progress.project_start_date,
                    status: progress.project_start_date ? 'completed' : 'pending'
                },
                {
                    title: '发货完成',
                    date: progress.shipping_end_date,
                    status: progress.shipping_end_date ? 'completed' : 'pending'
                },
                {
                    title: '开始安装',
                    date: progress.installation_start_date,
                    status: progress.installation_start_date ? 'completed' : 'pending'
                },
                {
                    title: '安装完成',
                    date: progress.installation_end_date,
                    status: progress.installation_end_date ? 'completed' : 'pending'
                },
                {
                    title: '验收完成',
                    date: progress.acceptance_date,
                    status: progress.acceptance_date ? 'completed' : 'pending'
                }
            ];

            // 过滤掉没有日期的项
            timelineItems = timelineItems.filter(item => item.date);

            // 按日期排序
            timelineItems.sort((a, b) => {
                return new Date(a.date) - new Date(b.date);
            });

            // 生成时间线HTML
            let html = '';
            timelineItems.forEach(function(item, index) {
                html += `
                <div class="timeline-item ${item.status}">
                    <div class="timeline-dot"><i class="layui-icon layui-icon-ok"></i></div>
                    <div class="timeline-content">
                        <div class="timeline-title">${item.title}</div>
                        <div class="timeline-date">${item.date}</div>
                    </div>
                </div>
                `;
            });

            // 如果没有时间线项，显示提示信息
            if (timelineItems.length === 0) {
                html = '<div class="layui-none">暂无进度信息</div>';
            }

            // 添加到容器
            $('#progressTimeline').html(html);
        }

        function loadProgressDetailsTable() {
            // 渲染进度明细表格
            table.render({
                elem: '#progress-detail-table',
                cols: [[
                    {field: 'stage', title: '阶段', align: 'center'},
                    {field: 'start_date', title: '开始日期', align: 'center'},
                    {field: 'end_date', title: '完成日期', align: 'center'},
                    {field: 'duration', title: '持续时间', align: 'center'},
                    {field: 'status', title: '状态', align: 'center', templet: function(d) {
                        if (d.status === '已完成') {
                            return '<span class="layui-badge layui-bg-green">已完成</span>';
                        } else if (d.status === '进行中') {
                            return '<span class="layui-badge layui-bg-blue">进行中</span>';
                        } else {
                            return '<span class="layui-badge layui-bg-gray">未开始</span>';
                        }
                    }}
                ]],
                data: getProgressDetailData(),
                page: false,
                skin: 'line',
                even: true,
                size: 'lg'
            });
        }

        function getProgressDetailData() {
            // 从页面元素中获取进度数据
            let data = [];

            // 合同开始阶段
            let projectStartDate = $('#projectStartDate').text();
            if (projectStartDate !== '--') {
                data.push({
                    stage: '合同开始阶段',
                    start_date: projectStartDate,
                    end_date: projectStartDate,
                    duration: '0天',
                    status: '已完成'
                });
            }

            // 发货阶段
            let shippingEndDate = $('#shippingEndDate').text();
            if (shippingEndDate !== '--') {
                data.push({
                    stage: '发货阶段',
                    start_date: projectStartDate !== '--' ? projectStartDate : '',
                    end_date: shippingEndDate,
                    duration: calculateDuration(projectStartDate, shippingEndDate),
                    status: '已完成'
                });
            }

            // 安装阶段
            let installationStartDate = $('#installationStartDate').text();
            let installationEndDate = $('#installationEndDate').text();
            if (installationStartDate !== '--') {
                let status = '未开始';
                if (installationStartDate !== '--' && installationEndDate !== '--') {
                    status = '已完成';
                } else if (installationStartDate !== '--') {
                    status = '进行中';
                }

                data.push({
                    stage: '安装阶段',
                    start_date: installationStartDate,
                    end_date: installationEndDate !== '--' ? installationEndDate : '',
                    duration: calculateDuration(installationStartDate, installationEndDate),
                    status: status
                });
            }

            // 验收阶段
            let acceptanceDate = $('#acceptanceDate').text();
            if (acceptanceDate !== '--') {
                data.push({
                    stage: '验收阶段',
                    start_date: installationEndDate !== '--' ? installationEndDate : '',
                    end_date: acceptanceDate,
                    duration: calculateDuration(installationEndDate, acceptanceDate),
                    status: '已完成'
                });
            }

            return data;
        }

        function calculateDuration(startDate, endDate) {
            if (startDate === '--' || endDate === '--' || !startDate || !endDate) {
                return '进行中';
            }

            let start = new Date(startDate);
            let end = new Date(endDate);
            let diffTime = Math.abs(end - start);
            let diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            return diffDays + '天';
        }
    });
</script>
</html>