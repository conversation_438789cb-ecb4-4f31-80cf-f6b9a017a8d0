import datetime
from flask_login import UserMixin
from applications.extensions import db


class ygong(db.Model, UserMixin):
    __tablename__ = 'admin_yg'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='用户ID')
    openid = db.Column(db.String(255), comment='openid')
    name = db.Column(db.String(255), comment='姓名')
    employee_id = db.Column(db.String(255), comment='员工工号')
    phone = db.Column(db.String(11), comment='电话号码')
    dept_id = db.Column(db.Integer, db.<PERSON>Key('admin_dept.id'), comment='部门id')
    position = db.Column(db.String(255), comment='职位')
    hire_date = db.Column(db.Date, comment='入职日期')
    gender = db.Column(db.String(10), comment='性别')
    birth_date = db.Column(db.Date, comment='出生日期')
    age = db.Column(db.Integer, comment='年龄')
    id_card = db.Column(db.String(18), comment='身份证号码')
    is_formal = db.Column(db.String(20), comment='是否正式')
    base_salary = db.Column(db.Float, default=0, comment='基本工资')
    performance_salary = db.Column(db.Float, default=0, comment='绩效工资')
    supervisor_assessment = db.Column(db.Float, default=0, comment='主管考核项')
    enable = db.Column(db.Integer, default=0, comment='启用')
    create_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='创建时间')
    role = db.relationship('Role', secondary="admin_yg_role", backref=db.backref('yg'), lazy='dynamic')

    # 添加与部门的关联
    dept = db.relationship('Dept', backref='employees', foreign_keys=[dept_id])






