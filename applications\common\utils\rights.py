from functools import wraps
from flask import abort, request, jsonify, session, current_app
from flask_login import login_required, current_user
from applications.common.admin import admin_log
from applications.models import Power


def authorize(power: str, log: bool = False):
    """
    用户权限判断，用于判断目前会话用户是否拥有访问权限。
    在模板中有与之对应的全局非修饰函数 authorize ，此函数定义位于 `applications/extensions/init_template_directives.py` 。

    :param power: 权限标识
    :type power: str
    :param log: 是否记录日志, defaults to False
    :type log: bool, optional
    """
    def decorator(func):
        @login_required
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 对于超级管理员，首先检查权限是否存在且启用
            if current_user.username == current_app.config.get("SUPERADMIN"):
                # 如果是菜单权限，检查该权限是否启用
                power_obj = Power.query.filter_by(code=power).first()
                if power_obj and power_obj.enable == 0:
                    if log:
                        admin_log(request=request, is_access=False)
                    if request.method == 'GET':
                        abort(403)
                    else:
                        return jsonify(success=False, msg="权限未启用!")
                
                if log:
                    admin_log(request=request, is_access=True)
                return func(*args, **kwargs)

            if not power in session.get('permissions'):
                if log:
                    admin_log(request=request, is_access=False)
                if request.method == 'GET':
                    abort(403)
                else:
                    return jsonify(success=False, msg="权限不足!")

            if log:
                admin_log(request=request, is_access=True)

            return func(*args, **kwargs)

        return wrapper

    return decorator
