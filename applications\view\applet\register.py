# register.py
from flask import Blueprint, request, jsonify
from applications.extensions import db
from applications.models import User, Applet_user, ygong  # 假设有 User 和 PersonnelInfo 模型

bp = Blueprint('register', __name__)

@bp.route('/register', methods=['POST'])
def register():
    data = request.json
    openid = data.get('openid')
    name = data.get('name')
    phone = data.get('phone')

    if not openid or not name or not phone:
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400

    try:
        # 检查员工是否存在且启用
        personnel = ygong.query.filter_by(name=name, phone=phone).first()
        if not personnel:
            return jsonify({'success': False, 'message': '员工信息不存在'}), 404
        if personnel.enable != 1:
            return jsonify({'success': False, 'message': '您没有权限操作该应用'}), 403

        # 插入数据到 user 表
        new_user = Applet_user(openid=openid, name=name, phone=phone)
        db.session.add(new_user)

        # 更新 personnel_info 表中的 openid
        personnel.openid = openid

        db.session.commit()
        return jsonify({'success': True, 'message': '注册成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'数据库错误: {str(e)}'}), 500