# GosunTec项目功能介绍书

## 目录

- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [核心功能模块](#核心功能模块)
- [数据模型](#数据模型)
- [部署要求](#部署要求)

## 项目概述

### 项目背景和目标

GosunTec是一个基于Pear Admin Flask框架开发的企业项目管理系统，旨在提供全面的项目管理、成本控制、工时统计和人员管理解决方案。该系统通过整合企业内部的项目信息、人员信息和财务数据，实现项目全生命周期的可视化管理和数据分析。

### 系统价值和应用场景

- **项目管理优化**：提供项目创建、跟踪、进度管理和成本控制等功能，帮助企业优化项目管理流程
- **成本控制**：通过BOM成本、人工成本和其他成本的精细化管理，帮助企业控制项目成本
- **工时统计**：支持员工工时记录和统计，便于项目工时分配和成本核算
- **数据可视化**：提供多维度的数据统计和可视化报表，辅助管理决策
- **移动端支持**：通过小程序接口，支持员工在移动端进行工时记录和查询

## 技术架构

### 技术栈概述

1. **后端技术**
   - **框架**：Flask (Python Web框架)
   - **ORM**：SQLAlchemy (对象关系映射)
   - **数据序列化**：Flask-Marshmallow
   - **用户认证**：Flask-Login
   - **数据库迁移**：Flask-Migrate
   - **表单处理**：Flask-WTF

2. **前端技术**
   - **UI框架**：Pear Admin Layui
   - **JavaScript库**：jQuery
   - **图表库**：ECharts
   - **数据表格**：Layui Table
   - **表单组件**：Layui Form

3. **数据库**
   - **主数据库**：MySQL
   - **备选数据库**：SQLite (开发环境)

4. **部署环境**
   - **Web服务器**：Gunicorn
   - **反向代理**：Nginx
   - **容器化**：Docker

### 系统架构图

```
+----------------------------------+
|         Client Layer             |
|  +-------------+ +-------------+ |
|  |  Web Browser| |  微信小程序  | |
|  +-------------+ +-------------+ |
+----------------------------------+
                |
                v
+----------------------------------+
|         Presentation Layer       |
|  +-------------+ +-------------+ |
|  |  Pear Admin | |  RESTful API| |
|  |  Layui UI   | |  接口        | |
|  +-------------+ +-------------+ |
+----------------------------------+
                |
                v
+----------------------------------+
|         Application Layer        |
|  +-------------+ +-------------+ |
|  |  Flask      | |  业务逻辑    | |
|  |  框架        | |  模块        | |
|  +-------------+ +-------------+ |
+----------------------------------+
                |
                v
+----------------------------------+
|         Data Access Layer        |
|  +-------------+ +-------------+ |
|  | SQLAlchemy  | |  数据模型    | |
|  | ORM         | |  定义        | |
|  +-------------+ +-------------+ |
+----------------------------------+
                |
                v
+----------------------------------+
|         Infrastructure Layer     |
|  +-------------+ +-------------+ |
|  |  MySQL      | |  文件存储    | |
|  |  数据库      | |  系统        | |
|  +-------------+ +-------------+ |
+----------------------------------+
```

### 核心组件说明

1. **Flask应用服务器**
   - 处理HTTP请求和响应
   - 路由管理和URL映射
   - 视图函数和模板渲染
   - 会话管理和用户认证

2. **SQLAlchemy ORM**
   - 数据库模型定义
   - 数据库查询构建
   - 事务管理
   - 数据库连接池管理

3. **Pear Admin Layui**
   - 页面布局和样式
   - 表单和表格组件
   - 交互效果和动画
   - 响应式设计

4. **业务逻辑模块**
   - 项目管理
   - 成本控制
   - 工时统计
   - 用户权限管理

## 核心功能模块

### 用户和权限管理

1. **用户管理**
   - 用户创建、编辑和删除
   - 用户角色分配
   - 用户部门管理
   - 个人信息管理

2. **角色权限管理**
   - 角色创建和管理
   - 权限分配和控制
   - 菜单权限设置
   - 操作日志记录

3. **部门管理**
   - 部门创建和管理
   - 部门层级结构
   - 部门负责人设置

### 项目管理

1. **项目类型管理**
   - 项目类型创建和管理
   - 项目类型状态控制
   - 项目类型排序

2. **项目信息管理**
   - 项目创建和编辑
   - 项目基本信息维护
   - 项目状态管理
   - 项目负责人分配
   - 项目参与部门设置
   - 项目币种设置

3. **项目筛选和查询**
   - 按项目名称、编码筛选
   - 按部门筛选
   - 按状态筛选
   - 数据导出功能

### 项目进度跟踪

1. **项目进度管理**
   - 项目开始日期设置
   - 生产开始/完成日期记录
   - 发货开始/完成日期记录
   - 安装开始/完成日期记录
   - 验收日期记录
   - 项目完成日期记录

2. **进度可视化**
   - 项目进度条显示
   - 项目甘特图展示
   - 项目状态自动计算
   - 项目状态颜色区分

3. **项目异常管理**
   - 异常项目标记
   - 异常原因记录
   - 异常项目查询

### 项目成本管理

1. **成本预估**
   - 项目总预估成本设置
   - BOM成本预估
   - 机械BOM成本预估
   - 电气BOM成本预估
   - 人工成本预估
   - 其他成本预估

2. **实际成本记录**
   - BOM成本导入
   - 人工成本计算
   - 其他成本记录
   - 月度成本汇总

3. **成本分析**
   - 预估成本与实际成本对比
   - 成本差异分析
   - 成本超支预警
   - 成本比例可视化

### 项目付款管理

1. **付款计划管理**
   - 预付款设置
   - 发货款设置
   - 验收款设置
   - 质保金设置

2. **实际付款记录**
   - 付款日期记录
   - 付款金额记录
   - 付款备注记录

3. **付款状态管理**
   - 付款状态自动计算
   - 未付款项目提醒
   - 部分付款项目标记
   - 付款率可视化

### 工时统计与分析

1. **工时记录**
   - 正常工时记录
   - 加班工时记录
   - 工作内容记录
   - 工作地点记录（厂内/厂外）

2. **工时统计**
   - 按员工统计工时
   - 按部门统计工时
   - 按项目统计工时
   - 按时间段统计工时

3. **会议统计**
   - 会议工时记录
   - 会议工时成本计算
   - 会议工时统计报表

### 员工薪资管理

1. **薪资导入**
   - Excel薪资数据导入
   - 员工薪资记录
   - 薪资历史查询

2. **薪资组成**
   - 基本工资
   - 绩效工资
   - 加班费
   - 各类津贴
   - 扣款项目
   - 实发工资

3. **薪资查询**
   - 员工薪资查询
   - 部门薪资统计
   - 薪资历史记录

### 统计报表

1. **工时统计报表**
   - 员工工时统计
   - 部门工时统计
   - 项目工时统计
   - 工时成本分析

2. **成本统计报表**
   - 项目成本统计
   - 部门成本统计
   - 成本类型分析
   - 成本趋势分析

3. **项目统计报表**
   - 项目状态统计
   - 项目类型统计
   - 项目完成率分析
   - 项目付款率分析

### 小程序接口

1. **用户认证**
   - 微信登录
   - 用户注册
   - 获取OpenID

2. **项目信息**
   - 获取项目列表
   - 获取项目详情
   - 项目分类查询

3. **工时记录**
   - 提交工时记录
   - 查询工时记录
   - 工时记录详情

4. **薪资查询**
   - 查询薪资记录
   - 薪资详情查看
   - 薪资确认

## 数据模型

### 核心数据实体

1. **用户相关**
   - User (用户)
   - Role (角色)
   - Power (权限)
   - Dept (部门)
   - AdminLog (操作日志)

2. **项目相关**
   - Import_project (项目)
   - ProjectManageDept (项目类型)
   - ProjectProgress (项目进度)
   - ProjectPayment (项目付款)
   - ProjectActualCost (项目实际成本)
   - BOMCostImport (BOM成本导入)
   - OtherCostImport (其他成本导入)
   - ProjectLaborCost (项目人工成本)

3. **员工相关**
   - ygong (员工)
   - EmployeeSalary (员工薪资)
   - LogInfo (工时日志)
   - Applet_user (小程序用户)

### 实体关系说明

- 项目(Import_project)与项目类型(ProjectManageDept)是多对一关系
- 项目(Import_project)与部门(Dept)是多对多关系
- 项目(Import_project)与项目进度(ProjectProgress)是一对一关系
- 项目(Import_project)与项目付款(ProjectPayment)是一对一关系
- 员工(ygong)与部门(Dept)是多对一关系
- 员工(ygong)与薪资(EmployeeSalary)是一对多关系
- 员工(ygong)与工时日志(LogInfo)是一对多关系
- 用户(User)与角色(Role)是多对多关系
- 角色(Role)与权限(Power)是多对多关系

## 部署要求

### 服务器环境要求

1. **硬件要求**
   - CPU: 至少4核心，推荐8核心
   - 内存: 至少8GB，推荐16GB
   - 存储: 至少100GB SSD
   - 网络: 千兆网卡，稳定的网络连接

2. **软件环境**
   - 操作系统: Ubuntu 20.04 LTS 或 CentOS 8
   - Python: 3.8+
   - Docker: 20.10+ (可选)
   - Docker Compose: 2.0+ (可选)

### 数据库配置

1. **MySQL配置**
   - 版本: MySQL 8.0+ 或 MariaDB 10.5+
   - 字符集: utf8mb4
   - 排序规则: utf8mb4_unicode_ci
   - 连接池: 建议启用

2. **数据库初始化**
   - 使用Flask-Migrate进行数据库迁移
   - 初始化管理员账户
   - 初始化基础数据

### 部署步骤

1. **环境准备**
   - 安装Python 3.8+
   - 安装MySQL 8.0+
   - 安装必要的系统依赖

2. **应用部署**
   - 克隆代码仓库
   - 创建虚拟环境
   - 安装依赖包
   - 配置数据库连接
   - 初始化数据库
   - 配置Web服务器
   - 启动应用

3. **Docker部署** (可选)
   - 使用提供的docker-compose.yaml文件
   - 配置环境变量
   - 启动容器
