from flask import Blueprint, render_template, request, jsonify, send_file
from flask_login import login_required, current_user
from sqlalchemy import desc
from datetime import datetime
import pandas as pd
from io import BytesIO

from applications.common import curd
from applications.common.curd import enable_status, disable_status
from applications.common.utils.http import table_api, fail_api, success_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.models import Role, Dept, EmployeeSalary, ygong
from applications.models import User, AdminLog

bp = Blueprint('employee_salary', __name__, url_prefix='/employee_salary')


@bp.get('/')
@login_required
def main():
    # 获取所有部门数据
    depts = Dept.query.all()
    return render_template('system/employee_salary/main.html', depts=depts)

@bp.get('/data')
@login_required
def data():
    # 获取请求参数
    name = request.args.get('name', '')
    month = request.args.get('month', '')  # 格式为YYYY-MM
    dept_id = request.args.get('dept_id', '')
    view_status = request.args.get('view_status', '')  # 获取确认状态筛选参数

    # 构建查询
    query = db.session.query(EmployeeSalary, ygong).join(
        ygong, EmployeeSalary.employee_id == ygong.id
    )

    # 添加筛选条件
    if name:
        query = query.filter(ygong.name.like(f'%{name}%'))
    if month:
        try:
            # 尝试将月份拆分为年份和月份
            year, month_num = month.split('-')
            # 移除前导零，以匹配数据库中的格式
            month_num = month_num.lstrip('0')
            # 使用精确匹配
            query = query.filter(EmployeeSalary.month == f"{year}-{month_num}")
        except Exception as e:
            # 如果拆分失败，可能是格式不正确，尝试其他匹配方式
            query = query.filter(
                db.or_(
                    EmployeeSalary.month.like(f"%{month}%"),  # 尝试模糊匹配
                    EmployeeSalary.month == month  # 尝试精确匹配
                )
            )
    if dept_id:
        query = query.filter(ygong.dept_id == dept_id)
    if view_status:
        query = query.filter(EmployeeSalary.view_status == view_status)

    total = query.count()
    data = query.paginate(
        page=request.args.get('page', 1, type=int),
        per_page=request.args.get('limit', 10, type=int)
    )

    result = []
    for item in data.items:
        # 确保 month 是 "YYYY-MM" 格式
        if '-' not in item.EmployeeSalary.month:
            # 如果 month 是 "MM" 格式，补全为当前年份
            current_year = datetime.now().year
            month_str = f"{current_year}-{item.EmployeeSalary.month.zfill(2)}"
        else:
            month_str = item.EmployeeSalary.month

        # 将 month 字符串转换为 datetime 对象，然后再格式化
        month_date = datetime.strptime(month_str, "%Y-%m")
        formatted_month = month_date.strftime('%Y年%m月')  # 显示年份和月份

        result.append({
            'id': item.EmployeeSalary.id,
            'name': item.ygong.name,
            'month': formatted_month,  # 使用格式化后的年份和月份
            'base_salary': item.EmployeeSalary.base_salary,
            'performance_salary': item.EmployeeSalary.performance_salary,
            'supervisor_assessment': item.EmployeeSalary.supervisor_assessment,
            'assessment_coefficient': item.EmployeeSalary.assessment_coefficient,
            'position_allowance': item.EmployeeSalary.position_allowance,
            'full_attendance': item.EmployeeSalary.full_attendance,
            'overtime_pay': item.EmployeeSalary.overtime_pay,
            'housing_subsidy': item.EmployeeSalary.housing_subsidy,
            'project_assessment': item.EmployeeSalary.project_assessment,
            'high_temp_allowance': item.EmployeeSalary.high_temp_allowance,
            'other_allowance': item.EmployeeSalary.other_allowance,
            'leave_deduction': item.EmployeeSalary.leave_deduction,
            'other_deduction': item.EmployeeSalary.other_deduction,
            'should_pay': item.EmployeeSalary.should_pay,
            'insurance_deduction': item.EmployeeSalary.insurance_deduction,
            'tax_deduction': item.EmployeeSalary.tax_deduction,
            'year_end_tax': item.EmployeeSalary.year_end_tax,
            'total_deduction': item.EmployeeSalary.total_deduction,
            'actual_salary': item.EmployeeSalary.actual_salary,
            'view_status': item.EmployeeSalary.view_status  # 添加工资条确认状态
        })

    return table_api(
        data=result,
        count=total
    )

@bp.post('/import')
@login_required
def import_salary():
    if 'file' not in request.files:
        return fail_api(msg="请选择文件")

    file = request.files['file']
    if not file.filename.endswith('.xlsx'):
        return fail_api(msg="请上传Excel文件")

    try:
        # 读取Excel时填充空值为0
        df = pd.read_excel(file, engine='openpyxl').fillna(0)

        # 检查必要的列是否存在
        required_columns = ['工号', '月份', '基本工资', '绩效工资', '主管考核项', '考核系数',
                          '职务津贴', '全勤', '加班费', '房补', '项目考核', '高温费',
                          '其他', '请假扣款', '其他扣款', '应发工资', '社保扣款', '个税',
                          '年终奖个税扣款', '扣款', '实发工资']

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return fail_api(msg=f"Excel文件缺少以下列: {', '.join(missing_columns)}")

        # 获取所有员工工号
        all_employees = {emp.employee_id: emp for emp in ygong.query.all()}

        # 获取Excel中的所有员工工号
        excel_employees = set(str(emp_id).strip() for emp_id in df['工号'].unique())

        # 找出不存在的员工
        missing_employees = excel_employees - set(all_employees.keys())

        if missing_employees:
            missing_list = '\n'.join(missing_employees)
            return fail_api(msg=f"以下员工工号不存在于系统中，请先在员工管理中添加这些员工：\n{missing_list}")

        success_count = 0
        update_count = 0
        error_records = []

        for index, row in df.iterrows():
            try:
                employee_id = str(row['工号']).strip()
                employee = all_employees.get(employee_id)

                if not employee:
                    error_records.append(f"第{index+2}行: 未找到工号为 '{employee_id}' 的员工")
                    continue

                # 处理月份格式
                month_str = str(row['月份']).strip()
                if '-' in month_str:
                    # 如果是 "YYYY-MM" 格式，直接使用
                    month = month_str
                else:
                    # 如果是 "MM" 格式，补全为当前年份
                    current_year = datetime.now().year
                    month = f"{current_year}-{month_str.zfill(2)}"

                # 验证月份是否有效
                try:
                    datetime.strptime(month, "%Y-%m")
                except ValueError:
                    error_records.append(f"第{index+2}行: 月份格式错误 '{row['月份']}'")
                    continue

                # 检查是否已存在该月份的工资记录
                existing_salary = EmployeeSalary.query.filter_by(
                    employee_id=employee.id,
                    month=month
                ).first()

                if existing_salary:
                    # 如果存在，更新记录
                    existing_salary.base_salary = float(row['基本工资'] or 0)
                    existing_salary.performance_salary = float(row['绩效工资'] or 0)
                    existing_salary.supervisor_assessment = float(row['主管考核项'] or 0)
                    existing_salary.assessment_coefficient = float(row['考核系数'] or 0)
                    existing_salary.position_allowance = float(row['职务津贴'] or 0)
                    existing_salary.full_attendance = float(row['全勤'] or 0)
                    existing_salary.overtime_pay = float(row['加班费'] or 0)
                    existing_salary.housing_subsidy = float(row['房补'] or 0)
                    existing_salary.project_assessment = float(row['项目考核'] or 0)
                    existing_salary.high_temp_allowance = float(row['高温费'] or 0)
                    existing_salary.other_allowance = float(row['其他'] or 0)
                    existing_salary.leave_deduction = float(row['请假扣款'] or 0)
                    existing_salary.other_deduction = float(row['其他扣款'] or 0)
                    existing_salary.should_pay = float(row['应发工资'] or 0)
                    existing_salary.insurance_deduction = float(row['社保扣款'] or 0)
                    existing_salary.tax_deduction = float(row['个税'] or 0)
                    existing_salary.year_end_tax = float(row['年终奖个税扣款'] or 0)
                    existing_salary.total_deduction = float(row['扣款'] or 0)
                    existing_salary.actual_salary = float(row['实发工资'] or 0)
                    update_count += 1
                else:
                    # 如果不存在，创建新记录
                    salary = EmployeeSalary(
                        employee_id=employee.id,
                        month=month,
                        base_salary=float(row['基本工资'] or 0),
                        performance_salary=float(row['绩效工资'] or 0),
                        supervisor_assessment=float(row['主管考核项'] or 0),
                        assessment_coefficient=float(row['考核系数'] or 0),
                        position_allowance=float(row['职务津贴'] or 0),
                        full_attendance=float(row['全勤'] or 0),
                        overtime_pay=float(row['加班费'] or 0),
                        housing_subsidy=float(row['房补'] or 0),
                        project_assessment=float(row['项目考核'] or 0),
                        high_temp_allowance=float(row['高温费'] or 0),
                        other_allowance=float(row['其他'] or 0),
                        leave_deduction=float(row['请假扣款'] or 0),
                        other_deduction=float(row['其他扣款'] or 0),
                        should_pay=float(row['应发工资'] or 0),
                        insurance_deduction=float(row['社保扣款'] or 0),
                        tax_deduction=float(row['个税'] or 0),
                        year_end_tax=float(row['年终奖个税扣款'] or 0),
                        total_deduction=float(row['扣款'] or 0),
                        actual_salary=float(row['实发工资'] or 0)
                    )
                    db.session.add(salary)
                    success_count += 1

            except Exception as e:
                error_records.append(f"第{index+2}行: 处理出错 - {str(e)}")
                continue

        if success_count > 0 or update_count > 0:
            db.session.commit()

        # 返回导入结果
        if error_records:
            if success_count > 0 or update_count > 0:
                return success_api(msg=f"成功导入{success_count}条记录，更新{update_count}条记录，但有以下错误：\n" + "\n".join(error_records))
            else:
                return fail_api(msg="导入失败：\n" + "\n".join(error_records))
        else:
            return success_api(msg=f"成功导入{success_count}条记录，更新{update_count}条记录")

    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"导入失败: {str(e)}")


@bp.get('/employees')
@login_required
def get_employees():
    """获取所有员工信息，用于调试"""
    employees = ygong.query.all()
    return jsonify({
        'employees': [{'id': emp.id, 'name': emp.name} for emp in employees]
    })


@bp.get('/status/<int:id>')
@login_required
def status(id):
    """显示员工工资条确认状态页面"""
    # 获取工资记录
    salary = EmployeeSalary.query.get_or_404(id)
    # 获取员工信息
    employee = ygong.query.get_or_404(salary.employee_id)

    # 格式化月份
    if '-' not in salary.month:
        current_year = datetime.now().year
        month_str = f"{current_year}-{salary.month.zfill(2)}"
    else:
        month_str = salary.month

    month_date = datetime.strptime(month_str, "%Y-%m")
    formatted_month = month_date.strftime('%Y年%m月')

    return render_template(
        'system/employee_salary/status.html',
        salary=salary,
        employee=employee,
        formatted_month=formatted_month
    )


@bp.post('/update_status')
@login_required
def update_status():
    """更新工资条确认状态"""
    # 获取请求数据
    data = request.get_json()
    salary_id = data.get('id')
    status = data.get('status')  # 'confirmed' 或 'disputed'
    dispute_reason = data.get('dispute_reason', '')  # 如果有异议，提供原因

    if not salary_id or not status:
        return fail_api(msg="参数错误")

    # 验证状态值
    if status not in ['confirmed', 'disputed']:
        return fail_api(msg="状态值无效")

    try:
        # 获取工资记录
        salary = EmployeeSalary.query.get_or_404(salary_id)

        # 更新状态
        salary.view_status = status

        # 如果有异议，记录异议原因（可以添加一个字段来存储）
        # 这里假设已经在模型中添加了 dispute_reason 字段
        # 如果没有，可以考虑添加该字段或使用其他方式存储

        # 保存更改
        db.session.commit()

        # 记录操作日志
        operation = "确认工资条" if status == "confirmed" else "对工资条提出异议"
        AdminLog.add_log(operation, "员工工资", current_user.id)

        return success_api(msg="状态更新成功")
    except Exception as e:
        db.session.rollback()
        return fail_api(msg=f"更新失败: {str(e)}")


