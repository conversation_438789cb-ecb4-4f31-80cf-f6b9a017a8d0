from flask import Blueprint, request, jsonify
from applications.extensions import db
from applications.models import LogInfo  # 假设有一个 LogInfo 模型
from datetime import datetime

bp = Blueprint('save_log', __name__)


@bp.route('/save_project_log', methods=['POST'])
def save_project_log():
    try:
        data = request.get_json()
        print("Received data:", data)  # 调试输出
        
        projectPrefix = data.get('projectPrefix')
        projectNumber = data.get('projectNumber')
        regularWorkingHours = float(data.get('regularWorkingHours', 0))
        overtimeWorkingHours = float(data.get('overtimeWorkingHours', 0))
        projectLocation = data.get('projectLocation')
        openid = data.get('openid')
        employee_id = data.get('employee_id')
        content = data.get('content')
        totalHours = float(data.get('totalHours', 0))
        status = data.get('status')
        name = data.get('name')  # Changed from 'approved' to 'status'
        work_date = data.get('work_date')  # 获取工作日期

        # 将字符串日期转换为日期对象
        work_date = datetime.strptime(work_date, '%Y-%m-%d').date()

        # Calculate total hours if not provided
        if totalHours == 0:
            totalHours = regularWorkingHours + overtimeWorkingHours

        print(f"Using totalHours: {totalHours}")  # Add logging

        # 获取当前时间戳
        created_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 使用 SQLAlchemy 插入数据
        new_log = LogInfo(
            projectPrefix=projectPrefix,
            projectNumber=projectNumber,
            regularWorkingHours=regularWorkingHours,
            overtimeWorkingHours=overtimeWorkingHours,
            projectLocation=projectLocation,
            openid=openid,
            employee_id=employee_id,
            content=content,
            totalHours=totalHours,
            created_at=created_at,
            status=status,
            name=name,
            work_date=work_date  # 添加工作日期
        )

        db.session.add(new_log)
        db.session.commit()

        return jsonify({'message': '项目日志保存成功', 'status': 200})
    except Exception as e:
        print("Error:", str(e))  # Add logging
        db.session.rollback()  # 回滚事务
        return jsonify({'message': f'项目日志保存失败: {str(e)}', 'status': 500})