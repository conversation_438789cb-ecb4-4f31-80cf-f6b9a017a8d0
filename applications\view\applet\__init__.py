from flask import Flask, Blueprint
from applications.view.applet.get_Project_info import bp as get_Project_info_bp
from applications.view.applet.get_log_list import bp as get_log_list_bp
from applications.view.applet.get_openid import bp as get_openid_bp
from applications.view.applet.register import bp as register_bp
from applications.view.applet.log_detail import bp as log_detail_bp
from applications.view.applet.save_log import bp as save_log_bp
from applications.view.applet.get_log import bp as get_log_bp
from applications.view.applet.employee_salary import bp as employee_salary_bp

applet_bp = Blueprint('applet', __name__, url_prefix='/applet')


def register_applet_bps(app: Flask):
    applet_bp.register_blueprint(get_Project_info_bp)
    applet_bp.register_blueprint(get_log_list_bp)
    applet_bp.register_blueprint(get_openid_bp)
    applet_bp.register_blueprint(register_bp)
    applet_bp.register_blueprint(log_detail_bp)
    applet_bp.register_blueprint(save_log_bp)
    applet_bp.register_blueprint(get_log_bp)
    applet_bp.register_blueprint(employee_salary_bp)
    app.register_blueprint(applet_bp)
