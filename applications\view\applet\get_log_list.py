from flask import Blueprint, request, jsonify, session, redirect
from applications.extensions import db
from applications.models import LogInfo  # 假设有一个 LogInfo 模型
from datetime import datetime, timedelta

bp = Blueprint('get_log_list', __name__)

@bp.route('/get_log_list', methods=['GET'])
def get_log_list():
    # if 'admin' not in session:
    #     return redirect('/admin/login')
    try:
        openid = request.args.get('openid')
        start_date = request.args.get('start_date', default=None)
        end_date = request.args.get('end_date', default=None)
        
        query = LogInfo.query.filter(LogInfo.openid == openid)
        
        if start_date and end_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1) - timedelta(seconds=1)  # 调整为当天的23:59:59
            query = query.filter(LogInfo.work_date.between(start_date, end_date))
        else:
            # 默认显示最近15天
            default_end_date = datetime.now() + timedelta(days=1) - timedelta(seconds=1)  # 调整为当天的23:59:59
            default_start_date = default_end_date - timedelta(days=14)  # 包含当天共15天
            query = query.filter(LogInfo.work_date.between(default_start_date, default_end_date))
        
        logs = query.all()
        results = [{
            'id': log.id,
            'projectPrefix': log.projectPrefix,
            'projectNumber': log.projectNumber,
            'regularWorkingHours': log.regularWorkingHours,
            'overtimeWorkingHours': log.overtimeWorkingHours,
            'projectLocation': log.projectLocation,
            'openid': log.openid,
            'content': log.content,
            'totalHours': log.totalHours,
            'created_at': log.created_at.isoformat() if log.created_at else None,
            'status': log.status,
            'work_date': log.work_date.strftime('%Y-%m-%d') if log.work_date else None  # 确保返回 work_date
        } for log in logs]
        
        return jsonify({'data': results, 'status': 200})
    except Exception as e:
        return jsonify({'message': f'获取日志列表失败: {str(e)}', 'status': 500})