import copy
from collections import OrderedDict

from flask import jsonify, current_app, Blueprint, render_template
from flask_login import login_required, current_user

from ...common.utils.http import table_api
from ...models import Power
from ...schemas import PowerOutSchema
from ...models import LogInfo
from applications.view.system.Statistical_data import get_current_month_range, calculate_overtime_pay
from applications.models import EmployeeSalary, ygong, Dept
from applications.extensions import db
from collections import defaultdict
from datetime import datetime, timedelta
import calendar

bp = Blueprint('rights', __name__, url_prefix='/rights')


# 渲染配置
@bp.get('/configs')
@login_required
def configs():
    # 网站配置
    config = dict(logo={
        # 网站名称
        "title": current_app.config.get("SYSTEM_NAME"),
        # 网站图标
        "image": "/static/system/admin/images/logo.png"
        # 菜单配置
    }, menu={
        # 菜单数据来源
        "data": "/system/rights/menu",
        "collaspe": False,
        # 是否同时只打开一个菜单目录
        "accordion": True,
        "method": "GET",
        # 是否开启多系统菜单模式
        "control": False,
        # 顶部菜单宽度 PX
        "controlWidth": 500,
        # 默认选中的菜单项
        "select": "0",
        # 是否开启异步菜单，false 时 data 属性设置为菜单数据，false 时为 json 文件或后端接口
        "async": True
    }, tab={
        # 是否开启多选项卡
        "enable": True,
        # 切换选项卡时，是否刷新页面状态
        "keepState": True,
        # 是否开启 Tab 记忆
        "session": True,
        # 预加载
        "preload": False,
        # 最大可打开的选项卡数量
        "max": 30,
        "index": {
            # 标识 ID , 建议与菜单项中的 ID 一致
            "id": "10",
            # 页面地址
            "href": "/system/rights/welcome",
            # 标题
            "title": "首页"
        }
    }, theme={
        # 默认主题色，对应 colors 配置中的 ID 标识
        "defaultColor": "2",
        # 默认的菜单主题 dark-theme 黑 / light-theme 白
        "defaultMenu": "dark-theme",
        # 是否允许用户切换主题，false 时关闭自定义主题面板
        "allowCustom": True
    }, colors=[{
        "id": "1",
        "color": "#2d8cf0"
    },
        {
            "id": "2",
            "color": "#5FB878"
        },
        {
            "id": "3",
            "color": "#1E9FFF"
        }, {
            "id": "4",
            "color": "#FFB800"
        }, {
            "id": "5",
            "color": "darkgray"
        }
    ], links=current_app.config.get("SYSTEM_PANEL_LINKS"), other={
        # 主页动画时长
        "keepLoad": 0,
        # 布局顶部主题
        "autoHead": False
    }, header={
        'message': '/system/rights/message'
    })
    return jsonify(config)


# 消息
@bp.get('/message')
@login_required
def message():
    # 获取当前用户的 dept_id
    current_user_dept_id = current_user.dept_id
    
    # 如果是管理员(superadmin)，显示所有待办
    if current_user.username == current_app.config.get("SUPERADMIN"):
        pending_logs = LogInfo.query.filter_by(status='pending').all()
    else:
        # 非管理员只能看到本部门员工的待办
        if current_user_dept_id:
            # 查询部门内所有员工
            yg_users = ygong.query.filter_by(dept_id=current_user_dept_id).all()
            yg_openids = [yg.openid for yg in yg_users]  # 获取这些员工的 openid
            
            # 只获取本部门员工的待办日志
            pending_logs = LogInfo.query.filter(
                LogInfo.status == 'pending',
                LogInfo.openid.in_(yg_openids)
            ).all()
        else:
            # 没有部门ID的用户不显示任何待办
            pending_logs = []
    
    # 添加是否有待办事项的标识
    has_pending = len(pending_logs) > 0
    
    return dict(
        code=200,
        data=[
            {
                "id": 1,
                "title": "代办",
                "children": [{
                        'id': log.id,
                        "avatar": "https://gw.alipayobjects.com/zos/rmsportal/ThXAXghbEsBCCSDihZxY.png",
                        'title': f'日志需要审批 - {log.name}',
                        'content': f'项目编号: {log.projectNumber}',
                        'type': 'log_approval',
                        'time': log.created_at.strftime("%Y-%m-%d %H:%M")
                    } for log in pending_logs]
            }
        ],
        has_pending=has_pending  # 新增字段
    )


# 菜单
@bp.get('/menu')
@login_required
def menu():
    if current_user.username != current_app.config.get("SUPERADMIN"):
        role = current_user.role
        powers = []
        for i in role:
            # 如果角色没有被启用就直接跳过
            if i.enable == 0:
                continue
            # 变量角色用户的权限
            for p in i.power:
                # 如果权限关闭了就直接跳过
                if p.enable == 0:
                    continue
                # 一二级菜单
                if int(p.type) in [0, 1] and p not in powers:
                    powers.append(p)

        power_schema = PowerOutSchema(many=True)  # 用已继承 ma.ModelSchema 类的自定制类生成序列化类
        power_dict = power_schema.dump(powers)  # 生成可序列化对象
        power_dict.sort(key=lambda x: (x['parent_id'], x['id']), reverse=True)
        
        # 过滤禁用的菜单项
        power_dict = [item for item in power_dict if item.get('enable') == 1]

        menu_dict = OrderedDict()
        for _dict in power_dict:
            if _dict['id'] in menu_dict:
                # 当前节点添加子节点
                _dict['children'] = copy.deepcopy(menu_dict[_dict['id']])
                # 过滤禁用的子菜单
                if 'children' in _dict and _dict['children']:
                    _dict['children'] = [child for child in _dict['children'] if child.get('enable') == 1]
                    _dict['children'].sort(key=lambda item: item['sort'])
                # 删除子节点
                del menu_dict[_dict['id']]

            if _dict['parent_id'] not in menu_dict:
                menu_dict[_dict['parent_id']] = [_dict]
            else:
                menu_dict[_dict['parent_id']].append(_dict)
        return jsonify(sorted(menu_dict.get(0), key=lambda item: item['sort']))
    else:
        powers = Power.query.all()
        power_schema = PowerOutSchema(many=True)  # 用已继承 ma.ModelSchema 类的自定制类生成序列化类
        power_dict = power_schema.dump(powers)  # 生成可序列化对象
        power_dict.sort(key=lambda x: (x['parent_id'], x['id']), reverse=True)
        
        # 超级管理员也应该遵循菜单启用状态的设置
        power_dict = [item for item in power_dict if item.get('enable') == 1]

        menu_dict = OrderedDict()
        for _dict in power_dict:
            if _dict['id'] in menu_dict:
                # 当前节点添加子节点
                _dict['children'] = copy.deepcopy(menu_dict[_dict['id']])
                # 过滤禁用的子菜单
                if 'children' in _dict and _dict['children']:
                    _dict['children'] = [child for child in _dict['children'] if child.get('enable') == 1]
                    _dict['children'].sort(key=lambda item: item['sort'])
                # 删除子节点
                del menu_dict[_dict['id']]

            if _dict['parent_id'] not in menu_dict:
                menu_dict[_dict['parent_id']] = [_dict]
            else:
                menu_dict[_dict['parent_id']].append(_dict)
        return jsonify(sorted(menu_dict.get(0), key=lambda item: item['sort']))


# 控制台页面
@bp.get('/welcome')
@login_required
def welcome():
    # # 获取当前月的日期范围
    # current_month_start, current_month_end = get_current_month_range()
    
    # # 计算当月的工作日总工时
    # year = current_month_start.year
    # month = current_month_start.month
    # workdays = calendar.monthrange(year, month)[1]  # 获取当月天数
    # total_work_hours = workdays * 8  # 每天8小时

    # # 获取所有部门数据
    # depts = Dept.query.order_by(Dept.sort).all()
    
    # # 如果是管理员，获取所有员工
    # if current_user.username == 'admin':
    #     employees = ygong.query.all()
    # else:
    #     # 获取当前用户所属部门
    #     current_dept_id = current_user.dept_id
        
    #     # 获取当前部门及其所有子部门
    #     subordinate_depts = get_subordinate_depts(current_dept_id)
        
    #     # 获取这些部门的所有员工
    #     employees = ygong.query.filter(ygong.dept_id.in_(subordinate_depts)).all()
    
    # # 查询当前月的工资数据
    # current_month_salaries = EmployeeSalary.query.filter(
    #     EmployeeSalary.create_at >= current_month_start,
    #     EmployeeSalary.create_at <= current_month_end
    # ).all()

    # # 如果没有当前月数据，使用最新一个月的数据
    # if not current_month_salaries:
    #     # 获取最新一个月的工资数据
    #     latest_salary = EmployeeSalary.query.order_by(EmployeeSalary.create_at.desc()).first()
    #     if latest_salary:
    #         latest_month_start = latest_salary.create_at.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    #         latest_month_end = latest_month_start + timedelta(days=32)
    #         latest_month_end = latest_month_end.replace(day=1) - timedelta(days=1)
    #         current_month_salaries = EmployeeSalary.query.filter(
    #             EmployeeSalary.create_at >= latest_month_start,
    #             EmployeeSalary.create_at <= latest_month_end
    #         ).all()

    # # 如果没有数据，返回空数据并提示
    # if not current_month_salaries:
    #     return render_template('system/Statistical_data/current.html', 
    #                          employee_logs=[],
    #                          dept_pay=[],
    #                          message="没有找到任何数据，请检查数据库或联系管理员。")

    # # 计算当前月的工资率
    # salary_rate = {}
    # for salary in current_month_salaries:
    #     # 通过 ygong 表找到对应的 openid
    #     employee = ygong.query.filter_by(id=salary.employee_id).first()
    #     if not employee:
    #         continue
        
    #     # 通过 openid 查询日志
    #     current_month_logs = LogInfo.query.filter(
    #         LogInfo.openid == employee.openid,
    #         LogInfo.work_date >= current_month_start,
    #         LogInfo.work_date <= current_month_end
    #     ).all()
        
    #     if total_work_hours > 0:
    #         # 计算工资率
    #         rate = (salary.actual_salary - salary.overtime_pay) / total_work_hours
    #         salary_rate[salary.employee_id] = rate

    # # 获取每个员工的日志数据，只保留有日志的员工
    # employee_logs = []
    # dept_overtime_pay = defaultdict(float)
    # dept_regular_pay = defaultdict(float)

    # # 新增：按 projectLocation 分类统计
    # location_stats = defaultdict(lambda: {
    #     'total_regular': 0,
    #     'total_overtime': 0,
    #     'total_overtime_pay': 0,
    #     'total_regular_pay': 0
    # })

    # for emp in employees:
    #     # 获取员工的基本工资
    #     salary = EmployeeSalary.query.filter_by(employee_id=emp.id).order_by(EmployeeSalary.create_at.desc()).first()
    #     if not salary:
    #         continue
        
    #     base_salary = salary.base_salary if salary else 0
        
    #     # 查询当前月的日志
    #     logs = LogInfo.query.filter(
    #         LogInfo.openid == emp.openid,
    #         LogInfo.work_date >= current_month_start,
    #         LogInfo.work_date <= current_month_end
    #     ).all()
        
    #     if logs:  # 只处理有日志的员工
    #         # 原有员工日志统计逻辑保持不变
    #         current_month_regular = sum(log.regularWorkingHours for log in logs)
    #         rate = salary_rate.get(emp.id, 0)
    #         regular_pay = round(current_month_regular * rate)
    #         total_regular = sum(log.regularWorkingHours for log in logs)
    #         total_overtime = sum(log.overtimeWorkingHours for log in logs)
    #         total_overtime_pay = sum(calculate_overtime_pay(log, base_salary, log.work_date) for log in logs)
            
    #         dept_overtime_pay[emp.dept_id] += total_overtime_pay
    #         dept_regular_pay[emp.dept_id] += regular_pay
            
    #         employee_logs.append({
    #             'id': emp.id,
    #             'name': emp.name,
    #             'dept_id': emp.dept_id, 
    #             'regularWorkingHours': total_regular,
    #             'overtimeWorkingHours': total_overtime,
    #             'overtime_pay': total_overtime_pay,
    #             'regular_pay': regular_pay
    #         })

    #         # 新增：按工作地点分类统计
    #         for log in logs:
    #             location = log.projectLocation if log.projectLocation else '未知'
    #             overtime_pay = calculate_overtime_pay(log, base_salary, log.work_date)
    #             regular_pay = round(log.regularWorkingHours * rate)
                
    #             location_stats[location]['total_regular'] += log.regularWorkingHours
    #             location_stats[location]['total_overtime'] += log.overtimeWorkingHours
    #             location_stats[location]['total_overtime_pay'] += overtime_pay
    #             location_stats[location]['total_regular_pay'] += regular_pay

    # # 将 location_stats 转换为列表
    # location_data = [{
    #     'location': location,
    #     'total_regular': stats['total_regular'],
    #     'total_overtime': stats['total_overtime'],
    #     'total_overtime_pay': stats['total_overtime_pay'],
    #     'total_regular_pay': stats['total_regular_pay']
    # } for location, stats in location_stats.items()]

    # # 原有部门费用统计逻辑保持不变
    # dept_pay_list = [
    #     {
    #         'dept_id': dept_id,
    #         'dept_name': Dept.query.get(dept_id).dept_name if Dept.query.get(dept_id) else '未知部门',
    #         'total_overtime_pay': dept_overtime_pay.get(dept_id, 0),
    #         'total_regular_pay': dept_regular_pay.get(dept_id, 0),
    #         'total_pay': dept_overtime_pay.get(dept_id, 0) + dept_regular_pay.get(dept_id, 0)
    #     }
    #     for dept_id in set(dept_overtime_pay.keys()).union(dept_regular_pay.keys())
    # ]

    # # 员工日志饼图数据
    # employee_pie_data = []
    # for log in employee_logs:
    #     employee_pie_data.append({
    #         'name': log['name'],
    #         'value': log['regularWorkingHours'] + log['overtimeWorkingHours']
    #     })

    # # 部门费用饼图数据
    # dept_pie_data = []
    # for dept in dept_pay_list:
    #     dept_pie_data.append({
    #         'name': dept['dept_name'],
    #         'value': dept['total_pay']
    #     })

    # # 工作地点分类饼图数据
    # location_pie_data = []
    # for loc in location_data:
    #     location_pie_data.append({
    #         'name': loc['location'],
    #         'value': loc['total_regular'] + loc['total_overtime']
    #     })

    # return render_template('system/Statistical_data/current.html', 
    #                      employee_logs=employee_logs,
    #                      dept_pay=dept_pay_list,
    #                      location_data=location_data,
    #                      employee_pie_data=employee_pie_data,  # 新增
    #                      dept_pie_data=dept_pie_data,  # 新增
    #                      location_pie_data=location_pie_data)  # 新增
    return render_template('system/analysis/main.html')