<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门预估成本</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
</head>
<body class="pear-container">
    {# 查询表单 #}
    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form" action="" lay-filter="yuguinfo-query-form">
                <div class="layui-form-item" style="margin-bottom: unset;">
                    <label class="layui-form-label">项目编号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="project_code" placeholder="请输入项目编号" class="layui-input">
                    </div>
                    <label class="layui-form-label">部门名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="dept_name" placeholder="请输入部门名称" class="layui-input">
                    </div>
                    <label class="layui-form-label">工作地点</label>
                    <div class="layui-input-inline">
                        <input type="text" name="location" placeholder="请输入工作地点" class="layui-input">
                    </div>
                    <button class="layui-btn layui-btn-md" lay-submit lay-filter="yuguinfo-query">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </form>
        </div>
    </div>

    {# 预估工时表格 #}
    <div class="layui-card">
        <div class="layui-card-body">
            <h3>预估成本 - 当前部门：{{ dept_name }}</h3>
            <table id="yuguinfo-table" lay-filter="yuguinfo-table"></table>
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}

<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;

        console.log("预估工时数据:", {{ estimates | tojson | safe }});  // 打印调试信息

        // 初始化表格
        table.render({
            elem: '#yuguinfo-table',
            data: {{ estimates | tojson | safe }},
            page: true,
            cols: [
                [
                    {title: '项目类型', field: 'project_type', align: 'center'},
                    {title: '项目编号', field: 'project_code', align: 'center'},
                    {title: '项目名称', field: 'project_name', align: 'center'},
                    {title: '部门名称', field: 'dept_name', align: 'center'},
                    {title: '工作地点', field: 'location', align: 'center'},
                    {title: '预估工时', field: 'estimate_hours', align: 'center'},
                    {title: '预估人工成本', field: 'labor_cost', align: 'center'},
                    {title: '预估机械BOM成本', field: 'mechanical_bom_cost', align: 'center'},
                    {title: '预估电气BOM成本', field: 'electrical_bom_cost', align: 'center'},
                    {title: '预估其他成本', field: 'other_cost', align: 'center'},
                    {title: '创建时间', field: 'created_at', align: 'center'}
                ]
            ],
            skin: 'line',
            text: {none: '暂无预估工时数据'},
            defaultToolbar: ['filter', 'print', 'exports']
        });

        // 查询表单提交
        form.on('submit(yuguinfo-query)', function (data) {
            let field = data.field;
            let filteredData = {{ estimates | tojson | safe }}.filter(estimate => {
                return (!field.project_code || estimate.project_code.includes(field.project_code)) &&
                       (!field.dept_name || estimate.dept_name.includes(field.dept_name)) &&
                       (!field.location || estimate.location.includes(field.location));
            });
            table.reload('yuguinfo-table', { data: filteredData });
            return false;
        });
    });
</script>
</html>

