import datetime
from datetime import date


class WorkingHoursCalculator:
    """工时计算器"""
    
    @staticmethod
    def is_weekend(work_date):
        """
        判断是否为周末
        
        Args:
            work_date (str/date/datetime): 工作日期
            
        Returns:
            bool: True表示周末，False表示工作日
        """
        if isinstance(work_date, str):
            work_date = datetime.datetime.strptime(work_date, '%Y-%m-%d').date()
        elif isinstance(work_date, datetime.datetime):
            work_date = work_date.date()
        
        # weekday(): 0=Monday, 6=Sunday
        # 5=Saturday, 6=Sunday
        return work_date.weekday() >= 5
    
    @staticmethod
    def is_holiday(work_date, holidays=None):
        """
        判断是否为法定节假日
        
        Args:
            work_date (str/date/datetime): 工作日期
            holidays (list): 法定节假日列表，格式为['2025-01-01', '2025-02-10', ...]
            
        Returns:
            bool: True表示节假日，False表示非节假日
        """
        if holidays is None:
            holidays = []
            
        if isinstance(work_date, str):
            date_str = work_date
        elif isinstance(work_date, date):
            date_str = work_date.strftime('%Y-%m-%d')
        elif isinstance(work_date, datetime.datetime):
            date_str = work_date.strftime('%Y-%m-%d')
        else:
            return False
            
        return date_str in holidays
    
    @staticmethod
    def calculate_working_hours(total_hours, work_date, holidays=None):
        """
        计算正常工时和加班工时
        
        业务规则：
        - 正常工作日（周一到周五）：
          * 总工时 ≤ 8小时：全部计入regularWorkingHours，overtimeWorkingHours = 0
          * 总工时 > 8小时：8小时计入regularWorkingHours，超出部分计入overtimeWorkingHours
        - 双休日（周六、周日）：
          * 无论多少工时：全部计入overtimeWorkingHours，regularWorkingHours = 0
        - 法定节假日：
          * 无论多少工时：全部计入overtimeWorkingHours，regularWorkingHours = 0
        
        Args:
            total_hours (float): 总工时
            work_date (str/date/datetime): 工作日期
            holidays (list): 法定节假日列表（可选）
            
        Returns:
            dict: {
                'regularWorkingHours': float,
                'overtimeWorkingHours': float
            }
        """
        total_hours = float(total_hours)
        
        # 判断是否为周末或节假日
        if (WorkingHoursCalculator.is_weekend(work_date) or 
            WorkingHoursCalculator.is_holiday(work_date, holidays)):
            # 双休日或节假日：全部为加班工时
            return {
                'regularWorkingHours': 0.0,
                'overtimeWorkingHours': total_hours
            }
        else:
            # 正常工作日
            if total_hours <= 8.0:
                # 不满8小时：全部为正常工时
                return {
                    'regularWorkingHours': total_hours,
                    'overtimeWorkingHours': 0.0
                }
            else:
                # 超过8小时：8小时正常工时，其余为加班工时
                return {
                    'regularWorkingHours': 8.0,
                    'overtimeWorkingHours': total_hours - 8.0
                }
    
    @staticmethod
    def validate_working_hours(total_hours, regular_hours, overtime_hours):
        """
        验证工时数据的合理性
        
        Args:
            total_hours (float): 总工时
            regular_hours (float): 正常工时
            overtime_hours (float): 加班工时
            
        Returns:
            dict: {
                'valid': bool,
                'message': str
            }
        """
        try:
            total_hours = float(total_hours)
            regular_hours = float(regular_hours)
            overtime_hours = float(overtime_hours)
            
            # 检查工时是否为负数
            if total_hours < 0 or regular_hours < 0 or overtime_hours < 0:
                return {
                    'valid': False,
                    'message': '工时不能为负数'
                }
            
            # 检查总工时是否等于正常工时+加班工时
            if abs(total_hours - (regular_hours + overtime_hours)) > 0.01:
                return {
                    'valid': False,
                    'message': '总工时应等于正常工时加加班工时'
                }
            
            # 检查工时是否过大（一天最多24小时）
            if total_hours > 24:
                return {
                    'valid': False,
                    'message': '单日工时不能超过24小时'
                }
            
            # 检查正常工时是否超过8小时
            if regular_hours > 8:
                return {
                    'valid': False,
                    'message': '正常工时不能超过8小时'
                }
            
            return {
                'valid': True,
                'message': '工时数据验证通过'
            }
            
        except (ValueError, TypeError):
            return {
                'valid': False,
                'message': '工时数据格式错误'
            }


# 使用示例和测试用例
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        {
            'name': '正常工作日-不满8小时',
            'input': {'total_hours': 6.0, 'work_date': '2025-05-26'},  # 周一
            'expected': {'regularWorkingHours': 6.0, 'overtimeWorkingHours': 0.0}
        },
        {
            'name': '正常工作日-正好8小时',
            'input': {'total_hours': 8.0, 'work_date': '2025-05-27'},  # 周二
            'expected': {'regularWorkingHours': 8.0, 'overtimeWorkingHours': 0.0}
        },
        {
            'name': '正常工作日-超过8小时',
            'input': {'total_hours': 10.5, 'work_date': '2025-05-28'},  # 周三
            'expected': {'regularWorkingHours': 8.0, 'overtimeWorkingHours': 2.5}
        },
        {
            'name': '周六-任意工时',
            'input': {'total_hours': 6.0, 'work_date': '2025-05-24'},  # 周六
            'expected': {'regularWorkingHours': 0.0, 'overtimeWorkingHours': 6.0}
        },
        {
            'name': '周日-任意工时',
            'input': {'total_hours': 12.0, 'work_date': '2025-05-25'},  # 周日
            'expected': {'regularWorkingHours': 0.0, 'overtimeWorkingHours': 12.0}
        }
    ]
    
    print("工时计算器测试结果：")
    print("=" * 50)
    
    for test_case in test_cases:
        result = WorkingHoursCalculator.calculate_working_hours(
            test_case['input']['total_hours'],
            test_case['input']['work_date']
        )
        
        passed = (result == test_case['expected'])
        status = "✅ 通过" if passed else "❌ 失败"
        
        print(f"{test_case['name']}: {status}")
        print(f"  输入: {test_case['input']}")
        print(f"  期望: {test_case['expected']}")
        print(f"  实际: {result}")
        print()
