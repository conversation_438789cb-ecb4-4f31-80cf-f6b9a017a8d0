from flask import Flask, render_template, Blueprint, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from applications.common.utils.rights import authorize
from applications.services.cost_calculation import (
    calculate_labor_cost_by_timesheet, calculate_labor_cost_by_salary,
    import_bom_cost, import_other_cost, get_project_actual_cost,
    get_project_monthly_costs, get_dashboard_metrics,
    get_cost_structure_data, get_status_distribution_data
)
from applications.models.import_project import Import_project
from applications.models.admin_dict import DictType, DictData
from applications.models.project_cost import ProjectActualCost, BOMCostImport, OtherCostImport, ProjectLaborCost
from applications.models.project_manage_dept import ProjectManageDept
from applications.common.utils.http import success_api, fail_api
from applications.extensions.init_sqlalchemy import db
import datetime
import os
import pandas as pd
from werkzeug.utils import secure_filename
from dateutil.relativedelta import relativedelta
import uuid  # 导入uuid模块用于生成随机字符串
from io import BytesIO


bp = Blueprint('project_cost', __name__, url_prefix='/project_cost')


@bp.route('/')
@login_required
@authorize('system:project_cost:main')
def index():
    """项目成本分析主页"""
    # 获取项目状态列表供筛选
    statuses = DictData.query.filter_by(
        type_code='project_status',
        enable=1
    ).order_by(DictData.data_value).all()

    # 获取项目类型部门列表供筛选
    project_departments = ProjectManageDept.query.filter_by(
        status=1  # 只获取开启状态的部门
    ).order_by(ProjectManageDept.sort).all()

    return render_template(
        'system/project_cost/main.html',
        statuses=statuses,
        project_departments=project_departments
    )


@bp.route('/dept_cost')
@login_required
@authorize("system:project_cost:dept")
def dept_cost():
    """部门成本分析页面"""
    # 获取项目状态列表供筛选
    statuses = DictData.query.filter_by(
        type_code='project_status',
        enable=1
    ).order_by(DictData.data_value).all()

    # 获取项目类型部门列表供筛选
    project_departments = ProjectManageDept.query.filter_by(
        status=1  # 只获取开启状态的部门
    ).order_by(ProjectManageDept.sort).all()

    return render_template(
        'system/project_cost/dept_cost.html',
        statuses=statuses,
        project_departments=project_departments
    )


@bp.route('/project_info')
@login_required
@authorize("system:project_cost:project_info")
def project_info():
    """项目成本汇总页面"""
    # 获取项目状态列表供筛选
    statuses = DictData.query.filter_by(
        type_code='project_status',
        enable=1
    ).order_by(DictData.data_value).all()

    # 获取项目类型部门列表供筛选
    project_departments = ProjectManageDept.query.filter_by(
        status=1  # 只获取开启状态的部门
    ).order_by(ProjectManageDept.sort).all()

    return render_template(
        'system/project_cost/project_info.html',
        statuses=statuses,
        project_departments=project_departments
    )


@bp.route('/detail/<int:project_id>')
@login_required
def detail(project_id):
    """项目成本详情页"""
    project = Import_project.query.get_or_404(project_id)
    return render_template(
        'system/project_cost/detail_new.html',
        project=project
    )


@bp.route('/import/bom')
@login_required
@authorize('system:project_cost:import')
def import_bom_page():
    """BOM成本导入页面"""
    # 获取所有项目列表（不再根据项目状态筛选）
    projects = Import_project.query.all()
    return render_template(
        'system/project_cost/import_bom.html',
        projects=projects
    )


@bp.route('/import/other')
@login_required
@authorize('system:project_cost:import')
def import_other_page():
    """其他成本导入页面"""
    # 获取所有项目列表（不再根据项目状态筛选）
    projects = Import_project.query.all()
    return render_template(
        'system/project_cost/import_other.html',
        projects=projects
    )


@bp.route('/api/dashboard/metrics')
@login_required
def api_dashboard_metrics():
    """获取仪表盘指标API"""
    # 获取查询参数
    compare_type = request.args.get('compare', 'year')  # year: 同比, month: 环比

    # 移除对时间范围的依赖，使用当前时间
    now = datetime.datetime.now()
    year, month = now.year, now.month

    # 计算指标
    metrics = get_dashboard_metrics(
        current_year=year,
        current_month=month,
        compare_with_last_year=(compare_type == 'year')
    )

    return jsonify(metrics)


@bp.route('/api/projects')
@login_required
def api_projects():
    """获取项目列表API"""
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # 获取筛选条件
    status = request.args.get('status', '')  # 对应前端name="status"
    dept_id = request.args.get('dept_id', '')  # 获取项目部门ID
    keyword = request.args.get('keyword', '')

    # 时间戳参数，用于防止缓存
    _ = request.args.get('_t', None)

    # 构建查询
    query = Import_project.query

    # 应用筛选条件
    if status:
        query = query.filter_by(project_status=status)

    if dept_id:
        query = query.filter_by(dept_id=dept_id)

    if keyword:
        query = query.filter(
            (Import_project.project_name.like(f'%{keyword}%')) |
            (Import_project.project_code.like(f'%{keyword}%'))
        )

    # 执行分页查询，确保获取最新数据
    db.session.commit()  # 确保使用最新的数据库状态
    paginate = query.layui_paginate(page=page, limit=limit)

    # 准备返回数据
    items = []
    for project in paginate.items:
        # 获取项目状态名称
        status_dict = DictData.query.filter_by(
            type_code='project_status',
            data_value=str(project.project_status)
        ).first()
        status_name = status_dict.data_label if status_dict else '未知状态'

        # 获取项目类型名称
        project_type_obj = ProjectManageDept.query.filter_by(id=project.dept_id).first()
        project_type_name = project_type_obj.dept_name if project_type_obj else ''

        # 获取实际成本数据，强制从数据库获取最新数据
        db.session.refresh(project)  # 刷新项目对象
        actual_cost = get_project_actual_cost(project.id)

        # 计算预估总成本（BOM成本+人工成本+其他成本）
        total_estimate_cost = (project.bom_cost or 0) + (project.labor_cost or 0) + (project.other_cost or 0)

        # 计算成本占比
        cost_percent = 0
        try:
            if total_estimate_cost > 0:
                cost_percent = (actual_cost['total_cost'] / total_estimate_cost) * 100
            elif actual_cost['total_cost'] > 0:
                # 如果预算成本为0但实际成本大于0，设置为100%（表示全部超支）
                cost_percent = 100
        except (ZeroDivisionError, TypeError):
            # 处理除以0或类型错误的情况
            cost_percent = 0 if actual_cost['total_cost'] == 0 else 100

        # 判断是否为异常项目
        is_abnormal = cost_percent > 110  # 超过预估10%视为异常

        items.append({
            'id': project.id,
            'project_name': project.project_name,
            'project_code': project.project_code,
            'project_type_name': project_type_name,
            'project_status': project.project_status,
            'status_name': status_name,
            'price': project.price,
            'machine_number': project.machine_number,
            'estimate_cost': total_estimate_cost,  # 预估总成本（BOM成本+人工成本+其他成本）
            'bom_cost': project.bom_cost or 0,     # 预估BOM成本（来自Import_project表）
            'labor_cost': project.labor_cost or 0,  # 预估人工成本（来自Import_project表）
            'other_cost': project.other_cost or 0,  # 预估其他成本（来自Import_project表）
            'actual_bom_cost': actual_cost['bom_cost'],  # 实际BOM成本（来自ProjectActualCost表）
            'actual_labor_cost': actual_cost['labor_cost'],  # 实际人工成本（来自ProjectActualCost表）
            'actual_other_cost': actual_cost['other_cost'],  # 实际其他成本（来自ProjectActualCost表）
            'actual_cost': actual_cost['total_cost'],  # 实际总成本（来自ProjectActualCost表）
            'cost_percent': round(cost_percent, 2),
            'is_abnormal': is_abnormal
        })

    return jsonify({
        'code': 0,
        'msg': '',
        'count': paginate.total,
        'data': items
    })


@bp.route('/api/project/<int:project_id>')
@login_required
def api_project_detail(project_id):
    """获取项目详情API"""
    project = Import_project.query.get_or_404(project_id)

    # 获取项目状态名称
    status_dict = DictData.query.filter_by(
        type_code='project_status',
        data_value=str(project.project_status)
    ).first()
    status_name = status_dict.data_label if status_dict else '未知状态'

    # 获取项目类型名称
    project_type_obj = ProjectManageDept.query.filter_by(id=project.dept_id).first()
    project_type_name = project_type_obj.dept_name if project_type_obj else ''

    # 获取实际成本数据
    actual_cost = get_project_actual_cost(project.id)

    # 计算预估总成本（BOM成本+人工成本+其他成本）
    total_estimate_cost = (project.bom_cost or 0) + (project.labor_cost or 0) + (project.other_cost or 0)

    # 计算成本占比
    cost_percent = 0
    try:
        if total_estimate_cost > 0:
            cost_percent = (actual_cost['total_cost'] / total_estimate_cost) * 100
        elif actual_cost['total_cost'] > 0:
            # 如果预算成本为0但实际成本大于0，设置为100%（表示全部超支）
            cost_percent = 100
    except (ZeroDivisionError, TypeError):
        # 处理除以0或类型错误的情况
        cost_percent = 0 if actual_cost['total_cost'] == 0 else 100

    # 计算同比变化
    # 这里假设我们有12个月前的数据
    one_year_ago = datetime.datetime.now() - relativedelta(years=1)
    last_year_costs = ProjectActualCost.query.filter_by(
        project_id=project.id
    ).filter(
        ProjectActualCost.created_at <= one_year_ago
    ).all()

    last_year_total = sum(cost.total_cost for cost in last_year_costs) if last_year_costs else 0
    last_year_bom = sum(cost.bom_cost for cost in last_year_costs) if last_year_costs else 0
    last_year_labor = sum(cost.labor_cost for cost in last_year_costs) if last_year_costs else 0
    last_year_other = sum(cost.other_cost for cost in last_year_costs) if last_year_costs else 0

    # 计算变化率
    def calc_change(current, previous):
        if previous == 0:
            return 100 if current > 0 else 0
        return ((current - previous) / previous) * 100

    total_change = calc_change(actual_cost['total_cost'], last_year_total)
    bom_change = calc_change(actual_cost['bom_cost'], last_year_bom)
    labor_change = calc_change(actual_cost['labor_cost'], last_year_labor)
    other_change = calc_change(actual_cost['other_cost'], last_year_other)

    # 构建返回数据
    result = {
        'id': project.id,
        'project_name': project.project_name,
        'project_code': project.project_code,
        'project_type_name': project_type_name,
        'project_status': project.project_status,
        'status_name': status_name,
        'price': project.price,
        'machine_number': project.machine_number,
        'estimate_cost': total_estimate_cost,  # 预估总成本（BOM成本+人工成本+其他成本）
        'estimate_costs': {
            'bom': {
                'value': project.bom_cost or 0,  # 预估BOM成本（来自Import_project表）
                'change': bom_change,
                'change_type': 'increase' if bom_change >= 0 else 'decrease'
            },
            'labor': {
                'value': project.labor_cost or 0,  # 预估人工成本（来自Import_project表）
                'change': labor_change,
                'change_type': 'increase' if labor_change >= 0 else 'decrease'
            },
            'other': {
                'value': project.other_cost or 0,  # 预估其他成本（来自Import_project表）
                'change': other_change,
                'change_type': 'increase' if other_change >= 0 else 'decrease'
            },
            'total': {
                'value': total_estimate_cost,
                'change': 0,
                'change_type': 'increase'
            }
        },
        'actual_costs': {
            'bom': {
                'value': actual_cost['bom_cost'],  # 实际BOM成本（来自ProjectActualCost表）
                'change': bom_change,
                'change_type': 'increase' if bom_change >= 0 else 'decrease'
            },
            'labor': {
                'value': actual_cost['labor_cost'],  # 实际人工成本（来自ProjectActualCost表）
                'change': labor_change,
                'change_type': 'increase' if labor_change >= 0 else 'decrease'
            },
            'other': {
                'value': actual_cost['other_cost'],  # 实际其他成本（来自ProjectActualCost表）
                'change': other_change,
                'change_type': 'increase' if other_change >= 0 else 'decrease'
            },
            'total': {
                'value': actual_cost['total_cost'],  # 实际总成本（来自ProjectActualCost表）
                'change': total_change,
                'change_type': 'increase' if total_change >= 0 else 'decrease'
            }
        },
        # 添加 costs 字段，与 actual_costs 保持一致，以兼容前端代码
        'costs': {
            'bom': {
                'value': actual_cost['bom_cost'],  # 实际BOM成本（来自ProjectActualCost表）
                'change': bom_change,
                'change_type': 'increase' if bom_change >= 0 else 'decrease'
            },
            'labor': {
                'value': actual_cost['labor_cost'],  # 实际人工成本（来自ProjectActualCost表）
                'change': labor_change,
                'change_type': 'increase' if labor_change >= 0 else 'decrease'
            },
            'other': {
                'value': actual_cost['other_cost'],  # 实际其他成本（来自ProjectActualCost表）
                'change': other_change,
                'change_type': 'increase' if other_change >= 0 else 'decrease'
            },
            'total': {
                'value': actual_cost['total_cost'],  # 实际总成本（来自ProjectActualCost表）
                'change': total_change,
                'change_type': 'increase' if total_change >= 0 else 'decrease'
            }
        },
        'cost_percent': round(cost_percent, 2)
    }

    return jsonify(result)


@bp.route('/api/project/<int:project_id>/trend')
@login_required
def api_project_trend(project_id):
    """获取项目月度成本趋势API"""
    months_limit = request.args.get('limit', 12, type=int)
    trend_data = get_project_monthly_costs(project_id, months_limit)
    return jsonify(trend_data)


@bp.route('/api/project/<int:project_id>/cost_details')
@login_required
def api_project_cost_details(project_id):
    """获取项目成本明细API"""
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # 获取所有成本记录
    bom_costs = BOMCostImport.query.filter_by(project_id=project_id).all()
    other_costs = OtherCostImport.query.filter_by(project_id=project_id).all()

    # 成本类型英文代码到中文名称的映射
    cost_type_map = {
        'travel': '差旅费',
        'outsource': '外协费',
        'training': '培训费',
        'repair': '维修费',
        'management': '管理费',
        'transportation': '运杂费',
        'other': '其他费用',
        '测试成本': '测试成本'  # 保留原来的中文名称
    }

    details = []

    # 添加BOM成本记录
    for cost in bom_costs:
        # 处理import_file字段，如果包含分隔符'|'则提取原始文件名
        file_info = cost.import_file or ''
        display_filename = file_info
        download_link = None

        if file_info:
            if '|' in file_info:
                secure_name, display_filename = file_info.split('|', 1)
                # 创建下载链接
                download_link = f"/system/project_cost/download/bom/{secure_name}"
            else:
                # 向后兼容旧的文件命名
                download_link = f"/system/project_cost/download/bom/{file_info}"

        details.append({
            'date': cost.created_at.strftime('%Y-%m-%d'),
            'year_month': cost.year_month,
            'type': 'BOM成本',
            'amount': cost.amount,
            'remark': f"文件: {display_filename}" if cost.import_file else '',
            'download_link': download_link
        })

    # 添加其他成本记录
    for cost in other_costs:
        # 处理import_file字段，如果包含分隔符'|'则提取原始文件名
        file_info = cost.import_file or ''
        display_filename = file_info
        download_link = None

        if file_info:
            if '|' in file_info:
                secure_name, display_filename = file_info.split('|', 1)
                # 创建下载链接
                download_link = f"/system/project_cost/download/other/{secure_name}"
            else:
                # 向后兼容旧的文件命名
                download_link = f"/system/project_cost/download/other/{file_info}"

        # 将英文代码转换为中文显示名称
        display_type = cost_type_map.get(cost.cost_type, cost.cost_type)

        details.append({
            'date': cost.created_at.strftime('%Y-%m-%d'),
            'year_month': cost.year_month,
            'type': display_type,  # 使用中文显示名称
            'amount': cost.amount,
            'remark': f"文件: {display_filename}" if cost.import_file else '',
            'download_link': download_link
        })

    # 按日期排序
    details.sort(key=lambda x: x['date'], reverse=True)

    # 计算总记录数
    total_count = len(details)

    # 分页处理
    start = (page - 1) * limit
    end = start + limit
    paginated_data = details[start:end]

    return jsonify({
        'code': 0,
        'msg': '',
        'count': total_count,
        'data': paginated_data
    })


@bp.route('/api/cost_structure')
@login_required
def api_cost_structure():
    """获取成本构成数据API"""
    structure_data = get_cost_structure_data()
    return jsonify(structure_data)


@bp.route('/api/status_distribution')
@login_required
def api_status_distribution():
    """获取项目状态分布数据API"""
    distribution_data = get_status_distribution_data()
    return jsonify(distribution_data)


@bp.route('/api/import/bom', methods=['POST'])
@login_required
def api_import_bom():
    """导入BOM成本API"""
    # 获取表单数据
    project_id = request.form.get('project_id', type=int)
    year_month = request.form.get('year_month')
    amount = request.form.get('amount', type=float)

    if not project_id or not year_month or not amount:
        return fail_api(msg='缺少必要参数')

    # 检查项目是否存在
    project = Import_project.query.get(project_id)
    if not project:
        return fail_api(msg='项目不存在')

    # 处理文件上传
    import_file = None
    original_filename = None
    if 'file' in request.files:
        file = request.files['file']
        if file and file.filename:
            # 保存原始文件名
            original_filename = file.filename

            # 获取文件扩展名
            _, file_ext = os.path.splitext(file.filename)

            # 生成唯一的文件名：时间戳_随机字符串.扩展名
            timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            random_str = str(uuid.uuid4()).replace('-', '')[:8]
            secure_name = f"{timestamp}_{random_str}{file_ext}"

            # 确保上传目录存在
            upload_dir = os.path.join(current_app.config['UPLOADED_PHOTOS_DEST'], 'bom_costs')
            if not os.path.exists(upload_dir):
                os.makedirs(upload_dir)

            # 构建完整的文件路径
            filepath = os.path.join(upload_dir, secure_name)

            # 保存文件
            file.save(filepath)

            # 在数据库中保存文件信息（包含原始文件名和安全文件名）
            import_file = f"{secure_name}|{original_filename}"

    # 导入成本数据
    success, message = import_bom_cost(
        project_id=project_id,
        year_month=year_month,
        amount=amount,
        import_file=import_file,
        import_by=current_user.id
    )

    if success:
        return success_api(msg=message)
    else:
        return fail_api(msg=message)


@bp.route('/api/import/bom/excel', methods=['POST'])
@login_required
def api_import_bom_excel():
    """批量导入BOM成本API"""
    if 'file' not in request.files:
        return jsonify({'code': 400, 'msg': '未选择文件'})

    file = request.files['file']

    # 检查文件是否为空
    if file.filename == '':
        return jsonify({'code': 400, 'msg': '未选择文件'})

    # 检查文件类型
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'code': 400, 'msg': '仅支持Excel文件'})

    try:
        # 读取Excel文件
        df = pd.read_excel(BytesIO(file.read()))

        # 检查必要列是否存在
        required_columns = ['项目类型', '项目编号', '月份', 'BOM成本']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return jsonify({'code': 400, 'msg': f'文件格式不正确，缺少以下列：{", ".join(missing_columns)}'})

        # 保存原始文件
        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        random_str = str(uuid.uuid4()).replace('-', '')[:8]
        file_ext = os.path.splitext(file.filename)[1]
        secure_name = f"{timestamp}_{random_str}{file_ext}"

        # 确保上传目录存在
        upload_dir = os.path.join(current_app.config['UPLOADED_PHOTOS_DEST'], 'bom_costs')
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

        # 构建完整的文件路径
        filepath = os.path.join(upload_dir, secure_name)

        # 重新定位文件指针到开始位置
        file.seek(0)

        # 保存文件
        file.save(filepath)

        # 在数据库中保存文件信息（包含原始文件名和安全文件名）
        import_file = f"{secure_name}|{file.filename}"

        # 处理数据
        total_count = len(df)
        success_count = 0
        error_count = 0
        error_details = []

        for index, row in df.iterrows():
            try:
                # 获取项目类型和编号
                project_type = str(row['项目类型']).strip()
                project_code = str(row['项目编号']).strip()

                # 查找项目类型ID
                project_type_obj = ProjectManageDept.query.filter_by(dept_name=project_type).first()
                if not project_type_obj:
                    error_details.append(f"第{index+2}行: 项目类型 '{project_type}' 不存在")
                    error_count += 1
                    continue

                # 查找项目
                project = Import_project.query.filter_by(
                    project_code=project_code,
                    dept_id=project_type_obj.id
                ).first()

                if not project:
                    error_details.append(f"第{index+2}行: 项目编号 '{project_code}' 在项目类型 '{project_type}' 下不存在")
                    error_count += 1
                    continue

                # 处理月份
                month_str = str(row['月份']).strip()

                # 尝试解析不同格式的月份
                try:
                    if '-' in month_str:
                        # 如果是 "YYYY-MM" 格式，直接使用
                        year_month = month_str
                    elif '/' in month_str:
                        # 如果是 "YYYY/MM" 格式，转换为 "YYYY-MM"
                        parts = month_str.split('/')
                        year_month = f"{parts[0]}-{parts[1].zfill(2)}"
                    elif len(month_str) == 6:
                        # 如果是 "YYYYMM" 格式，转换为 "YYYY-MM"
                        year_month = f"{month_str[:4]}-{month_str[4:6]}"
                    else:
                        # 其他情况，尝试解析
                        date_obj = pd.to_datetime(month_str)
                        year_month = date_obj.strftime('%Y-%m')
                except Exception:
                    error_details.append(f"第{index+2}行: 月份格式 '{month_str}' 无效，请使用YYYY-MM格式")
                    error_count += 1
                    continue

                # 获取BOM成本金额
                try:
                    amount = float(row['BOM成本'])
                    if amount <= 0:
                        error_details.append(f"第{index+2}行: BOM成本必须大于0")
                        error_count += 1
                        continue
                except (ValueError, TypeError):
                    error_details.append(f"第{index+2}行: BOM成本 '{row['BOM成本']}' 不是有效的数字")
                    error_count += 1
                    continue

                # 导入BOM成本
                success, message = import_bom_cost(
                    project_id=project.id,
                    year_month=year_month,
                    amount=amount,
                    import_file=import_file,
                    import_by=current_user.id
                )

                if success:
                    success_count += 1
                else:
                    error_details.append(f"第{index+2}行: {message}")
                    error_count += 1

            except Exception as e:
                error_details.append(f"第{index+2}行: 处理出错 - {str(e)}")
                error_count += 1

        # 返回处理结果
        return jsonify({
            'code': 200,
            'msg': '导入完成',
            'data': {
                'total': total_count,
                'success': success_count,
                'error': error_count,
                'error_details': error_details
            }
        })

    except Exception as e:
        return jsonify({'code': 500, 'msg': f'导入失败: {str(e)}'})


@bp.route('/api/import/other', methods=['POST'])
@login_required
def api_import_other():
    """导入其他成本API"""
    # 获取表单数据
    project_id = request.form.get('project_id', type=int)
    year_month = request.form.get('year_month')
    cost_type = request.form.get('cost_type')
    amount = request.form.get('amount', type=float)

    if not project_id or not year_month or not cost_type or not amount:
        return fail_api(msg='缺少必要参数')

    # 检查项目是否存在
    project = Import_project.query.get(project_id)
    if not project:
        return fail_api(msg='项目不存在')

    # 处理文件上传
    import_file = None
    original_filename = None
    if 'file' in request.files:
        file = request.files['file']
        if file and file.filename:
            # 保存原始文件名
            original_filename = file.filename

            # 获取文件扩展名
            _, file_ext = os.path.splitext(file.filename)

            # 生成唯一的文件名：时间戳_随机字符串.扩展名
            timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            random_str = str(uuid.uuid4()).replace('-', '')[:8]
            secure_name = f"{timestamp}_{random_str}{file_ext}"

            # 确保上传目录存在
            upload_dir = os.path.join(current_app.config['UPLOADED_PHOTOS_DEST'], 'other_costs')
            if not os.path.exists(upload_dir):
                os.makedirs(upload_dir)

            # 构建完整的文件路径
            filepath = os.path.join(upload_dir, secure_name)

            # 保存文件
            file.save(filepath)

            # 在数据库中保存文件信息（包含原始文件名和安全文件名）
            import_file = f"{secure_name}|{original_filename}"

    # 导入成本数据
    success, message = import_other_cost(
        project_id=project_id,
        year_month=year_month,
        cost_type=cost_type,
        amount=amount,
        import_file=import_file,
        import_by=current_user.id
    )

    if success:
        return success_api(msg=message)
    else:
        return fail_api(msg=message)


@bp.route('/api/import/other/excel', methods=['POST'])
@login_required
def api_import_other_excel():
    """批量导入其他成本API"""
    if 'file' not in request.files:
        return jsonify({'code': 400, 'msg': '未选择文件'})

    file = request.files['file']

    # 检查文件是否为空
    if file.filename == '':
        return jsonify({'code': 400, 'msg': '未选择文件'})

    # 检查文件类型
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'code': 400, 'msg': '仅支持Excel文件'})

    try:
        # 读取Excel文件
        df = pd.read_excel(BytesIO(file.read()))

        # 检查必要列是否存在
        required_columns = ['项目类型', '项目编号', '月份', '成本类型', '成本金额']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return jsonify({'code': 400, 'msg': f'文件格式不正确，缺少以下列：{", ".join(missing_columns)}'})

        # 保存原始文件
        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        random_str = str(uuid.uuid4()).replace('-', '')[:8]
        file_ext = os.path.splitext(file.filename)[1]
        secure_name = f"{timestamp}_{random_str}{file_ext}"

        # 确保上传目录存在
        upload_dir = os.path.join(current_app.config['UPLOADED_PHOTOS_DEST'], 'other_costs')
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

        # 构建完整的文件路径
        filepath = os.path.join(upload_dir, secure_name)

        # 重新定位文件指针到开始位置
        file.seek(0)

        # 保存文件
        file.save(filepath)

        # 在数据库中保存文件信息（包含原始文件名和安全文件名）
        import_file = f"{secure_name}|{file.filename}"

        # 成本类型映射（中文名称到代码）
        cost_type_map = {
            '差旅费': 'travel',
            '外协费': 'outsource',
            '培训费': 'training',
            '维修费': 'repair',
            '管理费': 'management',
            '运杂费': 'transportation',
            '其他费用': 'other'
        }

        # 处理数据
        total_count = len(df)
        success_count = 0
        error_count = 0
        error_details = []

        for index, row in df.iterrows():
            try:
                # 获取项目类型和编号
                project_type = str(row['项目类型']).strip()
                project_code = str(row['项目编号']).strip()

                # 查找项目类型ID
                project_type_obj = ProjectManageDept.query.filter_by(dept_name=project_type).first()
                if not project_type_obj:
                    error_details.append(f"第{index+2}行: 项目类型 '{project_type}' 不存在")
                    error_count += 1
                    continue

                # 查找项目
                project = Import_project.query.filter_by(
                    project_code=project_code,
                    dept_id=project_type_obj.id
                ).first()

                if not project:
                    error_details.append(f"第{index+2}行: 项目编号 '{project_code}' 在项目类型 '{project_type}' 下不存在")
                    error_count += 1
                    continue

                # 处理月份
                month_str = str(row['月份']).strip()

                # 尝试解析不同格式的月份
                try:
                    if '-' in month_str:
                        # 如果是 "YYYY-MM" 格式，直接使用
                        year_month = month_str
                    elif '/' in month_str:
                        # 如果是 "YYYY/MM" 格式，转换为 "YYYY-MM"
                        parts = month_str.split('/')
                        year_month = f"{parts[0]}-{parts[1].zfill(2)}"
                    elif len(month_str) == 6:
                        # 如果是 "YYYYMM" 格式，转换为 "YYYY-MM"
                        year_month = f"{month_str[:4]}-{month_str[4:6]}"
                    else:
                        # 其他情况，尝试解析
                        date_obj = pd.to_datetime(month_str)
                        year_month = date_obj.strftime('%Y-%m')
                except Exception:
                    error_details.append(f"第{index+2}行: 月份格式 '{month_str}' 无效，请使用YYYY-MM格式")
                    error_count += 1
                    continue

                # 处理成本类型
                cost_type_str = str(row['成本类型']).strip()
                cost_type = cost_type_map.get(cost_type_str)

                if not cost_type:
                    error_details.append(f"第{index+2}行: 成本类型 '{cost_type_str}' 无效，有效类型为：差旅费、外协费、培训费、维修费、管理费、运杂费、其他费用")
                    error_count += 1
                    continue

                # 获取成本金额
                try:
                    amount = float(row['成本金额'])
                    if amount <= 0:
                        error_details.append(f"第{index+2}行: 成本金额必须大于0")
                        error_count += 1
                        continue
                except (ValueError, TypeError):
                    error_details.append(f"第{index+2}行: 成本金额 '{row['成本金额']}' 不是有效的数字")
                    error_count += 1
                    continue

                # 导入其他成本
                success, message = import_other_cost(
                    project_id=project.id,
                    year_month=year_month,
                    cost_type=cost_type,
                    amount=amount,
                    import_file=import_file,
                    import_by=current_user.id
                )

                if success:
                    success_count += 1
                else:
                    error_details.append(f"第{index+2}行: {message}")
                    error_count += 1

            except Exception as e:
                error_details.append(f"第{index+2}行: 处理出错 - {str(e)}")
                error_count += 1

        # 返回处理结果
        return jsonify({
            'code': 200,
            'msg': '导入完成',
            'data': {
                'total': total_count,
                'success': success_count,
                'error': error_count,
                'error_details': error_details
            }
        })

    except Exception as e:
        return jsonify({'code': 500, 'msg': f'导入失败: {str(e)}'})


@bp.route('/api/calculate/labor', methods=['POST'])
@login_required
def api_calculate_labor():
    """计算人工成本API"""
    # 获取表单数据
    method = request.form.get('method')

    # 只支持actual_salary方法
    if method != 'actual_salary':
        return fail_api(msg='不支持的计算方法，仅支持基于实际薪资的计算')

    # 获取必要参数
    start_date_str = request.form.get('start_date')
    end_date_str = request.form.get('end_date')
    project_id = request.form.get('project_id', type=int)  # 可选参数，如果不提供则计算所有项目

    # 验证日期参数
    if not start_date_str or not end_date_str:
        return fail_api(msg='开始日期和结束日期不能为空')

    # 解析日期
    try:
        start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d')

        # 验证日期范围
        if start_date > end_date:
            return fail_api(msg='开始日期不能晚于结束日期')

        date_range = (start_date, end_date)
    except ValueError:
        return fail_api(msg='日期格式无效，请使用YYYY-MM-DD格式')

    # 如果提供了项目ID，验证项目是否存在
    if project_id:
        project = Import_project.query.get(project_id)
        if not project:
            return fail_api(msg=f'项目ID {project_id} 不存在')

    # 调用计算服务
    try:
        from applications.services.cost_calculation import calculate_labor_cost_by_actual_salary
        count = calculate_labor_cost_by_actual_salary(project_id, date_range)
        return success_api(msg=f'成功计算{count}个项目的人工成本（基于实际薪资）')
    except Exception as e:
        # 记录错误日志
        current_app.logger.error(f"计算人工成本失败: {str(e)}")
        return fail_api(msg=f'计算失败: {str(e)}')


@bp.route('/excel/export', methods=['GET'])
@login_required
def export_excel():
    """导出项目成本数据为Excel"""
    # 获取所有项目
    projects = Import_project.query.all()

    # 准备导出数据
    data = []
    for project in projects:
        # 获取项目实际成本
        actual_cost = get_project_actual_cost(project.id)

        # 获取项目状态名称
        status_dict = DictData.query.filter_by(
            type_code='project_status',
            data_value=str(project.project_status)
        ).first()
        status_name = status_dict.data_label if status_dict else '未知状态'

        # 获取项目类型名称
        project_type_obj = ProjectManageDept.query.filter_by(id=project.dept_id).first()
        project_type_name = project_type_obj.dept_name if project_type_obj else ''

        # 拼接项目类型和项目编码
        project_code_with_type = project_type_name + '-' + project.project_code if project_type_name else project.project_code

        # 计算预估总成本（BOM成本+人工成本+其他成本）
        total_estimate_cost = (project.bom_cost or 0) + (project.labor_cost or 0) + (project.other_cost or 0)

        # 计算成本占比
        cost_percent = 0
        if total_estimate_cost > 0:
            cost_percent = (actual_cost['total_cost'] / total_estimate_cost) * 100

        data.append({
            '项目ID': project.id,
            '项目名称': project.project_name,
            '项目编码': project_code_with_type,
            '项目状态': status_name,
            '项目价格': project.price or 0,
            '机台数量': project.machine_number or 0,
            '预估总成本': total_estimate_cost,  # 预估总成本（BOM成本+人工成本+其他成本）
            '预估BOM成本': project.bom_cost or 0,   # 预估BOM成本（来自Import_project表）
            '预估人工成本': project.labor_cost or 0, # 预估人工成本（来自Import_project表）
            '预估其他成本': project.other_cost or 0, # 预估其他成本（来自Import_project表）
            '实际BOM成本': actual_cost['bom_cost'],  # 实际BOM成本（来自ProjectActualCost表）
            '实际人工成本': actual_cost['labor_cost'],  # 实际人工成本（来自ProjectActualCost表）
            '实际其他成本': actual_cost['other_cost'],  # 实际其他成本（来自ProjectActualCost表）
            '实际总成本': actual_cost['total_cost'],  # 实际总成本（来自ProjectActualCost表）
            '成本占比(%)': round(cost_percent, 2)
        })

    # 创建DataFrame
    df = pd.DataFrame(data)

    # 生成文件名
    now = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f'项目成本分析_{now}.xlsx'

    # 使用BytesIO创建内存中的Excel文件
    excel_buffer = BytesIO()

    # 将DataFrame写入内存中的Excel文件
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        df.to_excel(writer, index=False)

    # 将指针移到文件开头
    excel_buffer.seek(0)

    # 直接返回内存中的文件，不保存到磁盘
    return send_file(
        excel_buffer,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )


@bp.route('/api/labor_cost/result', methods=['GET'])
@login_required
def api_labor_cost_result():
    """获取人工成本计算结果API"""
    project_id = request.args.get('project_id', type=int)

    if project_id:
        # 获取特定项目的成本数据
        project = Import_project.query.get_or_404(project_id)

        # 获取项目的所有月度人工成本记录
        labor_costs = ProjectLaborCost.query.filter_by(project_id=project_id).order_by(ProjectLaborCost.work_month).all()

        # 准备返回数据
        result = {
            'project': {
                'id': project.id,
                'name': project.project_name,
                'code': project.project_code
            },
            'monthly_costs': [],
            'total_cost': 0
        }

        # 处理月度数据
        for cost in labor_costs:
            result['monthly_costs'].append({
                'year_month': cost.work_month,
                'cost': cost.total_cost
            })
            result['total_cost'] += cost.total_cost

        return jsonify(result)
    else:
        # 获取最近计算的数据
        recent_costs = ProjectLaborCost.query.order_by(ProjectLaborCost.calculated_at.desc()).limit(20).all()

        # 按项目分组
        projects_data = {}
        for cost in recent_costs:
            if cost.project_id not in projects_data:
                project = Import_project.query.get(cost.project_id)
                if project:
                    projects_data[cost.project_id] = {
                        'id': project.id,
                        'name': project.project_name,
                        'code': project.project_code,
                        'latest_update': cost.calculated_at.strftime('%Y-%m-%d %H:%M:%S'),
                        'total_cost': 0,
                        'months_count': 0
                    }

            if cost.project_id in projects_data:
                projects_data[cost.project_id]['total_cost'] += cost.total_cost
                projects_data[cost.project_id]['months_count'] += 1

        return jsonify(list(projects_data.values()))


@bp.route('/download/<cost_type>/<filename>')
@login_required
def download_cost_file(cost_type, filename):
    """下载成本文件"""
    # 确定文件类型路径
    if cost_type not in ['bom', 'other']:
        return fail_api(msg='无效的成本类型')

    # 构建文件目录
    file_dir = f"{cost_type}_costs"
    upload_dir = os.path.join(current_app.config['UPLOADED_PHOTOS_DEST'], file_dir)

    # 构建文件路径
    filepath = os.path.join(upload_dir, filename)

    # 检查文件是否存在
    if not os.path.exists(filepath):
        return fail_api(msg='文件不存在')

    # 获取关联的成本记录，以获取原始文件名
    if cost_type == 'bom':
        cost_record = BOMCostImport.query.filter(BOMCostImport.import_file.like(f"{filename}%")).first()
    else:
        cost_record = OtherCostImport.query.filter(OtherCostImport.import_file.like(f"{filename}%")).first()

    # 确定下载时显示的文件名
    display_filename = filename
    if cost_record and cost_record.import_file and '|' in cost_record.import_file:
        _, display_filename = cost_record.import_file.split('|', 1)

    # 发送文件
    return send_file(filepath, as_attachment=True, download_name=display_filename)


@bp.route('/api/import/template/download')
@login_required
def download_import_template():
    """下载导入模板"""
    # 获取模板类型
    template_type = request.args.get('type', 'bom')

    if template_type not in ['bom', 'other']:
        return fail_api(msg='无效的模板类型')

    try:
        # 获取项目类型列表
        project_types = [dept.dept_name for dept in ProjectManageDept.query.filter_by(status=1).all()]

        # 创建示例数据
        if template_type == 'bom':
            # BOM成本导入模板
            data = {
                '项目类型': [],
                '项目编号': [],
                '月份': [],
                'BOM成本': []
            }

            # 添加示例数据
            for i in range(3):
                if project_types:
                    data['项目类型'].append(project_types[0] if i == 0 else project_types[min(i, len(project_types)-1)])
                else:
                    data['项目类型'].append('GS')  # 默认项目类型

                data['项目编号'].append(f'250{i+1}')
                data['月份'].append(f'2025-{4-i}')
                data['BOM成本'].append(52000)

            filename = '项目BOM成本导入模板.xlsx'
        else:
            # 其他成本导入模板
            data = {
                '项目类型': [],
                '项目编号': [],
                '月份': [],
                '成本类型': [],
                '成本金额': []
            }

            # 成本类型列表
            cost_types = ['差旅费', '外协费', '培训费', '维修费', '管理费', '运杂费', '其他费用']

            # 添加示例数据
            for i in range(7):
                if project_types:
                    data['项目类型'].append(project_types[0] if i == 0 else project_types[min(i % 3, len(project_types)-1)])
                else:
                    data['项目类型'].append('GS')  # 默认项目类型

                data['项目编号'].append(f'250{i % 3 + 1}')
                data['月份'].append(f'2025-{4 - i % 3}')
                data['成本类型'].append(cost_types[i])
                data['成本金额'].append([1000, 622000, 300, 52000, 63000, 30000, 0][i])

            filename = '项目其他成本导入模板.xlsx'

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 创建一个BytesIO对象
        output = BytesIO()

        # 写入Excel
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='导入模板')

            # 获取worksheet
            workbook = writer.book
            worksheet = writer.sheets['导入模板']

            # 设置列宽
            for i, col in enumerate(df.columns):
                column_width = max(len(col) * 2, df[col].astype(str).map(len).max() * 2)
                worksheet.column_dimensions[chr(65 + i)].width = column_width

        # 设置文件指针到开始
        output.seek(0)

        # 返回文件
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        return fail_api(msg=f'模板创建失败: {str(e)}')


@bp.route('/api/dept_costs')
@login_required
def api_dept_costs():
    """获取部门成本数据API"""
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # 获取筛选条件
    status = request.args.get('status', '')
    dept_id = request.args.get('dept_id', '')
    time_range = request.args.get('time_range', 'year')

    # 确定日期范围
    now = datetime.datetime.now()
    start_date = None
    end_date = now

    if time_range == 'month':
        # 本月
        start_date = datetime.datetime(now.year, now.month, 1)
    elif time_range == 'quarter':
        # 本季度
        current_quarter = (now.month - 1) // 3 + 1
        start_date = datetime.datetime(now.year, (current_quarter - 1) * 3 + 1, 1)
    elif time_range == 'year':
        # 本年度
        start_date = datetime.datetime(now.year, 1, 1)
    else:
        # 全部，不设置开始日期
        pass

    # 获取所有部门
    from applications.models.admin_dept import Dept
    depts = Dept.query.filter_by(status=1).all()

    # 准备数据
    dept_costs = []
    total_all_depts = 0

    for dept in depts:
        # 查询该部门的项目
        projects_query = Import_project.query
        if dept_id and int(dept_id) != dept.id:
            continue

        # 筛选状态
        if status:
            projects_query = projects_query.filter_by(project_status=status)

        # 获取与该部门关联的项目
        # 这里需要根据你的数据模型进行调整，可能是通过多对多关系或者dept_id字段
        projects = projects_query.filter_by(dept_id=dept.id).all()

        # 如果没有项目，跳过
        if not projects:
            continue

        # 计算成本
        total_bom_cost = 0
        total_labor_cost = 0
        total_other_cost = 0

        for project in projects:
            # 查询成本记录
            bom_costs_query = BOMCostImport.query.filter_by(project_id=project.id)
            labor_costs_query = ProjectLaborCost.query.filter_by(project_id=project.id)
            other_costs_query = OtherCostImport.query.filter_by(project_id=project.id)

            # 应用日期筛选
            if start_date:
                bom_costs_query = bom_costs_query.filter(BOMCostImport.created_at >= start_date)
                labor_costs_query = labor_costs_query.filter(ProjectLaborCost.calculated_at >= start_date)
                other_costs_query = other_costs_query.filter(OtherCostImport.created_at >= start_date)

            # 计算各类成本
            bom_cost = sum(cost.amount for cost in bom_costs_query.all())
            labor_cost = sum(cost.total_cost for cost in labor_costs_query.all())
            other_cost = sum(cost.amount for cost in other_costs_query.all())

            total_bom_cost += bom_cost
            total_labor_cost += labor_cost
            total_other_cost += other_cost

        # 计算该部门的总成本
        total_cost = total_bom_cost + total_labor_cost + total_other_cost
        total_all_depts += total_cost

        # 添加到结果列表
        dept_costs.append({
            'dept_name': dept.dept_name,
            'project_count': len(projects),
            'total_bom_cost': round(total_bom_cost, 2),
            'total_labor_cost': round(total_labor_cost, 2),
            'total_other_cost': round(total_other_cost, 2),
            'total_cost': round(total_cost, 2),
            'cost_percent': 0  # 先设为0，后面计算
        })

    # 计算占比
    for dept_cost in dept_costs:
        if total_all_depts > 0:
            dept_cost['cost_percent'] = round((dept_cost['total_cost'] / total_all_depts) * 100, 2)

    # 按总成本排序
    dept_costs.sort(key=lambda x: x['total_cost'], reverse=True)

    # 分页
    start = (page - 1) * limit
    end = start + limit
    paginated_data = dept_costs[start:end]

    return jsonify({
        'code': 0,
        'msg': '',
        'count': len(dept_costs),
        'data': paginated_data
    })


@bp.route('/api/dept_cost_structure')
@login_required
def api_dept_cost_structure():
    """获取部门成本结构数据API，用于饼图展示"""
    # 获取筛选条件
    status = request.args.get('status', '')
    dept_id = request.args.get('dept_id', '')
    time_range = request.args.get('time_range', 'year')

    # 确定日期范围
    now = datetime.datetime.now()
    start_date = None
    end_date = now

    if time_range == 'month':
        # 本月
        start_date = datetime.datetime(now.year, now.month, 1)
    elif time_range == 'quarter':
        # 本季度
        current_quarter = (now.month - 1) // 3 + 1
        start_date = datetime.datetime(now.year, (current_quarter - 1) * 3 + 1, 1)
    elif time_range == 'year':
        # 本年度
        start_date = datetime.datetime(now.year, 1, 1)
    else:
        # 全部，不设置开始日期
        pass

    # 获取所有部门
    from applications.models.admin_dept import Dept
    depts = Dept.query.filter_by(status=1).all()

    # 准备数据
    dept_costs = []

    for dept in depts:
        # 查询该部门的项目
        projects_query = Import_project.query
        if dept_id and int(dept_id) != dept.id:
            continue

        # 筛选状态
        if status:
            projects_query = projects_query.filter_by(project_status=status)

        # 获取与该部门关联的项目
        projects = projects_query.filter_by(dept_id=dept.id).all()

        # 如果没有项目，跳过
        if not projects:
            continue

        # 计算成本
        total_cost = 0

        for project in projects:
            # 查询成本记录
            bom_costs_query = BOMCostImport.query.filter_by(project_id=project.id)
            labor_costs_query = ProjectLaborCost.query.filter_by(project_id=project.id)
            other_costs_query = OtherCostImport.query.filter_by(project_id=project.id)

            # 应用日期筛选
            if start_date:
                bom_costs_query = bom_costs_query.filter(BOMCostImport.created_at >= start_date)
                labor_costs_query = labor_costs_query.filter(ProjectLaborCost.calculated_at >= start_date)
                other_costs_query = other_costs_query.filter(OtherCostImport.created_at >= start_date)

            # 计算各类成本
            bom_cost = sum(cost.amount for cost in bom_costs_query.all())
            labor_cost = sum(cost.total_cost for cost in labor_costs_query.all())
            other_cost = sum(cost.amount for cost in other_costs_query.all())

            total_cost += bom_cost + labor_cost + other_cost

        # 添加到结果列表
        if total_cost > 0:
            dept_costs.append({
                'name': dept.dept_name,
                'value': round(total_cost, 2)
            })

    # 按成本排序
    dept_costs.sort(key=lambda x: x['value'], reverse=True)

    return jsonify(dept_costs)


@bp.route('/api/project_summary')
@login_required
def api_project_summary():
    """获取项目成本汇总数据API"""
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # 获取筛选条件
    status = request.args.get('status', '')  # 对应前端name="status"
    dept_id = request.args.get('dept_id', '')  # 获取项目部门ID
    keyword = request.args.get('keyword', '')

    # 时间戳参数，用于防止缓存
    _ = request.args.get('_t', None)

    # 构建查询
    query = Import_project.query

    # 应用筛选条件
    if status:
        query = query.filter_by(project_status=status)

    if dept_id:
        query = query.filter_by(dept_id=dept_id)

    if keyword:
        query = query.filter(
            (Import_project.project_name.like(f'%{keyword}%')) |
            (Import_project.project_code.like(f'%{keyword}%'))
        )

    # 执行分页查询，确保获取最新数据
    db.session.commit()  # 确保使用最新的数据库状态
    paginate = query.layui_paginate(page=page, limit=limit)

    # 准备返回数据
    items = []
    for project in paginate.items:
        # 获取项目状态名称
        status_dict = DictData.query.filter_by(
            type_code='project_status',
            data_value=str(project.project_status)
        ).first()
        status_name = status_dict.data_label if status_dict else '未知状态'

        # 获取项目类型名称
        project_type_obj = ProjectManageDept.query.filter_by(id=project.dept_id).first()
        project_type_name = project_type_obj.dept_name if project_type_obj else ''

        # 获取实际成本数据，强制从数据库获取最新数据
        db.session.refresh(project)  # 刷新项目对象
        actual_cost = get_project_actual_cost(project.id)

        # 计算预估总成本（BOM成本+人工成本+其他成本）
        total_estimate_cost = (project.bom_cost or 0) + (project.labor_cost or 0) + (project.other_cost or 0)

        # 计算成本占比
        cost_percent = 0
        try:
            if total_estimate_cost > 0:
                cost_percent = (actual_cost['total_cost'] / total_estimate_cost) * 100
            elif actual_cost['total_cost'] > 0:
                # 如果预算成本为0但实际成本大于0，设置为100%（表示全部超支）
                cost_percent = 100
        except (ZeroDivisionError, TypeError):
            # 处理除以0或类型错误的情况
            cost_percent = 0 if actual_cost['total_cost'] == 0 else 100

        # 计算成本占比的颜色类别
        cost_class = ''
        ratio = cost_percent / 100  # 成本占比是百分比形式，除以100得到比例
        if ratio > 1:
            # 超出预估，显示红色
            cost_class = 'cost-red'
        elif ratio >= 0.8:
            # 达到预估的80%以上但未超出，显示黄色
            cost_class = 'cost-yellow'
        elif ratio >= 0.6:
            # 达到预估的60%以上但未达到80%，显示橙色
            cost_class = 'cost-orange'
        else:
            # 低于预估的60%，显示绿色
            cost_class = 'cost-green'

        # 估算实际的机械BOM成本和电气BOM成本
        actual_mechanical_bom_cost = 0
        actual_electrical_bom_cost = 0

        # 如果预估的BOM成本大于0，则按照预估的机械和电气BOM成本的比例分配实际的BOM成本
        if project.bom_cost and project.bom_cost > 0:
            mechanical_ratio = (project.mechanical_bom_cost or 0) / project.bom_cost
            electrical_ratio = (project.electrical_bom_cost or 0) / project.bom_cost

            actual_mechanical_bom_cost = actual_cost['bom_cost'] * mechanical_ratio
            actual_electrical_bom_cost = actual_cost['bom_cost'] * electrical_ratio

        # 构建项目汇总数据
        items.append({
            'id': project.id,
            'project_name': project.project_name,
            'project_code': project.project_code,
            'project_type_name': project_type_name,
            'project_status': project.project_status,
            'status_name': status_name,
            'price': project.price,
            'machine_number': project.machine_number,
            'delivery_date': project.delivery_date.strftime('%Y-%m-%d') if project.delivery_date else '',
            'estimate_cost': total_estimate_cost,  # 预估总成本（BOM成本+人工成本+其他成本）
            'mechanical_bom_cost': project.mechanical_bom_cost or 0,  # 预估机械BOM成本
            'electrical_bom_cost': project.electrical_bom_cost or 0,  # 预估电气BOM成本
            'bom_cost': project.bom_cost or 0,     # 预估BOM成本（来自Import_project表）
            'labor_cost': project.labor_cost or 0,  # 预估人工成本（来自Import_project表）
            'other_cost': project.other_cost or 0,  # 预估其他成本（来自Import_project表）
            'actual_bom_cost': actual_cost['bom_cost'],  # 实际BOM成本（来自ProjectActualCost表）
            'actual_mechanical_bom_cost': actual_mechanical_bom_cost,  # 估算的实际机械BOM成本
            'actual_electrical_bom_cost': actual_electrical_bom_cost,  # 估算的实际电气BOM成本
            'actual_labor_cost': actual_cost['labor_cost'],  # 实际人工成本（来自ProjectActualCost表）
            'actual_other_cost': actual_cost['other_cost'],  # 实际其他成本（来自ProjectActualCost表）
            'actual_cost': actual_cost['total_cost'],  # 实际总成本（来自ProjectActualCost表）
            'cost_percent': round(cost_percent, 2),
            'cost_class': cost_class,
            'create_at': project.create_at.strftime('%Y-%m-%d %H:%M:%S') if project.create_at else ''
        })

    return jsonify({
        'code': 0,
        'msg': '',
        'count': paginate.total,
        'data': items
    })


@bp.route('/api/dept_cost_trend')
@login_required
def api_dept_cost_trend():
    """获取部门成本趋势数据API，用于折线图展示"""
    # 获取筛选条件
    status = request.args.get('status', '')
    dept_id = request.args.get('dept_id', '')
    time_range = request.args.get('time_range', 'year')

    # 确定日期范围和月份数
    now = datetime.datetime.now()
    months_count = 12  # 默认显示12个月

    if time_range == 'month':
        months_count = 1
    elif time_range == 'quarter':
        months_count = 3
    elif time_range == 'year':
        months_count = 12
    else:
        months_count = 24  # 全部显示最近24个月

    # 计算开始日期
    start_date = now - relativedelta(months=months_count-1)
    start_date = datetime.datetime(start_date.year, start_date.month, 1)

    # 生成月份列表
    months = []
    current = start_date
    while current <= now:
        months.append(current.strftime('%Y-%m'))
        current += relativedelta(months=1)

    # 初始化成本数据
    bom_costs = [0] * len(months)
    labor_costs = [0] * len(months)
    other_costs = [0] * len(months)
    total_costs = [0] * len(months)

    # 获取部门
    from applications.models.admin_dept import Dept
    if dept_id:
        depts = [Dept.query.get(dept_id)]
    else:
        depts = Dept.query.filter_by(status=1).all()

    # 查询各部门的项目成本
    for dept in depts:
        if not dept:
            continue

        # 查询该部门的项目
        projects_query = Import_project.query.filter_by(dept_id=dept.id)

        # 筛选状态
        if status:
            projects_query = projects_query.filter_by(project_status=status)

        projects = projects_query.all()

        for project in projects:
            # 查询BOM成本
            bom_costs_records = BOMCostImport.query.filter_by(project_id=project.id)\
                .filter(BOMCostImport.created_at >= start_date).all()

            for record in bom_costs_records:
                month_str = record.created_at.strftime('%Y-%m')
                if month_str in months:
                    month_index = months.index(month_str)
                    bom_costs[month_index] += record.amount

            # 查询人工成本
            labor_costs_records = ProjectLaborCost.query.filter_by(project_id=project.id)\
                .filter(ProjectLaborCost.calculated_at >= start_date).all()

            for record in labor_costs_records:
                month_str = record.calculated_at.strftime('%Y-%m')
                if month_str in months:
                    month_index = months.index(month_str)
                    labor_costs[month_index] += record.total_cost

            # 查询其他成本
            other_costs_records = OtherCostImport.query.filter_by(project_id=project.id)\
                .filter(OtherCostImport.created_at >= start_date).all()

            for record in other_costs_records:
                month_str = record.created_at.strftime('%Y-%m')
                if month_str in months:
                    month_index = months.index(month_str)
                    other_costs[month_index] += record.amount

    # 计算总成本
    for i in range(len(months)):
        total_costs[i] = bom_costs[i] + labor_costs[i] + other_costs[i]

    # 返回数据
    return jsonify({
        'months': months,
        'bom_costs': [round(cost, 2) for cost in bom_costs],
        'labor_costs': [round(cost, 2) for cost in labor_costs],
        'other_costs': [round(cost, 2) for cost in other_costs],
        'total_costs': [round(cost, 2) for cost in total_costs]
    })


@bp.route('/api/export_dept_costs')
@login_required
def api_export_dept_costs():
    """导出部门成本数据为Excel"""
    # 获取筛选条件
    status = request.args.get('status', '')
    dept_id = request.args.get('dept_id', '')
    time_range = request.args.get('time_range', 'year')

    # 确定日期范围
    now = datetime.datetime.now()
    start_date = None
    end_date = now

    if time_range == 'month':
        # 本月
        start_date = datetime.datetime(now.year, now.month, 1)
        time_range_str = f"{now.year}年{now.month}月"
    elif time_range == 'quarter':
        # 本季度
        current_quarter = (now.month - 1) // 3 + 1
        start_date = datetime.datetime(now.year, (current_quarter - 1) * 3 + 1, 1)
        time_range_str = f"{now.year}年第{current_quarter}季度"
    elif time_range == 'year':
        # 本年度
        start_date = datetime.datetime(now.year, 1, 1)
        time_range_str = f"{now.year}年"
    else:
        # 全部
        time_range_str = "全部时间"

    # 获取所有部门
    from applications.models.admin_dept import Dept
    depts = Dept.query.filter_by(status=1).all()

    # 准备数据
    dept_costs = []
    total_all_depts = 0

    for dept in depts:
        # 查询该部门的项目
        projects_query = Import_project.query
        if dept_id and int(dept_id) != dept.id:
            continue

        # 筛选状态
        if status:
            projects_query = projects_query.filter_by(project_status=status)

        # 获取与该部门关联的项目
        projects = projects_query.filter_by(dept_id=dept.id).all()

        # 如果没有项目，跳过
        if not projects:
            continue

        # 计算成本
        total_bom_cost = 0
        total_labor_cost = 0
        total_other_cost = 0

        for project in projects:
            # 查询成本记录
            bom_costs_query = BOMCostImport.query.filter_by(project_id=project.id)
            labor_costs_query = ProjectLaborCost.query.filter_by(project_id=project.id)
            other_costs_query = OtherCostImport.query.filter_by(project_id=project.id)

            # 应用日期筛选
            if start_date:
                bom_costs_query = bom_costs_query.filter(BOMCostImport.created_at >= start_date)
                labor_costs_query = labor_costs_query.filter(ProjectLaborCost.calculated_at >= start_date)
                other_costs_query = other_costs_query.filter(OtherCostImport.created_at >= start_date)

            # 计算各类成本
            bom_cost = sum(cost.amount for cost in bom_costs_query.all())
            labor_cost = sum(cost.total_cost for cost in labor_costs_query.all())
            other_cost = sum(cost.amount for cost in other_costs_query.all())

            total_bom_cost += bom_cost
            total_labor_cost += labor_cost
            total_other_cost += other_cost

        # 计算该部门的总成本
        total_cost = total_bom_cost + total_labor_cost + total_other_cost
        total_all_depts += total_cost

        # 添加到结果列表
        dept_costs.append({
            '部门名称': dept.dept_name,
            '项目数量': len(projects),
            'BOM成本': round(total_bom_cost, 2),
            '人工成本': round(total_labor_cost, 2),
            '其他成本': round(total_other_cost, 2),
            '总成本': round(total_cost, 2)
        })

    # 计算占比并添加
    for dept_cost in dept_costs:
        if total_all_depts > 0:
            dept_cost['占比(%)'] = round((dept_cost['总成本'] / total_all_depts) * 100, 2)
        else:
            dept_cost['占比(%)'] = 0

    # 按总成本排序
    dept_costs.sort(key=lambda x: x['总成本'], reverse=True)

    # 创建DataFrame
    df = pd.DataFrame(dept_costs)

    # 添加合计行
    if dept_costs:
        total_row = {
            '部门名称': '合计',
            '项目数量': sum(item['项目数量'] for item in dept_costs),
            'BOM成本': sum(item['BOM成本'] for item in dept_costs),
            '人工成本': sum(item['人工成本'] for item in dept_costs),
            '其他成本': sum(item['其他成本'] for item in dept_costs),
            '总成本': sum(item['总成本'] for item in dept_costs),
            '占比(%)': 100
        }
        df = pd.concat([df, pd.DataFrame([total_row])], ignore_index=True)

    # 生成文件名
    now_str = now.strftime('%Y%m%d%H%M%S')
    filename = f'部门成本分析_{time_range_str}_{now_str}.xlsx'

    # 创建内存中的Excel文件
    excel_buffer = BytesIO()

    # 将DataFrame写入Excel
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        df.to_excel(writer, index=False)

    # 将指针移到文件开头
    excel_buffer.seek(0)

    # 返回Excel文件
    return send_file(
        excel_buffer,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )



