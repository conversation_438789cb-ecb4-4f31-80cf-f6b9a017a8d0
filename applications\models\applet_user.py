from applications.extensions import db

class Applet_user(db.Model):
    __tablename__ = 'applet_user'  # 表名

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    openid = db.Column(db.String(255), nullable=False, unique=True)
    name = db.Column(db.String(255), nullable=False)
    phone = db.Column(db.String(20), nullable=False)

    def __repr__(self):
        return f"<Applet_user {self.name}>"