<!DOCTYPE html>
<html>
<head>
    <title>项目成本分析</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/console.css') }}"/>
    <style>
        /* 全局样式 */
        body.pear-container {
            background-color: #f5f7fa;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
            width: 100%;
            overflow-x: hidden;
        }

        .pear-container {
            width: 100%;
            padding: 0;
            box-sizing: border-box;
        }

        /* 卡片样式优化 */
        .layui-card {
            margin-bottom: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            overflow: hidden;
        }

        .layui-card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            transform: translateY(-5px);
        }

        .layui-card-body {
            padding: 20px;
        }

        .layui-card-header {
            padding: 16px 20px;
            font-weight: 600;
            font-size: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .layui-card-header.dashboard-header {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        /* 页面标题样式 */
        .page-title {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            color: #333;
            padding: 10px 0;
            position: relative;
            display: inline-block;
        }

        .page-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background: #009688;
            border-radius: 3px;
        }

        /* 统计卡片样式 */
        .stat-card {
            text-align: center;
            padding: 20px 10px;
            position: relative;
            overflow: hidden;
            height: 180px; /* 固定高度 */
            min-height: 180px; /* 最小高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .stat-card-projects {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .stat-card-estimated {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .stat-card-actual {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .stat-card-abnormal {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-icon {
            margin-bottom: 15px;
        }

        .stat-icon i {
            font-size: 36px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 70px;
            height: 70px;
            line-height: 70px;
            display: inline-block;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin: 12px 0 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            margin-bottom: 5px;
        }

        .stat-change {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 5px;
        }

        /* 原有样式保留 */
        .text-success {
            color: #5FB878 !important;
        }
        .text-danger {
            color: #FF5722 !important;
        }
        /* 成本占比颜色 */
        .cost-red {
            color: #FF5722 !important; /* 红色，超出预估 */
        }
        .cost-yellow {
            color: #FFB800 !important; /* 黄色，达到预估的80%以上但未超出 */
        }
        .cost-orange {
            color: #FF9966 !important; /* 橙色，达到预估的60%以上但未达到80% */
        }
        .cost-green {
            color: #5FB878 !important; /* 绿色，低于预估的60% */
        }
        .chart-container {
            min-height: 300px;
        }
        .project-table .layui-table-cell {
            height: auto;
            line-height: 24px;
        }
        .cost-status-warning {
            background-color: #FFB800 !important;
            color: #fff;
        }
        .cost-status-warning td {
            color: #fff !important;
        }
        .cost-status-critical {
            background-color: #FF5722 !important;
            color: #fff;
        }
        .cost-status-critical td {
            color: #fff !important;
        }

        /* 可点击数字样式 */
        .clickable-count {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: inline-block;
        }

        .clickable-count:hover {
            transform: scale(1.1);
        }

        /* 项目状态颜色样式 */
        .status-draft {
            background-color: #BDBDBD !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }
        .status-reviewing {
            background-color: #1E9FFF !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }
        .status-pending {
            background-color: #FFB800 !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }
        .status-production {
            background-color: #5FB878 !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }
        .status-completed {
            background-color: #2F4056 !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }
        .status-cancelled {
            background-color: #FF5722 !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }
        .status-acceptance {
            background-color: #01AAED !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }
        .status-debugging {
            background-color: #9966CC !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }
        .status-modification {
            background-color: #FF9966 !important;
            color: #fff;
            padding: 4px 8px;
            border-radius: 2px;
        }

        /* 响应式设计 */
        @media screen and (max-width: 992px) {
            .stat-card {
                padding: 15px 10px;
            }

            .stat-icon i {
                font-size: 32px;
                width: 60px;
                height: 60px;
                line-height: 60px;
            }

            .stat-number {
                font-size: 28px;
            }

            .stat-title {
                font-size: 14px;
            }
        }

        @media screen and (max-width: 576px) {
            .stat-card {
                padding: 15px;
            }

            .stat-icon i {
                font-size: 36px;
                width: 70px;
                height: 70px;
                line-height: 70px;
            }

            .stat-number {
                font-size: 32px;
            }
        }
    </style>
</head>
<body class="pear-container">


    <!-- 页面标题和操作按钮 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <h2 class="page-title">项目成本分析</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="layui-row layui-col-space15" style="width: 100%; margin: 0; clear: both;">
        <!-- 总项目数 -->
        <div class="layui-col-md3 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-projects">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-app"></i>
                    </div>
                    <div class="stat-title">总项目数</div>
                    <div class="stat-number" id="projectCount">--</div>
                    <div class="stat-change" id="projectCountChange">
                        <i class="layui-icon"></i> <span>--</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总预估成本 -->
        <div class="layui-col-md3 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-estimated">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-rmb"></i>
                    </div>
                    <div class="stat-title">总预估成本</div>
                    <div class="stat-number" id="totalCost">--</div>
                    <div class="stat-change" id="totalCostChange">
                        <i class="layui-icon"></i> <span>--</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实际总成本 -->
        <div class="layui-col-md3 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-actual">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-chart"></i>
                    </div>
                    <div class="stat-title">实际总成本</div>
                    <div class="stat-number" id="avgCost">--</div>
                    <div class="stat-change" id="avgCostChange">
                        <i class="layui-icon"></i> <span>--</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 异常项目数 -->
        <div class="layui-col-md3 layui-col-sm6 layui-col-xs12">
            <div class="layui-card">
                <div class="layui-card-body stat-card stat-card-abnormal">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-refresh"></i>
                    </div>
                    <div class="stat-title">异常项目数</div>
                    <div class="stat-number clickable-count" id="abnormalCount" title="点击查看异常项目详情">--</div>
                    <div class="stat-change" id="abnormalCountChange">
                        <i class="layui-icon"></i> <span>--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 同比/环比切换 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="layui-btn-group compare-toggle" style="margin: 10px 0;">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal active" data-compare="year">同比</button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" data-compare="month">环比</button>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header dashboard-header">成本构成分析</div>
                <div class="layui-card-body chart-container">
                    <div id="costStructureChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header dashboard-header">项目状态分布</div>
                <div class="layui-card-body chart-container">
                    <div id="statusDistributionChart" style="width: 100%; height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body">
                    <form class="layui-form" action="" lay-filter="cost-query-form">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">项目状态</label>
                                <div class="layui-input-inline">
                                    <select name="status" id="projectStatus">
                                        <option value="">全部</option>
                                        {% for status in statuses %}
                                        <option value="{{ status.data_value }}">{{ status.data_label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">项目编码</label>
                                <div class="layui-input-inline">
                                    <select name="dept_id" id="projectDept">
                                        <option value="">全部</option>
                                        {% for dept in project_departments %}
                                        <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">关键字</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="keyword" placeholder="项目名称/编码" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn layui-btn-md" lay-submit lay-filter="cost-query">
                                    <i class="layui-icon layui-icon-search"></i>
                                    查询
                                </button>
                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                                    <i class="layui-icon layui-icon-refresh"></i>
                                    重置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目表格 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header dashboard-header">
                    项目列表
                    <div class="layui-inline" style="float: right;">
                        <div class="layui-btn-group">
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="exportExcel">
                                <i class="layui-icon layui-icon-export"></i> 导出Excel
                            </button>
                            {% if authorize("system:project_cost:import") %}
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="importBOM">
                                <i class="layui-icon layui-icon-upload-drag"></i> 导入BOM成本
                            </button>
                            {% endif %}
                            {% if authorize('system:project_cost:import') %}
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="importOther">
                                <i class="layui-icon layui-icon-upload-drag"></i> 导入其他成本
                            </button>
                            {% endif %}
                            {% if authorize('system:project_cost:import') %}
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="calculateLabor">
                                <i class="layui-icon layui-icon-engine"></i> 计算人工成本
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="layui-card-body">
                    <table id="project-table" lay-filter="project-table"></table>
                </div>
            </div>
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}

<!-- 表格操作 -->
<script type="text/html" id="project-toolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i>
        </button>
    </div>
</script>

<!-- 行操作 -->
<script type="text/html" id="project-bar">
    <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="detail">
        <i class="layui-icon layui-icon-form"></i>查看详情
    </button>
</script>

<script>
    layui.use(['table', 'form', 'laydate', 'echarts', 'jquery', 'popup', 'common', 'element'], function() {
        let table = layui.table;
        let form = layui.form;
        let laydate = layui.laydate;
        let echarts = layui.echarts;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let element = layui.element;

        // 加载指标数据
        loadDashboardMetrics('year');

        // 异常项目数点击事件
        $('#abnormalCount').click(function() {
            if ($(this).text() === '--' || parseInt($(this).text()) === 0) {
                layer.msg('当前没有异常项目', {icon: 1});
                return;
            }

            // 显示加载中
            const loadingIndex = layer.load(2);

            // 获取异常项目列表
            getAbnormalProjects(function(projects) {
                layer.close(loadingIndex);

                if (!projects || projects.length === 0) {
                    layer.msg('没有找到异常项目', {icon: 2});
                    return;
                }

                // 筛选出成本占比超过100%的项目
                const overBudgetProjects = projects.filter(function(project) {
                    return project.cost_percent > 100;
                });

                if (overBudgetProjects.length === 0) {
                    layer.msg('没有找到超出预估的项目', {icon: 1});
                    return;
                }

                // 使用筛选后的项目列表
                projects = overBudgetProjects;

                // 构建异常项目列表HTML
                let content = `
                    <div class="layui-card">
                        <div class="layui-card-header">超出预估项目列表</div>
                        <div class="layui-card-body">
                            <table id="abnormal-project-table" lay-filter="abnormal-project-table"></table>
                        </div>
                    </div>
                `;

                // 为每个项目添加异常原因
                projects.forEach(function(project) {
                    // 计算成本占比的样式
                    const ratio = project.cost_percent / 100;

                    // 分析哪种成本是主要问题
                    const totalCost = project.actual_cost || 0;
                    const bomCost = project.actual_bom_cost || 0;
                    const laborCost = project.actual_labor_cost || 0;
                    const otherCost = project.actual_other_cost || 0;

                    // 计算各类成本占总成本的比例
                    const bomPercent = totalCost > 0 ? (bomCost / totalCost * 100).toFixed(1) : 0;
                    const laborPercent = totalCost > 0 ? (laborCost / totalCost * 100).toFixed(1) : 0;
                    const otherPercent = totalCost > 0 ? (otherCost / totalCost * 100).toFixed(1) : 0;

                    // 确定主要成本类型
                    let mainCostType = '';
                    let mainCostPercent = 0;

                    if (bomCost >= laborCost && bomCost >= otherCost) {
                        mainCostType = 'BOM成本';
                        mainCostPercent = bomPercent;
                    } else if (laborCost >= bomCost && laborCost >= otherCost) {
                        mainCostType = '人工成本';
                        mainCostPercent = laborPercent;
                    } else {
                        mainCostType = '其他成本';
                        mainCostPercent = otherPercent;
                    }

                    if (ratio > 1) {
                        project.abnormalReason = '成本超出预估，主要是' + mainCostType + '（占总成本的' + mainCostPercent + '%）';
                    } else if (ratio >= 0.8) {
                        project.abnormalReason = '成本接近预估上限，主要是' + mainCostType + '（占总成本的' + mainCostPercent + '%）';
                    }
                });

                // 获取仪表盘显示的异常项目数
                const dashboardAbnormalCount = parseInt($('#abnormalCount').text()) || 0;

                // 验证异常项目数量是否一致
                if (dashboardAbnormalCount !== projects.length) {
                    console.warn('异常项目数量不一致：仪表盘显示 ' + dashboardAbnormalCount + ' 个，实际获取到 ' + projects.length + ' 个');
                }

                // 显示弹窗
                const index = layer.open({
                    type: 1,
                    title: '超出预估项目详情（共 ' + projects.length + ' 个）',
                    area: ['80%', '70%'],
                    content: content,
                    success: function(layero, index) {
                        // 定义操作栏模板
                        const operationBar = `
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-xs layui-btn-normal view-detail" lay-event="detail">查看详情</button>
                            </div>
                        `;

                        // 渲染表格
                        table.render({
                            elem: '#abnormal-project-table',
                            data: projects,
                            page: true,
                            limit: 10,
                            limits: [10, 20, 50, 100],
                            cols: [[
                                {field: 'project_name', title: '项目名称', align: 'center'},
                                {field: 'project_code', title: '项目编码', align: 'center'},
                                {field: 'status_name', title: '项目状态', align: 'center'},
                                {field: 'cost_percent', title: '成本占比', align: 'center', templet: function(d) {
                                    // 计算实际成本与预估成本的比例
                                    const ratio = d.cost_percent / 100;
                                    let costClass = '';

                                    if (ratio > 1) {
                                        costClass = 'cost-red';
                                    } else if (ratio >= 0.8) {
                                        costClass = 'cost-yellow';
                                    }

                                    return '<span class="' + costClass + '">' + d.cost_percent + '%</span>';
                                }},
                                {field: 'abnormalReason', title: '异常原因', align: 'center'},
                                {title: '操作', toolbar: operationBar, align: 'center', width: 130}
                            ]],
                            done: function(res, curr, count) {
                                // 自定义样式
                                $(layero).find('th').css({'background-color': '#009688', 'color': '#fff', 'font-weight': '600'});

                                // 为不同成本占比的项目添加特殊样式
                                const tableElem = $(layero).find('#abnormal-project-table').next();
                                tableElem.find('tr').each(function() {
                                    const $costCell = $(this).find('td[data-field="cost_percent"] span');

                                    if ($costCell.hasClass('cost-red')) {
                                        // 超出预估，添加红色警告样式
                                        $(this).addClass('cost-status-critical');
                                        // 将成本占比单元格中的文字颜色改为白色，提高可读性
                                        $costCell.attr('style', 'color: #fff !important');
                                    } else if ($costCell.hasClass('cost-yellow')) {
                                        // 达到预估的80%以上但未超出，添加黄色警告样式
                                        $(this).addClass('cost-status-warning');
                                        // 将成本占比单元格中的文字颜色改为白色，提高可读性
                                        $costCell.attr('style', 'color: #fff !important');
                                    }
                                });
                            }
                        });

                        // 表格工具栏事件
                        table.on('tool(abnormal-project-table)', function(obj) {
                            if (obj.event === 'detail') {
                                // 打开项目详情页
                                layer.open({
                                    type: 2,
                                    title: '项目成本详情',
                                    area: ['80%', '90%'],
                                    content: 'detail/' + obj.data.id,
                                    maxmin: true
                                });
                            }
                        });
                    }
                });
            });
        });

        // 切换同比/环比
        $('.compare-toggle .layui-btn').click(function() {
            $(this).addClass('layui-btn-normal').removeClass('layui-btn-primary')
                .siblings().removeClass('layui-btn-normal').addClass('layui-btn-primary');

            const compare = $(this).data('compare');
            loadDashboardMetrics(compare);
        });

        // 加载图表数据
        loadCostStructureChart();
        loadStatusDistributionChart();

        // 渲染表格
        table.render({
            elem: '#project-table',
            url: 'api/projects',
            page: true,
            limit: 10,
            limits: [10, 20, 50, 100],
            cols: [[
                {type: 'numbers', title: '序号', width: 60, align: 'center'},
                {field: 'project_name', title: '项目名称', align: 'center'},
                {field: 'project_code', title: '项目编码', align: 'center', templet: function(d) {
                    const prefix = d.project_type_name ? d.project_type_name + '-' : '';
                    return prefix + d.project_code;
                }},
                {field: 'status_name', title: '项目状态', align: 'center', templet: function(d) {
                    // 根据状态名称设置不同的颜色样式
                    let statusClass = '';

                    // 尝试从多个可能的属性中获取状态值
                    let statusValue = null;
                    if (d.status_value !== undefined) {
                        statusValue = d.status_value;
                    } else if (d.project_status !== undefined) {
                        statusValue = d.project_status;
                    } else if (d.status !== undefined) {
                        statusValue = d.status;
                    } else if (d.data_value !== undefined) {
                        statusValue = d.data_value;
                    }

                    // 状态映射 - 根据状态值
                    const statusValueMapping = {
                        '0': 'status-pending',    // 未开始
                        '1': 'status-production', // 生产中
                        '2': 'status-production', // 安装中
                        '3': 'status-acceptance', // 验收中
                        '4': 'status-debugging',  // 调试中
                        '5': 'status-modification', // 整改中
                        '7': 'status-completed'   // 已完成
                    };

                    // 状态映射 - 根据状态名称
                    const statusNameMapping = {
                        '未开始': 'status-pending',
                        '生产中': 'status-production',
                        '安装中': 'status-production',
                        '验收中': 'status-acceptance',
                        '调试中': 'status-debugging',
                        '整改中': 'status-modification',
                        '已完成': 'status-completed'
                    };

                    // 优先使用状态值匹配
                    if (statusValue !== null) {
                        statusClass = statusValueMapping[statusValue] || '';
                    }

                    // 如果没有匹配到，使用状态名称匹配
                    if (!statusClass) {
                        statusClass = statusNameMapping[d.status_name] || '';
                    }

                    // 如果还没有匹配到，尝试使用包含关键词的方式（向下兼容）
                    if (!statusClass) {
                        if (d.status_name.includes('草稿')) {
                            statusClass = 'status-draft';
                        } else if (d.status_name.includes('审核中')) {
                            statusClass = 'status-reviewing';
                        } else if (d.status_name.includes('待生产')) {
                            statusClass = 'status-pending';
                        } else if (d.status_name.includes('已取消')) {
                            statusClass = 'status-cancelled';
                        }
                    }

                    // 如果还是没有匹配到，输出调试信息
                    if (!statusClass && console && console.log) {
                        console.log('无法匹配状态颜色:', d.status_name, statusValue, d);
                    }

                    if (statusClass) {
                        return '<span class="' + statusClass + '">' + d.status_name + '</span>';
                    } else {
                        return d.status_name; // 默认不加样式
                    }
                }},
                {field: 'price', title: '项目价格', align: 'center', templet: function(d) {
                    return d.price ? '¥' + d.price.toLocaleString() : '¥0';
                }},
              /*   {field: 'machine_number', title: '机台数量', align: 'center'}, */

                {field: 'actual_bom_cost', title: '实际BOM成本', align: 'center', templet: function(d) {
                    return '¥' + d.actual_bom_cost.toLocaleString();
                }},
                {field: 'actual_labor_cost', title: '实际人工成本', align: 'center', templet: function(d) {
                    return '¥' + d.actual_labor_cost.toLocaleString();
                }},
                {field: 'actual_other_cost', title: '实际其他成本', align: 'center', templet: function(d) {
                    return '¥' + d.actual_other_cost.toLocaleString();
                }},
                {field: 'actual_cost', title: '实际总成本', align: 'center', templet: function(d) {
                    return '¥' + d.actual_cost.toLocaleString();
                }},
                {field: 'cost_percent', title: '成本占比', align: 'center', templet: function(d) {
                    // 计算实际成本与预估成本的比例
                    const ratio = d.cost_percent / 100; // 成本占比是百分比形式，除以100得到比例
                    let costClass = '';

                    if (ratio > 1) {
                        // 超出预估，显示红色
                        costClass = 'cost-red';
                    } else if (ratio >= 0.8) {
                        // 达到预估的80%以上但未超出，显示黄色
                        costClass = 'cost-yellow';
                    } else if (ratio >= 0.6) {
                        // 达到预估的60%以上但未达到80%，显示橙色
                        costClass = 'cost-orange';
                    } else {
                        // 低于预估的60%，显示绿色
                        costClass = 'cost-green';
                    }

                    return '<span class="' + costClass + '">' + d.cost_percent + '%</span>';
                }},
                {title: '操作', toolbar: '#project-bar', align: 'center', width: 130}
            ]],
            toolbar: '#project-toolbar',
            defaultToolbar: [{layEvent: 'refresh', icon: 'layui-icon-refresh'}, 'filter', 'print', 'exports'],
            done: function(res, curr, count) {
                // 自定义样式
                $('th').css({'background-color': '#009688', 'color': '#fff', 'font-weight': '600'});

                // 为不同成本占比的项目添加特殊样式
                const table = this.elem.next();
                table.find('tr').each(function() {
                    const $costCell = $(this).find('td[data-field="cost_percent"] span');

                    if ($costCell.hasClass('cost-red')) {
                        // 超出预估，添加红色警告样式
                        $(this).addClass('cost-status-critical');
                        // 将成本占比单元格中的文字颜色改为白色，提高可读性
                        $costCell.attr('style', 'color: #fff !important');
                    } else if ($costCell.hasClass('cost-yellow')) {
                        // 达到预估的80%以上但未超出，添加黄色警告样式
                        $(this).addClass('cost-status-warning');
                        // 将成本占比单元格中的文字颜色改为白色，提高可读性
                        $costCell.attr('style', 'color: #fff !important');
                    }
                });
            },
            skin: 'line'
        });

        // 表单提交
        form.on('submit(cost-query)', function(data) {
            table.reload('project-table', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
            loadDashboardMetrics($('.compare-toggle .layui-btn-normal').data('compare'), data.field);
            loadCostStructureChart(data.field);
            loadStatusDistributionChart(data.field);
            return false;
        });

        // 表格事件监听
        table.on('tool(project-table)', function(obj) {
            if (obj.event === 'detail') {
                // 查看详情，打开详情页
                layer.open({
                    type: 2,
                    title: '项目成本详情',
                    area: ['80%', '90%'],
                    content: 'detail/' + obj.data.id,
                    maxmin: true
                });
            }
        });

        // 表格工具栏事件
        table.on('toolbar(project-table)', function(obj) {
            if (obj.event === 'refresh') {
                // 刷新表格
                table.reload('project-table');
                // 刷新其他数据
                loadDashboardMetrics($('.compare-toggle .layui-btn-normal').data('compare'));
                loadCostStructureChart();
                loadStatusDistributionChart();
            }
        });

        // 导出Excel按钮
        $('#exportExcel').click(function() {
            window.location.href = 'excel/export';
        });

        // 导入BOM成本按钮
        $('#importBOM').click(function() {
            layer.open({
                type: 2,
                title: '导入BOM成本',
                area: ['60%', '70%'],
                content: 'import/bom',
                maxmin: true
            });
        });

        // 导入其他成本按钮
        $('#importOther').click(function() {
            layer.open({
                type: 2,
                title: '导入其他成本',
                area: ['60%', '70%'],
                content: 'import/other',
                maxmin: true
            });
        });

        // 计算人工成本按钮
        $('#calculateLabor').click(function() {
            layer.open({
                type: 1,
                title: '计算人工成本',
                area: ['600px', '400px'],
                content: `
                    <div class="layui-form" lay-filter="labor-form" style="padding: 20px;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">计算方法</label>
                            <div class="layui-input-block">
                                <input type="radio" name="method" value="actual_salary" title="基于实际薪资" checked>
                                <input type="hidden" name="method" value="actual_salary">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">选择项目</label>
                            <div class="layui-input-block">
                                <select name="project_id" lay-filter="project_select">
                                    <option value="">全部项目</option>
                                    <!-- 项目列表将通过AJAX加载 -->
                                </select>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">开始日期</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="start_date" id="start_date" placeholder="开始日期" autocomplete="off" class="layui-input" lay-verify="required" required>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">结束日期</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="end_date" id="end_date" placeholder="结束日期" autocomplete="off" class="layui-input" lay-verify="required" required>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="submitLabor">开始计算</button>
                                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            </div>
                        </div>
                    </div>
                `,
                success: function(layero, index) {
                    // 加载项目列表
                    $.ajax({
                        url: 'api/projects',
                        data: { limit: 1000 },  // 获取所有项目
                        success: function(res) {
                            if (res.code === 0 && res.data) {
                                let options = '<option value="">全部项目</option>';
                                res.data.forEach(function(project) {
                                    options += '<option value="' + project.id + '">' + project.project_name + '</option>';
                                });
                                $('select[name="project_id"]').html(options);
                                form.render('select', 'labor-form');
                            }
                        }
                    });

                    // 初始化日期选择器
                    laydate.render({
                        elem: '#start_date',
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        value: new Date().toISOString().split('T')[0]  // 今天的日期作为默认值
                    });

                    laydate.render({
                        elem: '#end_date',
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        value: new Date().toISOString().split('T')[0]  // 今天的日期作为默认值
                    });

                    form.render(null, 'labor-form');

                    // 提交计算
                    form.on('submit(submitLabor)', function(data) {
                        // 验证表单
                        if (!data.field.start_date) {
                            layer.msg('请选择开始日期', {icon: 2});
                            return false;
                        }

                        if (!data.field.end_date) {
                            layer.msg('请选择结束日期', {icon: 2});
                            return false;
                        }

                        let formData = new FormData();
                        formData.append('method', 'actual_salary');

                        if (data.field.project_id) {
                            formData.append('project_id', data.field.project_id);
                        }

                        formData.append('start_date', data.field.start_date);
                        formData.append('end_date', data.field.end_date);

                        const loading = layer.load(2);

                        $.ajax({
                            url: 'api/calculate/labor',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(res) {
                                layer.close(loading);
                                if (res.success) {
                                    layer.msg(res.msg, {icon: 1});
                                    layer.close(index);

                                    // 增强表格刷新逻辑，确保数据完全刷新
                                    // 延迟500ms确保后端数据处理完成
                                    setTimeout(function() {
                                        // 强制重载表格数据
                                        table.reload('project-table', {
                                            url: 'api/projects',
                                            page: {
                                                curr: 1
                                            },
                                            where: {
                                                _t: new Date().getTime() // 添加时间戳防止缓存
                                            }
                                        });

                                        // 刷新其他相关数据
                                        loadDashboardMetrics($('.compare-toggle .layui-btn-normal').data('compare'));
                                        loadCostStructureChart();
                                        loadStatusDistributionChart();

                                        // 显示完成提示
                                        layer.alert('人工成本计算已完成，点击"项目列表"中的项目可查看详细成本信息。', {
                                            title: '计算完成',
                                            btn: ['查看项目列表', '关闭'],
                                            yes: function(index) {
                                                layer.close(index);
                                                // 滚动到项目列表
                                                $('html, body').animate({
                                                    scrollTop: $("#project-table").offset().top - 100
                                                }, 500);
                                            }
                                        });
                                    }, 500);
                                } else {
                                    layer.msg(res.msg || '计算失败，请检查所有输入', {icon: 2});
                                }
                            },
                            error: function(xhr) {
                                layer.close(loading);
                                let errorMsg = '计算失败，请稍后重试';
                                if (xhr.responseJSON && xhr.responseJSON.msg) {
                                    errorMsg = xhr.responseJSON.msg;
                                }
                                layer.msg(errorMsg, {icon: 2});
                            }
                        });

                        return false;
                    });
                }
            });
        });

        // 加载仪表盘指标
        function loadDashboardMetrics(compareType, filters) {
            $.ajax({
                url: 'api/dashboard/metrics',
                data: {
                    compare: compareType,
                    ...filters
                },
                success: function(data) {
                    // 更新项目数
                    $('#projectCount').text(data.project_count.value);
                    updateChangeRate('#projectCountChange', data.project_count.change, data.project_count.change_type, compareType);

                    // 更新总成本
                    $('#totalCost').text(data.total_cost.formatted_value);
                    updateChangeRate('#totalCostChange', data.total_cost.change, data.total_cost.change_type, compareType);

                    // 更新平均成本
                    $('#avgCost').text(data.avg_cost.formatted_value);
                    updateChangeRate('#avgCostChange', data.avg_cost.change, data.avg_cost.change_type, compareType);

                    // 更新异常项目数
                    $('#abnormalCount').text(data.abnormal_count.value);
                    updateChangeRate('#abnormalCountChange', data.abnormal_count.change, data.abnormal_count.change_type, compareType);
                },
                error: function() {
                    layer.msg('加载指标数据失败', {icon: 2});
                }
            });
        }

        // 更新变化率显示
        function updateChangeRate(selector, change, changeType, compareType) {
            const $el = $(selector);

            if (changeType === 'increase') {
                $el.find('i').removeClass().addClass('layui-icon layui-icon-up');
            } else {
                $el.find('i').removeClass().addClass('layui-icon layui-icon-down');
            }

            const compareLabel = compareType === 'year' ? '同比' : '环比';
            $el.find('span').text(Math.abs(change).toFixed(1) + '% ' + compareLabel + (changeType === 'increase' ? '增长' : '下降'));
        }

        // 加载成本构成图表
        function loadCostStructureChart(filters) {
            $.ajax({
                url: 'api/cost_structure',
                data: filters,
                success: function(data) {
                    // 确保 data.total 存在，如果不存在则计算总和
                    if (data.total === undefined) {
                        data.total = data.values.reduce((sum, value) => sum + value, 0);
                    }

                    const chartDom = document.getElementById('costStructureChart');
                    const myChart = echarts.init(chartDom);

                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            right: 10,
                            top: 'center',
                            data: data.labels
                        },
                        series: [
                            {
                                name: '成本构成',
                                type: 'pie',
                                radius: ['50%', '70%'],
                                avoidLabelOverlap: false,
                                label: {
                                    show: false,
                                    position: 'center'
                                },
                                emphasis: {
                                    label: {
                                        show: true,
                                        fontSize: '18',
                                        fontWeight: 'bold'
                                    }
                                },
                                labelLine: {
                                    show: false
                                },
                                data: data.labels.map((label, index) => {
                                    return {
                                        value: data.values[index],
                                        name: label
                                    };
                                })
                            }
                        ]
                    };

                    myChart.setOption(option);

                    // 窗口大小变化时自动调整图表大小
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                },
                error: function() {
                    layer.msg('加载成本构成数据失败', {icon: 2});
                }
            });
        }

        // 获取异常项目列表
        function getAbnormalProjects(callback) {
            $.ajax({
                url: 'api/projects',
                data: {
                    abnormal: 1,  // 只获取异常项目
                    limit: 1000   // 获取足够多的记录
                },
                success: function(res) {
                    if (res.code === 0 && res.data) {
                        // 直接使用后端返回的异常项目列表，不再在前端筛选
                        callback(res.data);
                    } else {
                        callback([]);
                    }
                },
                error: function() {
                    layer.msg('获取异常项目失败', {icon: 2});
                    callback([]);
                }
            });
        }

        // 加载项目状态分布图表
        function loadStatusDistributionChart(filters) {
            $.ajax({
                url: 'api/status_distribution',
                data: filters,
                success: function(data) {
                    const chartDom = document.getElementById('statusDistributionChart');
                    const myChart = echarts.init(chartDom);

                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function(params) {
                                const countItem = params[0];
                                const costItem = params[1];
                                return countItem.name + '<br/>' +
                                    countItem.seriesName + ': ' + countItem.value + '<br/>' +
                                    costItem.seriesName + ': ¥' + costItem.value.toLocaleString();
                            }
                        },
                        legend: {
                            data: ['项目数量', '成本总额']
                        },
                        xAxis: {
                            type: 'category',
                            data: data.labels,
                            axisLabel: {
                                interval: 0,  // 强制显示所有标签
                                rotate: 45,   // 旋转标签，减少水平空间占用
                                textStyle: {
                                    fontSize: 12  // 调整字体大小
                                }
                            }
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '项目数量',
                                position: 'left'
                            },
                            {
                                type: 'value',
                                name: '成本总额(万元)',
                                position: 'right',
                                axisLabel: {
                                    formatter: '{value}万'
                                }
                            }
                        ],
                        series: [
                            {
                                name: '项目数量',
                                type: 'bar',
                                barWidth: '30%',
                                data: data.counts
                            },
                            {
                                name: '成本总额',
                                type: 'bar',
                                yAxisIndex: 1,
                                barWidth: '30%',
                                data: data.costs.map(cost => Math.round(cost / 10000))  // 转换为万元
                            }
                        ]
                    };

                    myChart.setOption(option);

                    // 窗口大小变化时自动调整图表大小
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                },
                error: function() {
                    layer.msg('加载项目状态分布数据失败', {icon: 2});
                }
            });
        }
    });
</script>
</html>
