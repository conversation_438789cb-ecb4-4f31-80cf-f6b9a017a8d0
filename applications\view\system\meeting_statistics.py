from flask import Blueprint, render_template, request, jsonify, make_response, send_file
from flask_login import login_required, current_user
from applications.common.utils.rights import authorize
from applications.models import LogInfo,Dept,EmployeeSalary, ProjectManageDept, User, ygong, Import_project, DictType, DictData
from applications.models.import_project import ProjectEstimate
from applications.extensions import db
from sqlalchemy import func, and_, case
from datetime import datetime, time, timedelta
from collections import defaultdict
from sqlalchemy.orm import joinedload
import pandas as pd
import xlsxwriter
import io
import calendar
import holidays  # 添加到文件顶部的 import 语句中
import urllib.parse
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from io import BytesIO

bp = Blueprint('meeting_statistics', __name__, url_prefix='/meeting_statistics')

def get_after_sales_config():
    """获取售后部数据处理配置"""
    dict_data = DictData.query.filter_by(
        type_code='after_sales_data_config',
        data_value='enable_after_sales_data_process'
    ).first()

    if dict_data:
        return dict_data.enable
    return 0  # 默认关闭

def build_dept_tree(depts, parent_id=0):
    """递归构建部门树形结构"""
    tree = []
    for dept in depts:
        if dept.parent_id == parent_id:
            children = build_dept_tree(depts, dept.id)
            node = {
                'id': dept.id,
                'label': dept.dept_name,
                'children': children
            }
            tree.append(node)
    return tree

def get_subordinate_depts(dept_id):
    """获取指定部门及其所有子部门"""
    depts = Dept.query.order_by(Dept.sort).all()
    result = [dept_id]

    def find_children(parent_id):
        for dept in depts:
            if dept.parent_id == parent_id:
                result.append(dept.id)
                find_children(dept.id)

    find_children(dept_id)
    return result

def calculate_overtime_pay(log, base_salary, created_at):
    """计算加班工资
    参数:
        log: 日志记录
        base_salary: 基本工资
        created_at: 日志创建时间
    返回:
        加班工资
    """
    # 获取中国法定节假日
    cn_holidays = holidays.China()

    # 格式化日期为 YYYY-MM-DD 格式，用于检查是否为法定节假日
    date_str = created_at.strftime('%Y-%m-%d')

    # 判断是法定节假日、周末还是普通工作日
    if date_str in cn_holidays:
        # 法定节假日，3倍工资
        rate = round(base_salary / 21.75 / 8 * 3)
        pay = log.overtimeWorkingHours * rate
    else:
        # 判断是工作日还是周末
        weekday = created_at.weekday()  # 0-6, 0是周一, 6是周日

        if weekday < 5:  # 周一到周五
            rate = round(base_salary / 21.75 / 8 * 1.5)  # 取整
            pay = log.overtimeWorkingHours * rate
        else:  # 周六周日
            rate = round(base_salary / 21.75 / 8 * 2)  # 取整
            pay = log.overtimeWorkingHours * rate

    return pay

def get_last_month_range():
    """获取上个月的日期范围"""
    today = datetime.today()
    first_day_of_current_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_day_of_last_month = first_day_of_current_month - timedelta(days=1)
    first_day_of_last_month = last_day_of_last_month.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    return first_day_of_last_month, last_day_of_last_month

def get_current_month_range():
    """获取当前月的日期范围"""
    today = datetime.today()
    first_day_of_current_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    return first_day_of_current_month, today

def get_time_range(time_range):
    """根据时间范围获取开始和结束时间"""
    today = datetime.today()

    # 获取工资表中最新的数据时间
    latest_salary = EmployeeSalary.query.order_by(EmployeeSalary.month.desc()).first()
    if latest_salary:
        latest_month = latest_salary.month
        # 解析 month 字段为日期
        latest_date = datetime.strptime(latest_month, '%Y-%m')
        # 计算最新数据所在月份的最后一天
        next_month = latest_date.replace(day=28) + timedelta(days=4)  # 确保进入下个月
        end_of_month = next_month - timedelta(days=next_month.day)
        end_date = end_of_month.replace(hour=23, minute=59, second=59)  # 结束时间到月底最后一天的23:59:59
    else:
        latest_date = today  # 如果没有工资数据，使用当前日期
        end_date = today.replace(hour=23, minute=59, second=59)  # 结束时间到当天的23:59:59

    if time_range == 'year':
        # 当年，使用最新数据的年份
        start_date = datetime(latest_date.year, 1, 1)
    elif time_range == 'quarter':
        # 当前季度，使用最新数据的季度
        current_quarter = (latest_date.month - 1) // 3 + 1
        start_date = datetime(latest_date.year, 3 * current_quarter - 2, 1)
    elif time_range == 'month':
        # 当前月，使用最新数据的月份
        start_date = datetime(latest_date.year, latest_date.month, 1)
    elif time_range == 'custom':
        # 自定义时间范围
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        if start_date and end_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59)  # 结束时间到23:59:59
        else:
            # 如果没有提供自定义时间，默认查询当年
            start_date = datetime(latest_date.year, 1, 1)
    else:
        # 默认查询当年
        start_date = datetime(latest_date.year, 1, 1)
    return start_date, end_date

def get_month_workdays(year, month):
    """计算指定月份的工作日天数"""
    # 获取当月的日历
    cal = calendar.monthcalendar(year, month)
    workdays = 0
    for week in cal:
        for day in week:
            # 0 表示非本月日期，1-5 表示周一到周五
            if day != 0 and calendar.weekday(year, month, day) < 5:
                workdays += 1
    return workdays

@bp.route('/')
@login_required
@authorize("system:meeting_statistics:main", log=True)
def main():
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)  # 默认每页10条

    # 获取查询参数
    project_prefix = request.args.get('project_prefix')
    project_number = request.args.get('project_number')
    time_range = request.args.get('time_range', 'year')  # 默认查询当年
    dept_id = request.args.get('dept_id')  # 新增部门筛选条件
    location = request.args.get('location')  # 新增工作地点筛选条件

    # 强制重置location参数
    # 只有当请求中明确包含location参数时才使用该值，否则设为None
    print(f"meeting_statistics - 请求参数: {request.args}")
    print(f"meeting_statistics - 原始Location参数: {request.args.get('location')}")

    if 'location' in request.args:
        location = request.args.get('location')
        # 如果location参数为空字符串，将其视为"全部"（即不进行筛选）
        if location == '':
            location = None
            print(f"meeting_statistics - Location参数为空字符串，设置为None")
        else:
            print(f"meeting_statistics - Location参数有值: {location}")
    else:
        location = None  # 明确设置为None表示不筛选
        print(f"meeting_statistics - 请求不包含Location参数，设置为None")

    print(f"meeting_statistics - 处理后的Location值: {location}")

    # 获取时间范围
    start_date, end_date = get_time_range(time_range)

    # 获取所有部门数据
    depts = Dept.query.filter(Dept.status == 1).order_by(Dept.sort).all()

    # 判断当前用户的部门是否有下级部门
    current_dept_id = current_user.dept_id
    has_sub_depts = Dept.query.filter_by(parent_id=current_dept_id, status=1).count() > 0

    # 如果用户没有下级部门且未指定部门筛选参数，自动设置为用户自己的部门
    if dept_id is None and not has_sub_depts and current_user.username != 'admin':
        dept_id = str(current_dept_id)
        print(f"meeting_statistics - 自动设置部门ID为用户部门: {dept_id}")

    # 获取售后部数据处理配置
    after_sales_enabled = get_after_sales_config()

    # 获取售后部、生产部和电控部的部门ID
    after_sales_dept = Dept.query.filter_by(dept_name='售后部').first()
    production_dept = Dept.query.filter_by(dept_name='生产部').first()
    control_dept = Dept.query.filter_by(dept_name='电控部').first()

    after_sales_dept_id = after_sales_dept.id if after_sales_dept else None
    production_dept_id = production_dept.id if production_dept else None
    control_dept_id = control_dept.id if control_dept else None

    # 如果是管理员，获取所有员工
    if current_user.username == 'admin':
        employees = ygong.query.all()
        employee_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).all()
        # 管理员可以查看所有部门
        has_sub_depts = True  # 管理员默认可以查看所有部门
    else:
        # 获取当前用户所属部门
        current_dept_id = current_user.dept_id

        # 获取当前部门及其所有子部门
        subordinate_depts = get_subordinate_depts(current_dept_id)

        # 获取这些部门的所有员工
        employees = ygong.query.filter(ygong.dept_id.in_(subordinate_depts)).all()
        employee_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id.in_(subordinate_depts)).all()

    # 如果用户选择了部门，过滤员工数据
    if dept_id:
        # 特殊处理：如果启用了售后部数据处理，且选择的是售后部
        if after_sales_enabled == 1 and str(dept_id) == str(after_sales_dept_id):
            # 获取售后部自己的员工
            after_sales_employees = ygong.query.filter_by(dept_id=dept_id).all()
            after_sales_employee_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id == dept_id).all()

            # 还需要获取生产部和电控部的员工数据，因为他们的厂外数据会归入售后部
            production_control_employees = ygong.query.filter(ygong.dept_id.in_([production_dept_id, control_dept_id])).all()
            production_control_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id.in_([production_dept_id, control_dept_id])).all()

            # 组合员工和薪资数据
            employees = after_sales_employees + production_control_employees
            employee_salary = after_sales_employee_salary + production_control_salary
        else:
            # 常规部门筛选
            employees = ygong.query.filter_by(dept_id=dept_id).all()
            employee_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id == dept_id).all()

    # 获取每个员工的日志数据，只保留有日志的员工
    employee_logs = []
    all_project_logs = []  # 存储所有项目日志
    salary_data = []

    # 创建一个嵌套的defaultdict来存储按项目分组的数据
    project_stats = defaultdict(lambda: {
        'dept_name': '',
        'location': '',
        'projectPrefix': '',
        'projectNumber': '',
        'total_regular_hours': 0,
        'total_overtime_hours': 0,
        'total_overtime_pay': 0,
        'total_regular_pay': 0,
        'total_pay': 0
    })

    # 创建一个用于合并员工日志数据的字典
    employee_logs_dict = {}

    # 计算每个员工的正工时费率
    employee_rates = {}
    for emp in employees:
        # 按月计算员工的费率
        monthly_rates = {}
        monthly_totals = defaultdict(lambda: {
            'regular_hours': 0,
            'overtime_hours': 0,
            'overtime_pay': 0
        })

        # 首先获取每个月的总工时（包含所有项目的工时）
        logs_by_month = db.session.query(
            func.concat(
                func.year(LogInfo.work_date),
                '-',
                func.month(LogInfo.work_date)
            ).label('month'),
            func.sum(LogInfo.regularWorkingHours).label('total_hours'),
            func.sum(LogInfo.overtimeWorkingHours).label('total_overtime_hours')
        ).filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= start_date,
            LogInfo.work_date <= end_date
            # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 条件，统计所有类型工时
        ).group_by('month').all()

        # 计算每个月的加班费和正工时费率
        for month_data in logs_by_month:
            month = month_data.month
            total_regular_hours = month_data.total_hours or 0
            total_overtime_hours = month_data.total_overtime_hours or 0

            # 获取对应月份的工资数据
            salary = EmployeeSalary.query.filter(
                EmployeeSalary.employee_id == emp.id,
                EmployeeSalary.month == month
            ).first()

            if salary and total_regular_hours > 0:
                # 获取该月所有日志并计算加班费（只包含会议和其他类型的工时）
                month_logs = LogInfo.query.filter(
                    LogInfo.openid == emp.openid,
                    func.concat(
                        func.year(LogInfo.work_date),
                        '-',
                        func.month(LogInfo.work_date)
                    ) == month
                    # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
                ).all()

                # 计算该月的总加班费
                month_overtime_pay = sum(
                    calculate_overtime_pay(log, salary.base_salary, log.work_date)
                    for log in month_logs
                )

                # 正工时费率 = (应发工资 - 加班费) / 总正工时
                regular_rate = (salary.should_pay - salary.overtime_pay) / total_regular_hours if total_regular_hours > 0 else 0
                monthly_rates[month] = {
                    'regular_rate': regular_rate,
                    'should_pay': salary.should_pay,
                    'overtime_pay': salary.overtime_pay
                }

        # 存储员工的费率信息
        employee_rates[emp.id] = monthly_rates

    # 存储原始部门ID与处理后的部门ID映射
    dept_mapping = {}

    # 特殊标记：表示是否正在筛选售后部
    is_filtering_after_sales = after_sales_enabled == 1 and dept_id and str(dept_id) == str(after_sales_dept_id)

    for emp in employees:
        # 当筛选售后部门且启用了售后部数据处理时，跳过生产部和电控部的常规处理
        # 它们的厂外数据会在处理售后部员工时一并获取
        if is_filtering_after_sales and emp.dept_id in [production_dept_id, control_dept_id]:
            continue

        # 构建基本查询
        query = LogInfo.query.filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= start_date,
            LogInfo.work_date <= end_date
            # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
        )

        if project_prefix:
            query = query.filter(LogInfo.projectPrefix.like(f'%{project_prefix}%'))

        if project_number:
            query = query.filter(LogInfo.projectNumber.like(f'%{project_number}%'))

        # 应用特殊部门过滤
        if after_sales_enabled == 1:
            # 工作地点筛选逻辑
            print(f"meeting_statistics - 员工: {emp.name}, 部门ID: {emp.dept_id}, 应用工作地点筛选")
            if location:
                # 用户明确选择了工作地点，尊重用户选择
                print(f"meeting_statistics - 用户选择了工作地点: {location}，应用筛选")
                query = query.filter(LogInfo.projectLocation == location)
            else:
                # 如果location为None，则查询所有工作地点，但需要根据部门特殊处理
                print(f"meeting_statistics - 用户未选择工作地点，应用特殊部门处理逻辑")
                if emp.dept_id in [production_dept_id, control_dept_id]:
                    # 对于生产部和电控部，只计算厂内数据
                    print(f"meeting_statistics - 员工属于生产部或电控部，只显示厂内数据")
                    query = query.filter(LogInfo.projectLocation == '厂内')
                elif emp.dept_id == after_sales_dept_id:
                    # 售后部查看所有地点
                    print(f"meeting_statistics - 员工属于售后部，显示所有地点数据")
                    query = query.filter(LogInfo.projectLocation.in_(['厂内', '厂外']))
                else:
                    # 其他部门查看所有地点
                    print(f"meeting_statistics - 员工属于其他部门，显示所有地点数据")
                    query = query.filter(LogInfo.projectLocation.in_(['厂内', '厂外']))

            # 如果是售后部，还需要添加生产部和电控部的厂外数据
            if emp.dept_id == after_sales_dept_id:
                # 先获取售后部自己的数据
                logs = query.all()

                # 然后获取生产部和电控部的厂外数据
                production_control_employees = ygong.query.filter(
                    ygong.dept_id.in_([production_dept_id, control_dept_id])
                ).all()

                for pc_emp in production_control_employees:
                    pc_query = LogInfo.query.filter(
                        LogInfo.openid == pc_emp.openid,
                        LogInfo.work_date >= start_date,
                        LogInfo.work_date <= end_date,
                        # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
                        LogInfo.projectLocation == '厂外'
                    )

                    if project_prefix:
                        pc_query = pc_query.filter(LogInfo.projectPrefix.like(f'%{project_prefix}%'))

                    if project_number:
                        pc_query = pc_query.filter(LogInfo.projectNumber.like(f'%{project_number}%'))

                    pc_logs = pc_query.all()

                    # 记录日志原始部门
                    for pc_log in pc_logs:
                        dept_mapping[pc_log.id] = pc_emp.dept_id

                    # 将获取的日志添加到售后部的日志中
                    logs.extend(pc_logs)

                # 处理这些日志数据
                for log in logs:
                    log_month = f"{log.work_date.year}-{log.work_date.month}"

                    # 确定日志来自哪个员工
                    original_dept_id = dept_mapping.get(log.id, emp.dept_id)
                    log_emp = emp  # 默认为当前售后部员工

                    # 如果是来自生产部或电控部的厂外数据，找到原始员工
                    if original_dept_id in [production_dept_id, control_dept_id]:
                        # 根据openid找到原始员工
                        pc_emp = ygong.query.filter_by(openid=log.openid).first()
                        if pc_emp:
                            log_emp = pc_emp

                    # 获取该员工该月的工资信息
                    salary = EmployeeSalary.query.filter(
                        EmployeeSalary.employee_id == log_emp.id,
                        EmployeeSalary.month == log_month
                    ).first()

                    # 获取该月的费率信息（对于售后部自己的数据）
                    month_rate_info = employee_rates.get(log_emp.id, {}).get(log_month)

                    if month_rate_info:
                        # 计算加班费
                        overtime_pay = calculate_overtime_pay(log, salary.base_salary if salary else log_emp.base_salary, log.work_date)

                        # 计算正工时费用
                        regular_pay = round(log.regularWorkingHours * month_rate_info['regular_rate'])

                        # 获取员工所属部门名称
                        if original_dept_id in [production_dept_id, control_dept_id] and log.projectLocation == '厂外':
                            # 如果是生产部或电控部的厂外数据，显示为售后部但标记原始部门
                            original_dept = Dept.query.get(original_dept_id)
                            dept_name = f"售后部(来自{original_dept.dept_name if original_dept else '未知部门'})"
                        else:
                            # 正常售后部数据
                            dept_name = after_sales_dept.dept_name if after_sales_dept else '未知部门'

                        # 添加员工日志数据 - 合并相同员工、月份、项目前缀和项目编号的记录
                        emp_log_key = (log_emp.id, log_month,
                                      log.projectLocation if hasattr(log, 'projectLocation') else '未知',
                                      log.projectPrefix if hasattr(log, 'projectPrefix') else '',
                                      log.projectNumber if hasattr(log, 'projectNumber') else '')

                        if emp_log_key in employee_logs_dict:
                            # 已存在相同键的记录，累加数值
                            employee_logs_dict[emp_log_key]['regularWorkingHours'] += log.regularWorkingHours
                            employee_logs_dict[emp_log_key]['overtimeWorkingHours'] += log.overtimeWorkingHours
                            employee_logs_dict[emp_log_key]['overtime_pay'] += overtime_pay
                            employee_logs_dict[emp_log_key]['regular_pay'] += regular_pay
                            employee_logs_dict[emp_log_key]['total_pay'] = (
                                employee_logs_dict[emp_log_key]['overtime_pay'] +
                                employee_logs_dict[emp_log_key]['regular_pay']
                            )
                        else:
                            # 创建新记录
                            employee_logs_dict[emp_log_key] = {
                                'id': log_emp.id,
                                'name': log_emp.name,
                                'dept_id': emp.dept_id,  # 仍然显示为售后部
                                'month': log_month,
                                'location': log.projectLocation if hasattr(log, 'projectLocation') else '未知',
                                'projectPrefix': log.projectPrefix if hasattr(log, 'projectPrefix') else '',
                                'projectNumber': log.projectNumber if hasattr(log, 'projectNumber') else '',
                                'regularWorkingHours': log.regularWorkingHours,
                                'overtimeWorkingHours': log.overtimeWorkingHours,
                                'overtime_pay': overtime_pay,
                                'regular_pay': regular_pay,
                                'total_pay': overtime_pay + regular_pay,
                                'should_pay': month_rate_info['should_pay'],
                                'total_overtime_pay': month_rate_info['overtime_pay']
                            }

                        # 更新项目统计
                        project_key = (
                            dept_name,
                            log.projectLocation if hasattr(log, 'projectLocation') else '未知',
                            log.projectPrefix if hasattr(log, 'projectPrefix') else '',
                            log.projectNumber if hasattr(log, 'projectNumber') else ''
                        )

                        # 更新项目统计
                        project_stats[project_key].update({
                            'dept_name': dept_name,
                            'month': log_month,
                            'location': project_key[1],
                            'projectPrefix': project_key[2],
                            'projectNumber': project_key[3],
                            'total_regular_hours': project_stats[project_key]['total_regular_hours'] + log.regularWorkingHours,
                            'total_overtime_hours': project_stats[project_key]['total_overtime_hours'] + log.overtimeWorkingHours,
                            'total_overtime_pay': project_stats[project_key]['total_overtime_pay'] + overtime_pay,
                            'total_regular_pay': project_stats[project_key]['total_regular_pay'] + regular_pay,
                            'total_pay': project_stats[project_key]['total_pay'] + overtime_pay + regular_pay
                        })

                # 已经处理完售后部及相关数据，继续下一个员工
                continue
        else:
            # 未启用售后部数据处理，正常筛选工作地点
            if location:
                query = query.filter(LogInfo.projectLocation == location)
            else:
                # 如果location为None，则查询所有工作地点
                query = query.filter(LogInfo.projectLocation.in_(['厂内', '厂外']))

        # 常规查询
        logs = query.all()

        if logs:
            for log in logs:
                log_month = f"{log.work_date.year}-{log.work_date.month}"

                # 获取该月的费率信息
                month_rate_info = employee_rates.get(emp.id, {}).get(log_month)

                if month_rate_info:
                  # 获取该员工该月的工资信息
                    salary = EmployeeSalary.query.filter(
                        EmployeeSalary.employee_id == emp.id,
                        EmployeeSalary.month == log_month
                    ).first()

                    # 计算加班费
                    overtime_pay = calculate_overtime_pay(log, salary.base_salary if salary else emp.base_salary, log.work_date)

                    # 计算正工时费用
                    regular_pay = round(log.regularWorkingHours * month_rate_info['regular_rate'])

                    # 添加员工日志数据 - 合并相同员工、月份、项目前缀和项目编号的记录
                    emp_log_key = (emp.id, log_month,
                                  log.projectLocation if hasattr(log, 'projectLocation') else '未知',
                                  log.projectPrefix if hasattr(log, 'projectPrefix') else '',
                                  log.projectNumber if hasattr(log, 'projectNumber') else '')

                    if emp_log_key in employee_logs_dict:
                        # 已存在相同键的记录，累加数值
                        employee_logs_dict[emp_log_key]['regularWorkingHours'] += log.regularWorkingHours
                        employee_logs_dict[emp_log_key]['overtimeWorkingHours'] += log.overtimeWorkingHours
                        employee_logs_dict[emp_log_key]['overtime_pay'] += overtime_pay
                        employee_logs_dict[emp_log_key]['regular_pay'] += regular_pay
                        employee_logs_dict[emp_log_key]['total_pay'] = (
                            employee_logs_dict[emp_log_key]['overtime_pay'] +
                            employee_logs_dict[emp_log_key]['regular_pay']
                        )
                    else:
                        # 创建新记录
                        employee_logs_dict[emp_log_key] = {
                            'id': emp.id,
                            'name': emp.name,
                            'dept_id': emp.dept_id,
                            'month': log_month,
                            'location': log.projectLocation if hasattr(log, 'projectLocation') else '未知',
                            'projectPrefix': log.projectPrefix if hasattr(log, 'projectPrefix') else '',
                            'projectNumber': log.projectNumber if hasattr(log, 'projectNumber') else '',
                            'regularWorkingHours': log.regularWorkingHours,
                            'overtimeWorkingHours': log.overtimeWorkingHours,
                            'overtime_pay': overtime_pay,
                            'regular_pay': regular_pay,
                            'total_pay': overtime_pay + regular_pay,
                            'should_pay': month_rate_info['should_pay'],
                            'total_overtime_pay': month_rate_info['overtime_pay']
                        }

                # 获取员工所属部门名称
                employee_dept = Dept.query.get(emp.dept_id)
                dept_name = employee_dept.dept_name if employee_dept else '未知部门'

                # 更新项目统计
                project_key = (
                    dept_name,
                    log.projectLocation if hasattr(log, 'projectLocation') else '未知',
                    log.projectPrefix if hasattr(log, 'projectPrefix') else '',
                    log.projectNumber if hasattr(log, 'projectNumber') else ''
                )

                # 更新项目统计
                project_stats[project_key].update({
                    'dept_name': dept_name,
                    'month': log_month,
                    'location': project_key[1],
                    'projectPrefix': project_key[2],
                    'projectNumber': project_key[3],
                    'total_regular_hours': project_stats[project_key]['total_regular_hours'] + log.regularWorkingHours,
                    'total_overtime_hours': project_stats[project_key]['total_overtime_hours'] + log.overtimeWorkingHours,
                    'total_overtime_pay': project_stats[project_key]['total_overtime_pay'] + overtime_pay,
                    'total_regular_pay': project_stats[project_key]['total_regular_pay'] + regular_pay,
                    'total_pay': project_stats[project_key]['total_pay'] + overtime_pay + regular_pay
                })

    # 将统计数据转换为列表
    dept_pay_list = list(project_stats.values())

    # 将合并后的员工日志数据转换为列表
    employee_logs = list(employee_logs_dict.values())

    # 处理工资数据
    for salary in employee_salary:
        # 关联查询员工姓名
        employee = ygong.query.filter_by(id=salary.employee_id).first()
        employee_name = employee.name if employee else "未知员工"

        salary_data.append({
            'id': salary.id,
            'employee_id': salary.employee_id,
            'employee_name': employee_name,
            'should_pay': salary.should_pay,
            'actual_salary': salary.actual_salary,
            'base_salary': salary.base_salary,
            'performance_salary': salary.performance_salary,
            'overtime_pay': salary.overtime_pay,
            'created_at': salary.create_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': salary.update_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    # 构建部门树
    dept_tree = build_dept_tree(depts)

    # 新增：统计项目前缀和项目编号的数据
    project_prefix_stats = defaultdict(lambda: {'total_regular': 0, 'total_overtime': 0})
    project_number_stats = defaultdict(lambda: {'total_regular': 0, 'total_overtime': 0})

    for log in all_project_logs:
        project_prefix_stats[log['projectPrefix']]['total_regular'] += log['regularWorkingHours']
        project_prefix_stats[log['projectPrefix']]['total_overtime'] += log['overtimeWorkingHours']

        project_number_stats[log['projectNumber']]['total_regular'] += log['regularWorkingHours']
        project_number_stats[log['projectNumber']]['total_overtime'] += log['overtimeWorkingHours']

    # 将统计数据转换为列表
    project_prefix_data = [{
        'prefix': prefix,
        'total_regular': stats['total_regular'],
        'total_overtime': stats['total_overtime']
    } for prefix, stats in project_prefix_stats.items()]

    project_number_data = [{
        'number': number,
        'total_regular': stats['total_regular'],
        'total_overtime': stats['total_overtime']
    } for number, stats in project_number_stats.items()]

    # 在统计项目前缀和项目编号的数据部分修改如下：
    project_combined_stats = defaultdict(lambda: {'total_regular': 0, 'total_overtime': 0})

    for log in all_project_logs:
        combined_key = f"{log['projectPrefix']}{log['projectNumber']}"
        project_combined_stats[combined_key]['total_regular'] += log['regularWorkingHours']
        project_combined_stats[combined_key]['total_overtime'] += log['overtimeWorkingHours']

    # 将统计数据转换为列表
    project_combined_data = [{
        'combined': combined,
        'total_regular': stats['total_regular'],
        'total_overtime': stats['total_overtime']
    } for combined, stats in project_combined_stats.items()]

    # 在 return render_template 之前添加调试输出
    print("Project Prefix Data:", project_prefix_data)
    print("Project Number Data:", project_number_data)
    print("Project Combined Data:", project_combined_data)

    # 过滤掉正工时费用为0的记录
    employee_logs = [log for log in employee_logs if log['regular_pay'] > 0]

    # 对员工日志数据进行分页
    paginated_employee_logs = employee_logs[(page-1)*per_page:page*per_page]

    # 修改部门费用图表数据生成部分
    dept_chart_data = []

    # 添加调试日志
    print("原始部门费用列表:", dept_pay_list)
    print("请求参数:", request.args)
    print("Location参数:", location)
    print("处理后的Location值:", location)

    if has_sub_depts or current_user.username == 'admin':
        # 如果有下级部门或管理员，显示柱状图
        chart_type = 'bar'

        # 首先，创建一个字典来汇总每个部门的总费用
        dept_total_pay = {}
        for d in dept_pay_list:
            # 如果用户选择了特定位置，只包含匹配的数据
            if location and d['location'] != location:
                continue

            location_key = d['location']
            dept_name = d['dept_name']  # 获取部门名称
            key = location_key  # 使用位置作为键

            if key not in dept_total_pay:
                dept_total_pay[key] = {'total': 0, 'dept_name': dept_name}
            dept_total_pay[key]['total'] += d['total_pay']

        # 只添加有费用数据的部门
        for location_key, data in dept_total_pay.items():
            if data['total'] > 0:  # 只添加费用大于0的部门
                dept_chart_data.append({
                    'name': location_key,  # 位置作为名称
                    'value': data['total'],  # 总费用
                    'dept_name': data['dept_name']  # 部门名称
                })
    else:
        # 如果没有下级部门，显示饼图
        chart_type = 'pie'

        # 直接从部门费用列表中提取唯一的位置及其对应的总费用
        dept_total_pay = {}
        for d in dept_pay_list:
            # 如果用户选择了特定位置，只包含匹配的数据
            if location and d['location'] != location:
                continue

            location_key = d['location']
            dept_name = d['dept_name']  # 获取部门名称

            if location_key not in dept_total_pay:
                dept_total_pay[location_key] = {'total': 0, 'dept_name': dept_name}
            dept_total_pay[location_key]['total'] += d['total_pay']

        # 只添加有费用数据的部门
        for location_key, data in dept_total_pay.items():
            if data['total'] > 0:  # 只添加费用大于0的部门
                dept_chart_data.append({
                    'name': location_key,
                    'value': data['total'],
                    'dept_name': data['dept_name']  # 部门名称
                })

    # 添加调试日志
    print("生成的部门费用图表数据:", dept_chart_data)

    # 将部门费用数据转换为员工级别的数据
    employee_pay_data = []
    for emp in employees:
        # 获取员工的基本工资
        salary = EmployeeSalary.query.filter_by(employee_id=emp.id).order_by(EmployeeSalary.create_at.desc()).first()
        base_salary = salary.base_salary if salary else 0

        # 查询日志，包含所有类型的工时（移除了只包含会议和其他的限制）
        logs = LogInfo.query.filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= start_date,
            LogInfo.work_date <= end_date
            # 移除了 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
        ).all()

        if logs:
            total_regular = sum(log.regularWorkingHours for log in logs)
            total_overtime = sum(log.overtimeWorkingHours for log in logs)
            total_overtime_pay = sum(calculate_overtime_pay(log, base_salary, log.work_date) for log in logs)

            # 计算正工时费用
            rate = salary.should_pay / total_regular if total_regular > 0 else 0
            regular_pay = round(total_regular * rate)

            employee_pay_data.append({
                'name': emp.name,
                'value': regular_pay + total_overtime_pay,
                'dept_name': Dept.query.get(emp.dept_id).dept_name if Dept.query.get(emp.dept_id) else '未知部门'
            })

    # 计算总工时和费用，用于图表显示
    total_regular_hours = sum(log['regularWorkingHours'] for log in employee_logs)
    total_overtime_hours = sum(log['overtimeWorkingHours'] for log in employee_logs)
    total_regular_pay = sum(log['regular_pay'] for log in employee_logs)
    total_overtime_pay = sum(log['overtime_pay'] for log in employee_logs)

    # 根据部门分组的员工费用数据
    dept_employee_data = {}
    for log in employee_logs:
        dept_name = Dept.query.get(log['dept_id']).dept_name if Dept.query.get(log['dept_id']) else '未知部门'
        if dept_name not in dept_employee_data:
            dept_employee_data[dept_name] = {
                'regular_pay': 0,
                'overtime_pay': 0,
                'total_pay': 0
            }
        dept_employee_data[dept_name]['regular_pay'] += log['regular_pay']
        dept_employee_data[dept_name]['overtime_pay'] += log['overtime_pay']
        dept_employee_data[dept_name]['total_pay'] += (log['regular_pay'] + log['overtime_pay'])

    # 将字典转换为列表
    dept_employee_list = [
        {
            'name': dept,
            'regular_pay': data['regular_pay'],
            'overtime_pay': data['overtime_pay'],
            'total_pay': data['total_pay']
        } for dept, data in dept_employee_data.items()
    ]

    # 按项目分组的工时数据
    project_hours_data = {}
    for log in employee_logs:
        project_key = f"{log['projectPrefix']}-{log['projectNumber']}"
        if project_key not in project_hours_data:
            project_hours_data[project_key] = {
                'regular_hours': 0,
                'overtime_hours': 0,
                'total_hours': 0,
                'regular_pay': 0,  # 添加正工时费用
                'overtime_pay': 0,  # 添加加班费用
                'total_pay': 0     # 添加总费用
            }
        project_hours_data[project_key]['regular_hours'] += log['regularWorkingHours']
        project_hours_data[project_key]['overtime_hours'] += log['overtimeWorkingHours']
        project_hours_data[project_key]['total_hours'] += (log['regularWorkingHours'] + log['overtimeWorkingHours'])
        project_hours_data[project_key]['regular_pay'] += log['regular_pay']  # 累加正工时费用
        project_hours_data[project_key]['overtime_pay'] += log['overtime_pay']  # 累加加班费用
        project_hours_data[project_key]['total_pay'] += (log['regular_pay'] + log['overtime_pay'])  # 累加总费用

    # 将字典转换为列表并按总工时排序
    project_hours_list = [
        {
            'name': project,
            'regular_hours': data['regular_hours'],
            'overtime_hours': data['overtime_hours'],
            'total_hours': data['total_hours'],
            'regular_pay': data['regular_pay'],  # 添加正工时费用
            'overtime_pay': data['overtime_pay'],  # 添加加班费用
            'total_pay': data['total_pay']  # 添加总费用
        } for project, data in project_hours_data.items()
    ]
    project_hours_list.sort(key=lambda x: x['total_pay'], reverse=True)  # 按总费用排序

    # 取费用最高的前10个项目
    top_projects = project_hours_list[:10] if project_hours_list else []

    # 在返回模板之前，添加月度汇总数据
    monthly_summary = defaultdict(lambda: defaultdict(lambda: {
        'total_regular_pay': 0,
        'total_overtime_pay': 0,
        'total_pay': 0,
        'should_pay': 0
    }))

    for log in employee_logs:
        month = log['month']
        dept_name = Dept.query.get(log['dept_id']).dept_name if Dept.query.get(log['dept_id']) else '未知部门'

        monthly_summary[month][dept_name]['total_regular_pay'] += log['regular_pay']
        monthly_summary[month][dept_name]['total_overtime_pay'] += log['overtime_pay']
        monthly_summary[month][dept_name]['total_pay'] += log['total_pay']

        # 获取该部门该月份的所有员工应发工资总和
        dept_employees = ygong.query.filter_by(dept_id=log['dept_id']).all()
        for emp in dept_employees:
            salary = EmployeeSalary.query.filter(
                EmployeeSalary.employee_id == emp.id,
                EmployeeSalary.month == month
            ).first()
            if salary:
                monthly_summary[month][dept_name]['should_pay'] = salary.should_pay

    # 转换为列表形式
    monthly_summary_list = [
        {
            'month': month,
            'dept_name': dept_name,
            'total_regular_pay': data['total_regular_pay'],
            'total_overtime_pay': data['total_overtime_pay'],
            'total_pay': data['total_pay'],
            'should_pay': data['should_pay'],
            'difference': data['should_pay'] - data['total_pay']  # 计算差额
        }
        for month, dept_data in monthly_summary.items()
        for dept_name, data in dept_data.items()
    ]

    # 添加调试信息
    print(f"请求参数: {request.args}")
    print(f"Location参数: {request.args.get('location')}")
    location = request.args.get('location', '')
    print(f"处理后的Location值: {location}")

    # 如果用户选择了特定位置，筛选部门费用列表和员工日志数据
    if location:
        print(f"筛选前的部门费用列表长度: {len(dept_pay_list)}")

        # 修改筛选逻辑，考虑部门名称中的位置信息
        if location == '厂外':
            # 如果筛选厂外，包含所有售后部(来自...)的记录，因为这些都是厂外数据
            dept_pay_list = [d for d in dept_pay_list if d['location'] == location or '来自' in d.get('dept_name', '')]
        else:
            # 如果筛选厂内，排除所有售后部(来自...)的记录，因为这些都是厂外数据
            dept_pay_list = [d for d in dept_pay_list if d['location'] == location and '来自' not in d.get('dept_name', '')]

        print(f"筛选后的部门费用列表长度: {len(dept_pay_list)}")
        print(f"筛选后的部门费用列表: {dept_pay_list}")

        print(f"筛选前的员工日志数据长度: {len(employee_logs)}")

        # 同样修改员工日志数据的筛选逻辑
        if location == '厂外':
            # 获取所有部门ID
            dept_ids = {d.id: d.dept_name for d in Dept.query.all()}
            # 筛选员工日志数据，包含售后部的特殊处理数据
            employee_logs = [
                log for log in employee_logs
                if log['location'] == location or
                (log['dept_id'] in dept_ids and '来自' in dept_ids.get(log['dept_id'], ''))
            ]
        else:
            # 如果筛选厂内，排除所有售后部(来自...)的记录
            dept_ids = {d.id: d.dept_name for d in Dept.query.all()}
            employee_logs = [
                log for log in employee_logs
                if log['location'] == location and
                (log['dept_id'] not in dept_ids or '来自' not in dept_ids.get(log['dept_id'], ''))
            ]

        print(f"筛选后的员工日志数据长度: {len(employee_logs)}")

        # 重新计算分页
        paginated_employee_logs = employee_logs[(page-1)*per_page:page*per_page]

    # 在返回模板之前，添加注释说明现在统计所有类型工时
    return render_template('system/meeting_statistics/meeting_main.html',
                         employee_logs=paginated_employee_logs,
                         all_project_logs=all_project_logs,
                         employee_salary=salary_data,
                         dept_pay=dept_pay_list,
                         project_prefix=project_prefix,
                         project_number=project_number,
                         location=location,  # 确保正确传递location参数
                         start_date=start_date.strftime('%Y-%m-%d') if start_date else None,
                         end_date=end_date.strftime('%Y-%m-%d') if end_date else None,
                         time_range=time_range,
                         depts=depts,  # 新增部门数据
                         dept_id=dept_id,  # 新增部门筛选条件
                         has_sub_depts=has_sub_depts,  # 新增：是否有下级部门
                         project_prefix_data=project_prefix_data,
                         project_number_data=project_number_data,
                         project_combined_data=project_combined_data,
                         page=page,
                         per_page=per_page,
                         total=len(employee_logs),  # 添加分页相关参数
                         dept_chart_data=dept_chart_data,
                         employee_pay_data=employee_pay_data,  # 新增
                         chart_type=chart_type,
                         total_regular_hours=total_regular_hours,
                         total_overtime_hours=total_overtime_hours,
                         total_regular_pay=total_regular_pay,
                         total_overtime_pay=total_overtime_pay,
                         dept_employee_list=dept_employee_list,
                         top_projects=top_projects,
                         monthly_summary=monthly_summary_list,
                         after_sales_enabled=after_sales_enabled)  # 添加售后部配置

@bp.route('/current')
@login_required
@authorize("system:meeting_statistics:main", log=True)
def current():
    # 获取员工数据分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)  # 默认每页10条

    # 获取部门费用分页参数
    dept_page = request.args.get('dept_page', 1, type=int)
    dept_per_page = request.args.get('dept_per_page', 10, type=int)  # 默认每页10条

    # 获取筛选参数
    project_prefix = request.args.get('project_prefix')
    project_number = request.args.get('project_number')
    location = request.args.get('location')

    # 获取当前月的日期范围
    current_month_start, current_month_end = get_current_month_range()

    # 计算当月的工作日总工时
    year = current_month_start.year
    month = current_month_start.month
    workdays = get_month_workdays(year, month)
    total_work_hours = workdays * 8  # 每天8小时

    # 获取所有部门数据
    depts = Dept.query.order_by(Dept.sort).all()

    # 如果是管理员，获取所有员工
    if current_user.username == 'admin':
        employees = ygong.query.all()
    else:
        # 获取当前用户所属部门
        current_dept_id = current_user.dept_id

        # 获取当前部门及其所有子部门
        subordinate_depts = get_subordinate_depts(current_dept_id)

        # 获取这些部门的所有员工
        employees = ygong.query.filter(ygong.dept_id.in_(subordinate_depts)).all()

    # 查询当前月的工资数据
    current_month_salaries = EmployeeSalary.query.filter(
        EmployeeSalary.create_at >= current_month_start,
        EmployeeSalary.create_at <= current_month_end
    ).all()

    # 如果没有当前月数据，使用最新一个月的数据
    if not current_month_salaries:
        # 获取最新一个月的工资数据
        latest_salary = EmployeeSalary.query.order_by(EmployeeSalary.create_at.desc()).first()
        if latest_salary:
            latest_month_start = latest_salary.create_at.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            latest_month_end = latest_month_start + timedelta(days=32)
            latest_month_end = latest_month_end.replace(day=1) - timedelta(days=1)
            current_month_salaries = EmployeeSalary.query.filter(
                EmployeeSalary.create_at >= latest_month_start,
                EmployeeSalary.create_at <= latest_month_end
            ).all()

    # 如果没有数据，返回空数据并提示
    if not current_month_salaries:
        return render_template('system/meeting_statistics/current.html',
                             employee_data=[],
                             dept_pay=[],
                             message="没有找到任何数据，请检查数据库或联系管理员。")

    # 计算当前月的工资率
    salary_rate = {}
    for salary in current_month_salaries:
        # 通过 ygong 表找到对应的 openid
        employee = ygong.query.filter_by(id=salary.employee_id).first()
        if not employee:
            continue

        # 获取工资数据对应的月份
        salary_month = salary.month
        if not salary_month:
            print(f"meeting_statistics - 警告: 员工{employee.name}的工资记录没有月份信息")
            continue

        # 解析月份格式 (例如: "2023-3")
        try:
            salary_year, salary_month_num = salary_month.split('-')
            salary_year = int(salary_year)
            salary_month_num = int(salary_month_num)

            # 计算工资月份的开始和结束日期
            salary_month_start = datetime(salary_year, salary_month_num, 1)
            if salary_month_num == 12:
                salary_month_end = datetime(salary_year + 1, 1, 1) - timedelta(days=1)
            else:
                salary_month_end = datetime(salary_year, salary_month_num + 1, 1) - timedelta(days=1)

            # 设置时间为一天的结束
            salary_month_end = salary_month_end.replace(hour=23, minute=59, second=59)

            # 通过 openid 查询与工资月份匹配的日志数据
            salary_month_logs = LogInfo.query.filter(
                LogInfo.openid == employee.openid,
                LogInfo.work_date >= salary_month_start,
                LogInfo.work_date <= salary_month_end
            ).all()

            # 计算该月的总工时和总正工时
            total_month_work_hours = sum(log.regularWorkingHours + log.overtimeWorkingHours for log in salary_month_logs)
            total_month_regular = sum(log.regularWorkingHours for log in salary_month_logs)

            # 调试信息：输出关键数据到应用日志
            print(f"meeting_statistics - 员工: {employee.name}, 工资月份: {salary_month}, 应发工资: {salary.should_pay}, 加班费: {salary.overtime_pay}, 工资月总正工时: {total_month_regular}")

            if total_month_regular > 0:
                # 计算工资率
                rate = (salary.should_pay - salary.overtime_pay) / total_month_regular
                salary_rate[salary.employee_id] = rate
                # 输出计算后的工资率
                print(f"meeting_statistics - 计算的工资率: {rate}")
            else:
                print(f"meeting_statistics - 警告: 员工{employee.name}在工资月份总正工时为0，无法计算工资率")

        except (ValueError, AttributeError) as e:
            print(f"meeting_statistics - 错误: 解析员工{employee.name}的工资月份{salary_month}时出错: {str(e)}")
            continue

        # 查询当前月的日志数据（用于显示）
        current_month_logs = LogInfo.query.filter(
            LogInfo.openid == employee.openid,
            LogInfo.work_date >= current_month_start,
            LogInfo.work_date <= current_month_end
        ).all()

        print(f"meeting_statistics - 员工: {employee.name}, 当前月总正工时: {sum(log.regularWorkingHours for log in current_month_logs)}")

    # 获取每个员工的日志数据，按工作地点、项目前缀、项目编号分类
    employee_data_dict = {}  # 用于聚合相同员工、月份、项目的数据
    dept_pay = defaultdict(lambda: {
        'location': '',
        'projectPrefix': '',
        'projectNumber': '',
        'total_regular_hours': 0,
        'total_overtime_hours': 0,
        'total_overtime_pay': 0,
        'total_regular_pay': 0,
        'total_pay': 0
    })

    # 聚合键的格式：'{name}_{location}_{projectPrefix}_{projectNumber}'
    key_format = "{name}_{location}_{projectPrefix}_{projectNumber}"

    for emp in employees:
        # 获取员工的基本工资
        salary = EmployeeSalary.query.filter_by(employee_id=emp.id).order_by(EmployeeSalary.create_at.desc()).first()
        if not salary:
            continue

        base_salary = salary.base_salary if salary else 0

        # 查询当前月的日志，改为统计所有类型的工时
        logs = LogInfo.query.filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= current_month_start,
            LogInfo.work_date <= current_month_end
            # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
        ).all()

        if logs:  # 只处理有日志的员工
            for log in logs:
                # 根据筛选条件过滤数据
                if project_prefix and log.projectPrefix != project_prefix:
                    continue
                if project_number and log.projectNumber != project_number:
                    continue
                if location and log.projectLocation != location:
                    continue

                # 处理会议和其他类型的工时
                # 计算加班工资和正工时费用
                overtime_pay = calculate_overtime_pay(log, base_salary, log.work_date)
                regular_pay = round(log.regularWorkingHours * salary_rate.get(emp.id, 0))
                total_pay = overtime_pay + regular_pay

                # 累加部门的总加班工资和正工时费用
                key = (log.projectLocation, log.projectPrefix, log.projectNumber)
                dept_pay[key]['location'] = log.projectLocation
                dept_pay[key]['projectPrefix'] = log.projectPrefix
                dept_pay[key]['projectNumber'] = log.projectNumber
                dept_pay[key]['total_regular_hours'] += log.regularWorkingHours
                dept_pay[key]['total_overtime_hours'] += log.overtimeWorkingHours
                dept_pay[key]['total_overtime_pay'] += overtime_pay
                dept_pay[key]['total_regular_pay'] += regular_pay
                dept_pay[key]['total_pay'] += total_pay

                # 创建唯一键用于聚合数据
                agg_key = key_format.format(
                    name=emp.name,
                    location=log.projectLocation if log.projectLocation else '未知',
                    projectPrefix=log.projectPrefix if log.projectPrefix else '',
                    projectNumber=log.projectNumber if log.projectNumber else ''
                )

                # 如果键已存在，则更新数据，否则创建新条目
                if agg_key in employee_data_dict:
                    # 累加工时和费用
                    employee_data_dict[agg_key]['regularWorkingHours'] += log.regularWorkingHours
                    employee_data_dict[agg_key]['overtimeWorkingHours'] += log.overtimeWorkingHours
                    employee_data_dict[agg_key]['overtime_pay'] += overtime_pay
                    employee_data_dict[agg_key]['regular_pay'] += regular_pay
                    employee_data_dict[agg_key]['total_pay'] = employee_data_dict[agg_key]['overtime_pay'] + employee_data_dict[agg_key]['regular_pay']
                else:
                    # 创建新条目
                    employee_data_dict[agg_key] = {
                        'name': emp.name,
                        'location': log.projectLocation if log.projectLocation else '未知',
                        'projectPrefix': log.projectPrefix if log.projectPrefix else '',
                        'projectNumber': log.projectNumber if log.projectNumber else '',
                        'regularWorkingHours': log.regularWorkingHours,
                        'overtimeWorkingHours': log.overtimeWorkingHours,
                        'overtime_pay': overtime_pay,
                        'regular_pay': regular_pay,
                        'total_pay': total_pay
                    }

    # 将聚合后的员工数据转换为列表
    employee_data = list(employee_data_dict.values())

    # 将部门费用数据转换为列表，并过滤掉正工时费用为0的记录
    dept_pay_list = [dept for dept in dept_pay.values() if dept['total_regular_pay'] > 0]

    # 如果用户选择了特定位置，筛选部门费用列表
    if location:
        print(f"筛选前的部门费用列表长度: {len(dept_pay_list)}")

        # 修改筛选逻辑，考虑部门名称中的位置信息
        if location == '厂外':
            # 如果筛选厂外，包含所有售后部(来自...)的记录，因为这些都是厂外数据
            dept_pay_list = [d for d in dept_pay_list if d['location'] == location or '来自' in d.get('dept_name', '')]
        else:
            # 如果筛选厂内，排除所有售后部(来自...)的记录，因为这些都是厂外数据
            dept_pay_list = [d for d in dept_pay_list if d['location'] == location and '来自' not in d.get('dept_name', '')]

        print(f"筛选后的部门费用列表长度: {len(dept_pay_list)}")

    # 过滤掉正工时费用为0的员工记录
    employee_data = [emp for emp in employee_data if emp['regular_pay'] > 0]

    # 如果用户选择了特定位置，筛选员工数据
    if location:
        print(f"筛选前的员工数据长度: {len(employee_data)}")
        if location == '厂外':
            # 如果筛选厂外，包含所有售后部(来自...)的记录，因为这些都是厂外数据
            employee_data = [emp for emp in employee_data if emp['location'] == location or '来自' in emp.get('dept_name', '')]
        else:
            # 如果筛选厂内，排除所有售后部(来自...)的记录，因为这些都是厂外数据
            employee_data = [emp for emp in employee_data if emp['location'] == location and '来自' not in emp.get('dept_name', '')]
        print(f"筛选后的员工数据长度: {len(employee_data)}")

    # 对员工数据进行分页
    paginated_employee_data = employee_data[(page-1)*per_page:page*per_page]

    # 对部门费用数据进行分页
    paginated_dept_pay = dept_pay_list[(dept_page-1)*dept_per_page:dept_page*dept_per_page]

    return render_template('system/meeting_statistics/meeting_current.html',
                         employee_data=paginated_employee_data,
                         dept_pay=paginated_dept_pay,
                         page=page,
                         per_page=per_page,
                         dept_page=dept_page,
                         dept_per_page=dept_per_page,
                         dept_total=len(dept_pay_list),
                         total=len(employee_data),
                         project_prefix=project_prefix,
                         project_number=project_number,
                         location=location)

@bp.route('/employee_hours')
@login_required
@authorize("system:meeting_statistics:main", log=True)
def export_employee_hours():
    # 获取查询参数
    project_prefix = request.args.get('project_prefix')
    project_number = request.args.get('project_number')
    time_range = request.args.get('time_range', 'year')
    dept_id = request.args.get('dept_id')
    start_date, end_date = get_time_range(time_range)

    # 获取员工日志数据
    employee_logs = get_employee_logs(project_prefix, project_number, start_date, end_date, dept_id)

    # 创建DataFrame
    if employee_logs:  # 确保有数据
        df = pd.DataFrame(employee_logs)
        df = df[['name', 'regularWorkingHours', 'overtimeWorkingHours']]
        df.columns = ['员工姓名', '正工时', '加班工时']
    else:
        # 如果没有数据，创建一个空的DataFrame
        df = pd.DataFrame(columns=['员工姓名', '正工时', '加班工时'])

    # 创建Excel文件
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='员工项目工时')

    # 创建响应
    output.seek(0)
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    filename = '员工工时数据.xlsx'
    # 提供ASCII文件名和UTF-8文件名两种格式，增强浏览器兼容性
    ascii_filename = urllib.parse.quote(filename)
    utf8_filename = urllib.parse.quote(filename, encoding='utf-8')

    response.headers['Content-Disposition'] = f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{utf8_filename}'

    return response

@bp.route('/dept_costs')
@login_required
@authorize("system:meeting_statistics:main", log=True)
def export_dept_costs():
    # 获取查询参数（与main函数相同）
    project_prefix = request.args.get('project_prefix')
    project_number = request.args.get('project_number')
    time_range = request.args.get('time_range', 'year')
    dept_id = request.args.get('dept_id')
    start_date, end_date = get_time_range(time_range)

    # 获取部门费用数据（与main函数相同）
    dept_pay_list = get_dept_pay_list(project_prefix, project_number, start_date, end_date, dept_id)

    # 创建DataFrame
    df = pd.DataFrame(dept_pay_list)
    df = df[['dept_name', 'total_overtime_pay', 'total_regular_pay', 'total_pay']]
    df.columns = ['部门名称', '总加班工资', '总正工时费用', '总费用']

    # 创建Excel文件
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='部门费用')

    # 创建响应
    output.seek(0)
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    filename = '部门费用统计.xlsx'
    # 提供ASCII文件名和UTF-8文件名两种格式，增强浏览器兼容性
    ascii_filename = urllib.parse.quote(filename)
    utf8_filename = urllib.parse.quote(filename, encoding='utf-8')

    response.headers['Content-Disposition'] = f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{utf8_filename}'

    return response

# 添加获取数据的辅助函数
def get_employee_logs(project_prefix, project_number, start_date, end_date, dept_id):
    # 获取员工数据
    if current_user.username == 'admin':
        employees = ygong.query.all()
    else:
        current_dept_id = current_user.dept_id
        subordinate_depts = get_subordinate_depts(current_dept_id)
        employees = ygong.query.filter(ygong.dept_id.in_(subordinate_depts)).all()

    if dept_id:
        employees = ygong.query.filter_by(dept_id=dept_id).all()

    employee_logs = []
    for emp in employees:
        # 获取员工的基本工资
        salary = EmployeeSalary.query.filter_by(employee_id=emp.id).order_by(EmployeeSalary.create_at.desc()).first()
        base_salary = salary.base_salary if salary else 0

        # 构建基本查询
        query = LogInfo.query.filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= start_date,
            LogInfo.work_date <= end_date
            # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
        )

        # 添加项目前缀筛选条件
        if project_prefix:
            query = query.filter(LogInfo.projectPrefix.like(f'%{project_prefix}%'))

        # 添加项目编号筛选条件
        if project_number:
            query = query.filter(LogInfo.projectNumber.like(f'%{project_number}%'))

        # 执行查询
        logs = query.all()

        if logs:
            total_regular = sum(log.regularWorkingHours for log in logs)
            total_overtime = sum(log.overtimeWorkingHours for log in logs)
            total_overtime_pay = sum(calculate_overtime_pay(log, base_salary, log.work_date) for log in logs)

            # 计算正工时费用
            rate = (salary.should_pay - salary.overtime_pay) / total_regular if total_regular > 0 else 0
            regular_pay = round(total_regular * rate)

            employee_logs.append({
                'name': emp.name,
                'regularWorkingHours': total_regular,
                'overtimeWorkingHours': total_overtime,
                'overtime_pay': total_overtime_pay,
                'regular_pay': regular_pay
            })

    return employee_logs

def get_dept_pay_list(project_prefix, project_number, start_date, end_date, dept_id):
    # 这里放入与main函数中相同的获取部门费用数据的逻辑
    # 返回与main函数中dept_pay_list相同格式的数据
    pass



@bp.route('/contrast')
@login_required
@authorize("system:meeting_statistics:contrast", log=True)
def contrast():
    # 获取当前用户的部门信息
    current_user_dept = Dept.query.get(current_user.dept_id)
    if not current_user_dept:
        return "未找到用户部门信息", 404

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)

    # 获取筛选参数
    project_code = request.args.get('project_code', '')
    project_type = request.args.get('project_type', '')
    dept_name = request.args.get('dept_name', '')
    location = request.args.get('location', '')

    # 获取当前用户可以查看的部门列表
    def get_subordinate_depts(dept_id):
        """获取指定部门及其所有子部门"""
        depts = []
        dept = Dept.query.get(dept_id)
        if dept:
            depts.append(dept)
            children = Dept.query.filter_by(parent_id=dept_id).all()
            for child in children:
                depts.extend(get_subordinate_depts(child.id))
        return depts

    # 获取可见部门列表
    visible_depts = get_subordinate_depts(current_user.dept_id)
    visible_dept_ids = [dept.id for dept in visible_depts]

    # 构建查询
    query = ProjectEstimate.query\
        .join(Import_project, ProjectEstimate.project_id == Import_project.id)\
        .join(Dept, ProjectEstimate.dept_id == Dept.id)\
        .join(ProjectManageDept, Import_project.dept_id == ProjectManageDept.id)\
        .filter(ProjectEstimate.dept_id.in_(visible_dept_ids))  # 只显示可见部门的数据

    # 应用筛选条件
    if project_code:
        query = query.filter(Import_project.project_code.like(f'%{project_code}%'))
    if project_type:
        query = query.filter(ProjectManageDept.dept_name == project_type)
    if dept_name:
        query = query.filter(Dept.dept_name.like(f'%{dept_name}%'))
    if location:
        query = query.filter(ProjectEstimate.location == location)

    # 获取总记录数和分页数据
    total = query.count()
    project_estimates = query.offset((page - 1) * limit).limit(limit).all()

    contrast_results = []

    for estimate in project_estimates:
        import_project = Import_project.query.get(estimate.project_id)
        if not import_project:
            continue

        project_manage_dept = ProjectManageDept.query.get(import_project.dept_id)
        if not project_manage_dept:
            continue

        admin_dept = Dept.query.get(estimate.dept_id)
        admin_dept_name = admin_dept.dept_name if admin_dept else '未知部门'

        # 根据部门名称决定如何查询日志
        if admin_dept_name == '售后部':
            # 对于售后部，获取电控部和生产部员工的厂外日志
            production_dept = Dept.query.filter_by(dept_name='生产部').first()
            control_dept = Dept.query.filter_by(dept_name='电控部').first()
            dept_ids = [dept.id for dept in [production_dept, control_dept] if dept]

            logs = LogInfo.query.join(ygong, LogInfo.employee_id == ygong.employee_id)\
                                .filter(ygong.dept_id.in_(dept_ids))\
                                .filter(LogInfo.projectPrefix == project_manage_dept.dept_name)\
                                .filter(LogInfo.projectNumber == import_project.project_code)\
                                .filter(LogInfo.projectLocation == '厂外')\
                                .all()
        else:
            # 对于其他部门，只获取本部门的厂内日志
            logs = LogInfo.query.join(ygong, LogInfo.employee_id == ygong.employee_id)\
                                .filter(ygong.dept_id == estimate.dept_id)\
                                .filter(LogInfo.projectPrefix == project_manage_dept.dept_name)\
                                .filter(LogInfo.projectNumber == import_project.project_code)\
                                .filter(LogInfo.projectLocation == estimate.location)\
                                .filter(LogInfo.projectLocation == '厂内')\
                                .all()

        # 计算部门总工时
        total_regular_hours = sum(log.regularWorkingHours for log in logs)
        total_overtime_hours = sum(log.overtimeWorkingHours for log in logs)
        total_hours = total_regular_hours + total_overtime_hours

        # 计算差异值和差异百分比
        difference = total_hours - estimate.estimate_hours
        difference_percentage = (difference / estimate.estimate_hours * 100) if estimate.estimate_hours != 0 else 0

        # 添加到对比结果中
        contrast_results.append({
            "project_id": estimate.project_id,
            "project_code": import_project.project_code,
            "project_name": import_project.project_name,
            "location": estimate.location,
            "dept_id": estimate.dept_id,
            "dept_name": admin_dept_name,
            "project_dept_name": project_manage_dept.dept_name,
            "estimate_hours": estimate.estimate_hours,
            "actual_hours": total_hours,
            "difference": difference,
            "difference_percentage": round(difference_percentage, 2)
        })

    # 获取所有项目类型
    project_manage_depts = ProjectManageDept.query.all()

    # 获取可选择的部门列表（用于筛选下拉框）
    available_depts = visible_depts

    return render_template('system/meeting_statistics/contrast.html',
                         contrast_results=contrast_results,
                         total=total,
                         page=page,
                         limit=limit,
                         project_manage_depts=project_manage_depts,
                         available_depts=available_depts,  # 添加可选择的部门列表
                         current_dept=current_user_dept)   # 添加当前用户部门信息

@bp.route('/export_employee_logs')
@login_required
@authorize("system:meeting_statistics:main", log=True)
def export_employee_logs():
    """导出员工日志数据为Excel"""
    # 获取查询参数和数据（使用与主页面相同的汇总逻辑）
    project_prefix = request.args.get('project_prefix')
    project_number = request.args.get('project_number')
    time_range = request.args.get('time_range', 'year')
    dept_id = request.args.get('dept_id')
    location = request.args.get('location')
    start_date, end_date = get_time_range(time_range)

    # 获取汇总后的数据
    employee_logs = get_employee_logs(
        project_prefix, project_number, time_range,
        dept_id, location, start_date, end_date
    )

    # 创建工作簿和工作表
    wb = Workbook()
    ws = wb.active
    ws.title = "员工工时统计"

    # 设置表头
    headers = [
        '员工姓名', '月份', '工作地点', '项目前缀', '项目编号',
        '正工时', '加班工时', '加班工资', '正工时费用', '总费用'
    ]
    ws.append(headers)

    # 写入数据
    for log in employee_logs:
        row = [
            log['name'],
            log['month'],
            log['location'],
            log['projectPrefix'],
            '无' if not log['projectNumber'] or not str(log['projectNumber']).strip() else str(log['projectNumber']).strip(),
            log['regularWorkingHours'],
            log['overtimeWorkingHours'],
            log['overtime_pay'],
            log['regular_pay'],
            log['total_pay']
        ]
        ws.append(row)

    # 设置列宽
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column].width = adjusted_width

    # 创建响应
    output = BytesIO()
    wb.save(output)
    output.seek(0)

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'员工工时统计_{datetime.now().strftime("%Y%m%d%H%M%S")}.xlsx'
    )

@bp.route('/export_dept_pay')
@login_required
@authorize("system:meeting_statistics:main", log=True)
def export_dept_pay():
    """导出部门费用数据为Excel"""
    # 获取查询参数
    project_prefix = request.args.get('project_prefix')
    project_number = request.args.get('project_number')
    time_range = request.args.get('time_range', 'year')
    dept_id = request.args.get('dept_id')
    location = request.args.get('location')
    start_date, end_date = get_time_range(time_range)

    # 判断当前用户的部门是否有下级部门
    current_dept_id = current_user.dept_id
    has_sub_depts = Dept.query.filter_by(parent_id=current_dept_id, status=1).count() > 0

    # 如果用户没有下级部门且未指定部门筛选参数，自动设置为用户自己的部门
    if dept_id is None and not has_sub_depts and current_user.username != 'admin':
        dept_id = str(current_dept_id)
        print(f"meeting_statistics - 导出函数自动设置部门ID为用户部门: {dept_id}")

    # 获取售后部数据处理配置
    after_sales_enabled = get_after_sales_config()

    # 获取售后部、生产部和电控部的部门ID
    after_sales_dept = Dept.query.filter_by(dept_name='售后部').first()
    production_dept = Dept.query.filter_by(dept_name='生产部').first()
    control_dept = Dept.query.filter_by(dept_name='电控部').first()

    after_sales_dept_id = after_sales_dept.id if after_sales_dept else None
    production_dept_id = production_dept.id if production_dept else None
    control_dept_id = control_dept.id if control_dept else None

    # 获取所有部门数据
    depts = Dept.query.filter(Dept.status == 1).order_by(Dept.sort).all()

    # 如果是管理员，获取所有员工
    if current_user.username == 'admin':
        employees = ygong.query.all()
    else:
        # 获取当前用户所属部门
        current_dept_id = current_user.dept_id

        # 获取当前部门及其所有子部门
        subordinate_depts = get_subordinate_depts(current_dept_id)

        # 获取这些部门的所有员工
        employees = ygong.query.filter(ygong.dept_id.in_(subordinate_depts)).all()

    # 如果用户选择了部门，过滤员工数据
    if dept_id:
        # 特殊处理：如果启用了售后部数据处理，且选择的是售后部
        if after_sales_enabled == 1 and str(dept_id) == str(after_sales_dept_id):
            # 获取售后部自己的员工
            after_sales_employees = ygong.query.filter_by(dept_id=dept_id).all()
            after_sales_employee_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id == dept_id).all()

            # 还需要获取生产部和电控部的员工数据，因为他们的厂外数据会归入售后部
            production_control_employees = ygong.query.filter(ygong.dept_id.in_([production_dept_id, control_dept_id])).all()
            production_control_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id.in_([production_dept_id, control_dept_id])).all()

            # 组合员工和薪资数据
            employees = after_sales_employees + production_control_employees
            employee_salary = after_sales_employee_salary + production_control_salary
        else:
            # 常规部门筛选
            employees = ygong.query.filter_by(dept_id=dept_id).all()
            employee_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id == dept_id).all()

    # 创建一个嵌套的defaultdict来存储按项目分组的数据
    project_stats = defaultdict(lambda: {
        'dept_name': '',
        'month': '',
        'location': '',
        'projectPrefix': '',
        'projectNumber': '',
        'total_regular_hours': 0,
        'total_overtime_hours': 0,
        'total_overtime_pay': 0,
        'total_regular_pay': 0,
        'total_pay': 0
    })

    # 计算每个员工的正工时费率
    employee_rates = {}
    for emp in employees:
        # 按月计算员工的费率
        monthly_rates = {}
        monthly_totals = defaultdict(lambda: {
            'regular_hours': 0,
            'overtime_hours': 0,
            'overtime_pay': 0
        })

        # 首先获取每个月的总工时（包含所有项目的工时）
        logs_by_month = db.session.query(
            func.concat(
                func.year(LogInfo.work_date),
                '-',
                func.month(LogInfo.work_date)
            ).label('month'),
            func.sum(LogInfo.regularWorkingHours).label('total_hours'),
            func.sum(LogInfo.overtimeWorkingHours).label('total_overtime_hours')
        ).filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= start_date,
            LogInfo.work_date <= end_date
            # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 条件，统计所有类型工时
        ).group_by('month').all()

        # 计算每个月的加班费和正工时费率
        for month_data in logs_by_month:
            month = month_data.month
            total_regular_hours = month_data.total_hours or 0
            total_overtime_hours = month_data.total_overtime_hours or 0

            # 获取对应月份的工资数据
            salary = EmployeeSalary.query.filter(
                EmployeeSalary.employee_id == emp.id,
                EmployeeSalary.month == month
            ).first()

            if salary and total_regular_hours > 0:
                # 获取该月所有日志并计算加班费（包含所有项目的工时）
                month_logs = LogInfo.query.filter(
                    LogInfo.openid == emp.openid,
                    func.concat(
                        func.year(LogInfo.work_date),
                        '-',
                        func.month(LogInfo.work_date)
                    ) == month
                    # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
                ).all()

                # 计算该月的总加班费
                month_overtime_pay = sum(
                    calculate_overtime_pay(log, salary.base_salary, log.work_date)
                    for log in month_logs
                )

                # 正工时费率 = (应发工资 - 加班费) / 总正工时
                regular_rate = (salary.should_pay - salary.overtime_pay) / total_regular_hours if total_regular_hours > 0 else 0
                monthly_rates[month] = {
                    'regular_rate': regular_rate,
                    'should_pay': salary.should_pay,
                    'overtime_pay': salary.overtime_pay
                }

        # 存储员工的费率信息
        employee_rates[emp.id] = monthly_rates

    # 创建部门ID映射以便于后续检查
    dept_id_mapping = {
        'after_sales': after_sales_dept_id,
        'production': production_dept_id,
        'control': control_dept_id
    }

    # 判断是否正在筛选售后部数据
    is_filtering_after_sales = (after_sales_enabled == 1 and str(dept_id) == str(after_sales_dept_id))

    for emp in employees:
        # 如果启用了售后部数据处理，且当前筛选的是售后部，则跳过处理生产部和电控部的厂内数据
        if is_filtering_after_sales and emp.dept_id in [production_dept_id, control_dept_id]:
            # 只处理生产部和电控部的厂外数据
            location_filter = '厂外'
        else:
            # 根据用户选择的位置筛选
            location_filter = location

        # 构建基本查询
        query = LogInfo.query.filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= start_date,
            LogInfo.work_date <= end_date
            # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
        )

        if project_prefix:
            query = query.filter(LogInfo.projectPrefix.like(f'%{project_prefix}%'))

        if project_number:
            query = query.filter(LogInfo.projectNumber.like(f'%{project_number}%'))

        # 工作地点筛选逻辑
        if location_filter and location_filter.strip():
            query = query.filter(LogInfo.projectLocation == location_filter)
        elif is_filtering_after_sales and emp.dept_id in [production_dept_id, control_dept_id]:
            # 如果是售后部筛选，对于生产部和电控部只查询厂外数据
            query = query.filter(LogInfo.projectLocation == '厂外')
        else:
            # 如果location为空，则查询所有工作地点
            query = query.filter(LogInfo.projectLocation.in_(['厂内', '厂外']))

        logs = query.all()

        if logs:
            for log in logs:
                log_month = f"{log.work_date.year}-{log.work_date.month}"

                # 获取该月的费率信息
                month_rate_info = employee_rates.get(emp.id, {}).get(log_month)

                if month_rate_info:
                    # 获取该员工该月的工资信息
                    salary = EmployeeSalary.query.filter(
                        EmployeeSalary.employee_id == emp.id,
                        EmployeeSalary.month == log_month
                    ).first()

                    # 使用正确的基本工资
                    base_salary = emp.base_salary
                    if salary and hasattr(salary, 'base_salary'):
                        base_salary = salary.base_salary

                    # 计算加班费
                    overtime_pay = calculate_overtime_pay(log, base_salary, log.work_date)

                    # 计算正工时费用
                    regular_pay = round(log.regularWorkingHours * month_rate_info['regular_rate'])

                    # 获取员工所属部门名称
                    employee_dept = Dept.query.get(emp.dept_id)
                    dept_name = employee_dept.dept_name if employee_dept else '未知部门'

                    # 确定在导出时显示的部门名称
                    display_dept_id = emp.dept_id
                    display_dept_name = dept_name

                    # 如果是生产部或电控部的厂外数据，且启用了售后部数据处理，则归入售后部
                    if after_sales_enabled == 1 and emp.dept_id in [production_dept_id, control_dept_id] and log.projectLocation == '厂外':
                        display_dept_id = after_sales_dept_id
                        after_sales_dept = Dept.query.get(after_sales_dept_id)
                        display_dept_name = after_sales_dept.dept_name if after_sales_dept else '售后部'

                    # 更新项目统计
                    project_key = (
                        display_dept_name,
                        log_month,
                        log.projectLocation if hasattr(log, 'projectLocation') and log.projectLocation else '未知',
                        log.projectPrefix if hasattr(log, 'projectPrefix') and log.projectPrefix else '',
                        '无' if not hasattr(log, 'projectNumber') or not log.projectNumber or not log.projectNumber.strip() else log.projectNumber.strip()
                    )

                    # 更新项目统计
                    project_stats[project_key].update({
                        'dept_name': display_dept_name,
                        'month': log_month,
                        'location': project_key[2],
                        'projectPrefix': project_key[3],
                        'projectNumber': project_key[4],
                        'total_regular_hours': project_stats[project_key]['total_regular_hours'] + log.regularWorkingHours,
                        'total_overtime_hours': project_stats[project_key]['total_overtime_hours'] + log.overtimeWorkingHours,
                        'total_overtime_pay': project_stats[project_key]['total_overtime_pay'] + overtime_pay,
                        'total_regular_pay': project_stats[project_key]['total_regular_pay'] + regular_pay,
                        'total_pay': project_stats[project_key]['total_pay'] + overtime_pay + regular_pay
                    })

    # 将统计数据转换为列表
    dept_pay_list = list(project_stats.values())

    # 创建DataFrame
    if dept_pay_list:
        df = pd.DataFrame(dept_pay_list)
        # 选择需要导出的列并重命名
        df = df[['dept_name', 'month', 'location', 'projectPrefix', 'projectNumber',
                'total_regular_hours', 'total_overtime_hours', 'total_overtime_pay',
                'total_regular_pay', 'total_pay']]
        df.columns = ['部门', '月份', '工作地点', '项目前缀', '项目编号',
                     '正工时', '加班工时', '加班工资', '正工时费用', '总费用']
    else:
        # 如果没有数据，创建一个空的DataFrame
        df = pd.DataFrame(columns=['部门', '月份', '工作地点', '项目前缀', '项目编号',
                                  '正工时', '加班工时', '加班工资', '正工时费用', '总费用'])

    # 创建Excel文件
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='部门费用数据')

        # 获取workbook和worksheet对象
        workbook = writer.book
        worksheet = writer.sheets['部门费用数据']

        # 设置列宽
        worksheet.set_column('A:A', 12)  # 部门
        worksheet.set_column('B:B', 10)  # 月份
        worksheet.set_column('C:C', 10)  # 工作地点
        worksheet.set_column('D:D', 12)  # 项目前缀
        worksheet.set_column('E:E', 12)  # 项目编号
        worksheet.set_column('F:G', 10)  # 工时列
        worksheet.set_column('H:J', 12)  # 费用列

        # 添加格式
        # 表头格式
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D9E1F2',  # 浅蓝色背景
            'border': 1
        })

        # 数值格式
        number_format = workbook.add_format({
            'num_format': '0.00',
            'border': 1
        })

        # 货币格式
        money_format = workbook.add_format({
            'num_format': '#,##0.00',
            'border': 1
        })

        # 文本格式
        text_format = workbook.add_format({
            'border': 1
        })

        # 应用表头格式
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)

        # 应用数据格式
        for row_num in range(1, len(df) + 1):
            # 文本列
            for col_num in range(0, 5):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], text_format)

            # 数值列（工时）
            for col_num in range(5, 7):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], number_format)

            # 货币列（费用）
            for col_num in range(7, 10):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], money_format)

    # 创建响应
    output.seek(0)
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    filename = f'部门费用数据_{datetime.now().strftime("%Y%m%d%H%M%S")}.xlsx'
    # 提供ASCII文件名和UTF-8文件名两种格式，增强浏览器兼容性
    ascii_filename = urllib.parse.quote(filename)
    utf8_filename = urllib.parse.quote(filename, encoding='utf-8')

    response.headers['Content-Disposition'] = f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{utf8_filename}'

    return response

@bp.route('/export_monthly_summary')
@login_required
@authorize("system:meeting_statistics:main", log=True)
def export_monthly_summary():
    """导出月度费用汇总数据为Excel"""
    # 获取查询参数
    project_prefix = request.args.get('project_prefix')
    project_number = request.args.get('project_number')
    time_range = request.args.get('time_range', 'year')
    dept_id = request.args.get('dept_id')
    location = request.args.get('location')
    start_date, end_date = get_time_range(time_range)

    # 获取售后部数据处理配置
    after_sales_enabled = get_after_sales_config()

    # 获取售后部、生产部和电控部的部门ID
    after_sales_dept = Dept.query.filter_by(dept_name='售后部').first()
    production_dept = Dept.query.filter_by(dept_name='生产部').first()
    control_dept = Dept.query.filter_by(dept_name='电控部').first()

    after_sales_dept_id = after_sales_dept.id if after_sales_dept else None
    production_dept_id = production_dept.id if production_dept else None
    control_dept_id = control_dept.id if control_dept else None

    # 获取所有部门数据
    depts = Dept.query.filter(Dept.status == 1).order_by(Dept.sort).all()

    # 如果是管理员，获取所有员工
    if current_user.username == 'admin':
        employees = ygong.query.all()
    else:
        # 获取当前用户所属部门
        current_dept_id = current_user.dept_id

        # 获取当前部门及其所有子部门
        subordinate_depts = get_subordinate_depts(current_dept_id)

        # 获取这些部门的所有员工
        employees = ygong.query.filter(ygong.dept_id.in_(subordinate_depts)).all()

    # 如果用户选择了部门，过滤员工数据
    if dept_id:
        # 特殊处理：如果启用了售后部数据处理，且选择的是售后部
        if after_sales_enabled == 1 and str(dept_id) == str(after_sales_dept_id):
            # 获取售后部自己的员工
            after_sales_employees = ygong.query.filter_by(dept_id=dept_id).all()
            after_sales_employee_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id == dept_id).all()

            # 还需要获取生产部和电控部的员工数据，因为他们的厂外数据会归入售后部
            production_control_employees = ygong.query.filter(ygong.dept_id.in_([production_dept_id, control_dept_id])).all()
            production_control_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id.in_([production_dept_id, control_dept_id])).all()

            # 组合员工和薪资数据
            employees = after_sales_employees + production_control_employees
            employee_salary = after_sales_employee_salary + production_control_salary
        else:
            # 常规部门筛选
            employees = ygong.query.filter_by(dept_id=dept_id).all()
            employee_salary = db.session.query(EmployeeSalary).join(ygong, EmployeeSalary.employee_id == ygong.id).filter(ygong.dept_id == dept_id).all()

    # 获取每个员工的日志数据，只保留有日志的员工
    employee_logs = []

    # 计算每个员工的正工时费率
    employee_rates = {}
    for emp in employees:
        # 按月计算员工的费率
        monthly_rates = {}
        monthly_totals = defaultdict(lambda: {
            'regular_hours': 0,
            'overtime_hours': 0,
            'overtime_pay': 0
        })

        # 首先获取每个月的总工时（包含所有项目的工时）
        logs_by_month = db.session.query(
            func.concat(
                func.year(LogInfo.work_date),
                '-',
                func.month(LogInfo.work_date)
            ).label('month'),
            func.sum(LogInfo.regularWorkingHours).label('total_hours'),
            func.sum(LogInfo.overtimeWorkingHours).label('total_overtime_hours')
        ).filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= start_date,
            LogInfo.work_date <= end_date
            # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
        ).group_by('month').all()

        # 计算每个月的加班费和正工时费率
        for month_data in logs_by_month:
            month = month_data.month
            total_regular_hours = month_data.total_hours or 0
            total_overtime_hours = month_data.total_overtime_hours or 0

            # 获取对应月份的工资数据
            salary = EmployeeSalary.query.filter(
                EmployeeSalary.employee_id == emp.id,
                EmployeeSalary.month == month
            ).first()

            if salary and total_regular_hours > 0:
                # 获取该月所有日志并计算加班费（包含所有项目的工时）
                month_logs = LogInfo.query.filter(
                    LogInfo.openid == emp.openid,
                    func.concat(
                        func.year(LogInfo.work_date),
                        '-',
                        func.month(LogInfo.work_date)
                    ) == month
                ).all()

                # 计算该月的总加班费
                month_overtime_pay = sum(
                    calculate_overtime_pay(log, salary.base_salary, log.work_date)
                    for log in month_logs
                )

                # 正工时费率 = (应发工资 - 加班费) / 总正工时
                regular_rate = (salary.should_pay - salary.overtime_pay) / total_regular_hours
                monthly_rates[month] = {
                    'regular_rate': regular_rate,
                    'should_pay': salary.should_pay,
                    'overtime_pay': salary.overtime_pay
                }

        # 存储员工的费率信息
        employee_rates[emp.id] = monthly_rates

    # 创建部门ID映射以便于后续检查
    dept_id_mapping = {
        'after_sales': after_sales_dept_id,
        'production': production_dept_id,
        'control': control_dept_id
    }

    # 判断是否正在筛选售后部数据
    is_filtering_after_sales = (after_sales_enabled == 1 and str(dept_id) == str(after_sales_dept_id))

    for emp in employees:
        # 如果启用了售后部数据处理，且当前筛选的是售后部，则跳过处理生产部和电控部的厂内数据
        if is_filtering_after_sales and emp.dept_id in [production_dept_id, control_dept_id]:
            # 只处理生产部和电控部的厂外数据
            location_filter = '厂外'
        else:
            # 根据用户选择的位置筛选
            location_filter = location

        # 构建基本查询
        query = LogInfo.query.filter(
            LogInfo.openid == emp.openid,
            LogInfo.work_date >= start_date,
            LogInfo.work_date <= end_date
            # 移除 LogInfo.projectPrefix.in_(['会议', '其他']) 限制，统计所有类型工时
        )

        if project_prefix:
            query = query.filter(LogInfo.projectPrefix.like(f'%{project_prefix}%'))

        if project_number:
            query = query.filter(LogInfo.projectNumber.like(f'%{project_number}%'))

        # 工作地点筛选逻辑
        if location_filter and location_filter.strip():
            query = query.filter(LogInfo.projectLocation == location_filter)
        else:
            # 如果location为空，则查询所有工作地点
            query = query.filter(LogInfo.projectLocation.in_(['厂内', '厂外']))

        logs = query.all()

        if logs:
            for log in logs:
                log_month = f"{log.work_date.year}-{log.work_date.month}"

                # 获取该月的费率信息
                month_rate_info = employee_rates.get(emp.id, {}).get(log_month)

                if month_rate_info:
                    # 获取该员工该月的工资信息
                    salary = EmployeeSalary.query.filter(
                        EmployeeSalary.employee_id == emp.id,
                        EmployeeSalary.month == log_month
                    ).first()

                    # 使用正确的基本工资
                    base_salary = emp.base_salary
                    if salary and hasattr(salary, 'base_salary'):
                        base_salary = salary.base_salary

                    # 计算加班费
                    overtime_pay = calculate_overtime_pay(log, base_salary, log.work_date)

                    # 计算正工时费用
                    regular_pay = round(log.regularWorkingHours * month_rate_info['regular_rate'])

                    # 添加员工日志数据
                    employee_logs.append({
                        'id': emp.id,
                        'name': emp.name,
                        'dept_id': emp.dept_id,
                        'month': log_month,
                        'location': log.projectLocation if hasattr(log, 'projectLocation') else '未知',
                        'projectPrefix': log.projectPrefix if hasattr(log, 'projectPrefix') else '',
                        'projectNumber': log.projectNumber if hasattr(log, 'projectNumber') and log.projectNumber.strip() else '无',
                        'regularWorkingHours': log.regularWorkingHours,
                        'overtimeWorkingHours': log.overtimeWorkingHours,
                        'overtime_pay': overtime_pay,
                        'regular_pay': regular_pay,
                        'total_pay': overtime_pay + regular_pay,
                        'should_pay': month_rate_info['should_pay'],
                        'total_overtime_pay': month_rate_info['overtime_pay']
                    })

    # 生成月度汇总数据
    monthly_summary = defaultdict(lambda: defaultdict(lambda: {
        'total_regular_pay': 0,
        'total_overtime_pay': 0,
        'total_pay': 0,
        'should_pay': 0
    }))

    for log in employee_logs:
        month = log['month']
        # 使用正确的部门信息
        dept_id = log['dept_id']
        dept_name = Dept.query.get(dept_id).dept_name if Dept.query.get(dept_id) else '未知部门'

        monthly_summary[month][dept_name]['total_regular_pay'] += log['regular_pay']
        monthly_summary[month][dept_name]['total_overtime_pay'] += log['overtime_pay']
        monthly_summary[month][dept_name]['total_pay'] += log['total_pay']

        # 获取该部门该月份的所有员工应发工资总和
        dept_employees = ygong.query.filter_by(dept_id=dept_id).all()
        for emp in dept_employees:
            salary = EmployeeSalary.query.filter(
                EmployeeSalary.employee_id == emp.id,
                EmployeeSalary.month == month
            ).first()
            if salary:
                monthly_summary[month][dept_name]['should_pay'] = salary.should_pay

    # 转换为列表形式
    monthly_summary_list = [
        {
            'month': month,
            'dept_name': dept_name,
            'total_regular_pay': data['total_regular_pay'],
            'total_overtime_pay': data['total_overtime_pay'],
            'total_pay': data['total_pay'],
            'should_pay': data['should_pay'],
            'difference': data['should_pay'] - data['total_pay']  # 计算差额
        }
        for month, dept_data in monthly_summary.items()
        for dept_name, data in dept_data.items()
    ]

    # 创建DataFrame
    if monthly_summary_list:
        df = pd.DataFrame(monthly_summary_list)
        # 选择需要导出的列并重命名
        df = df[['month', 'dept_name', 'total_regular_pay', 'total_overtime_pay',
                'total_pay', 'should_pay', 'difference']]
        df.columns = ['月份', '部门', '正工时费用', '加班费用', '总费用', '应发工资', '差额']
    else:
        # 如果没有数据，创建一个空的DataFrame
        df = pd.DataFrame(columns=['月份', '部门', '正工时费用', '加班费用', '总费用', '应发工资', '差额'])

    # 创建Excel文件
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='月度费用汇总')

        # 获取workbook和worksheet对象
        workbook = writer.book
        worksheet = writer.sheets['月度费用汇总']

        # 设置列宽
        worksheet.set_column('A:A', 10)  # 月份
        worksheet.set_column('B:B', 12)  # 部门
        worksheet.set_column('C:G', 12)  # 费用列

        # 添加格式
        # 表头格式
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D9E1F2',  # 浅蓝色背景
            'border': 1
        })

        # 文本格式
        text_format = workbook.add_format({
            'border': 1
        })

        # 货币格式
        money_format = workbook.add_format({
            'num_format': '#,##0.00',
            'border': 1
        })

        # 负值货币格式（红色）
        negative_format = workbook.add_format({
            'num_format': '#,##0.00',
            'font_color': 'red',
            'border': 1
        })

        # 应用表头格式
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)

        # 应用数据格式
        for row_num in range(1, len(df) + 1):
            # 文本列（月份和部门）
            for col_num in range(0, 2):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], text_format)

            # 货币列（费用）
            for col_num in range(2, 6):
                worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], money_format)

            # 差额列（可能为负值）
            difference = df.iloc[row_num-1, 6]
            if difference < 0:
                worksheet.write(row_num, 6, difference, negative_format)
            else:
                worksheet.write(row_num, 6, difference, money_format)

    # 创建响应
    output.seek(0)
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    filename = f'月度费用汇总_{datetime.now().strftime("%Y%m%d%H%M%S")}.xlsx'
    # 提供ASCII文件名和UTF-8文件名两种格式，增强浏览器兼容性
    ascii_filename = urllib.parse.quote(filename)
    utf8_filename = urllib.parse.quote(filename, encoding='utf-8')

    response.headers['Content-Disposition'] = f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{utf8_filename}'

    return response

# 添加配置更新接口，放在文件的适当位置
@bp.route('/update_after_sales_config', methods=['POST'])
@login_required
@authorize("system:meeting_statistics:main", log=True)
def update_after_sales_config():
    # 只允许admin更新配置
    if current_user.username != 'admin':
        return jsonify(success=False, msg="没有权限")

    req_json = request.get_json(force=True)
    enable = req_json.get('enable')

    # 更新字典数据
    dict_data = DictData.query.filter_by(
        type_code='after_sales_data_config',
        data_value='enable_after_sales_data_process'
    ).first()

    if not dict_data:
        # 如果不存在，创建一个新的
        dict_type = DictType.query.filter_by(type_code='after_sales_data_config').first()
        if not dict_type:
            # 如果字典类型不存在，创建字典类型
            dict_type = DictType(
                type_name='售后部数据配置',
                type_code='after_sales_data_config',
                description='用于控制售后部特殊数据处理功能',
                enable=1
            )
            db.session.add(dict_type)
            db.session.commit()

        # 创建字典数据
        dict_data = DictData(
            data_label='启用售后部数据处理',
            data_value='enable_after_sales_data_process',
            type_code='after_sales_data_config',
            is_default=1,
            enable=enable,
            remark='启用后，生产部和电控部的厂外数据将被算入售后部，同时这些部门不会重复计算厂外数据'
        )
        db.session.add(dict_data)
    else:
        # 如果存在，更新enable字段
        dict_data.enable = enable

    db.session.commit()
    return jsonify(success=True, msg="配置已更新")
