<!DOCTYPE html>
<html>
<head>
    <title>修改密码</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">旧密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="oldPassword" lay-verify="title" autocomplete="off"
                               placeholder="请输入旧密码" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">新密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="newPassword" lay-verify="title" autocomplete="off"
                               placeholder="请输入新密码" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">确认密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="confirmPassword" lay-verify="title" autocomplete="off"
                               placeholder="请重复输入新密码" class="layui-input">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit=""
                    lay-filter="edit-password">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['form', 'jquery', 'popup'], function () {
    let form = layui.form
    let $ = layui.jquery
    let popup = layui.popup

    // 修改密码提交
    form.on('submit(edit-password)', function (data) {
      $.ajax({
        url: '/system/user/editPassword',
        data: JSON.stringify(data.field),
        contentType: 'application/json',
        dataType: 'json',
        type: 'put',
        success: function (result) {
          if (result.success) {
            popup.success(result.msg, function () {
              parent.layer.close(parent.layer.getFrameIndex(window.name))
            })
          } else {
            popup.failure(result.msg)
          }
        }
      })
      return false
    })

  })
</script>
</body>
</html>