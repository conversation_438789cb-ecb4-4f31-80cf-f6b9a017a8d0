<!DOCTYPE html>
<html>
<head>
    <title>个人中心</title>
    <style>
        .layui-form-item {
            margin-top: 17px !important;
            margin-bottom: 17px !important;
        }
    </style>
</head>
<body class="pear-container">
<div class="layui-row layui-col-space10">
    {# 左侧栏 #}
    <div class="layui-col-md3">
        {# 个人信息卡片 #}
        <div class="layui-card">
            <div class="layui-card-body" style="padding: 25px;">
                <div class="text-center layui-text">
                    <div class="user-info-head" id="userInfoHead">
                        <img id="avatar" src="{{ user_info.avatar }}" width="115px" height="115px" alt="">
                    </div>
                    <h2 style="padding-top: 20px;font-size: 20px;">{{ user_info.realname }}</h2>
                    <p style="padding-top: 8px;margin-top: 10px;font-size: 13.5px;">China ， 中国</p>
                </div>
            </div>
            <div style="height: 45px;border-top: 1px whitesmoke solid;text-align: center;line-height: 45px;font-size: 13.5px;">
                <span>{{ user_info.remark }}</span>
            </div>
        </div>
        {# 登录信息卡片 #}
        <div class="layui-card">
            <div class="layui-card-header">
                登录记录
            </div>
            <div class="layui-card-body">
                <ul class="list">
                    {% for log in user_logs %}
                        <li class="list-item">
                            <span class="title">{{ log.url }}</span>
                            <span class="footer">{{ log.create_time }}</span>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {# 右侧栏 #}
    <div class="layui-col-md9">
        {# 个人信息卡片 #}
        <div class="layui-card">
            <div class="layui-card-header">个人信息</div>
            <div class="layui-card-body">
                <div class="layui-tab layui-tab-brief">
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <form class="layui-form">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">账号</label>
                                    <div class="layui-input-block">
                                        <input value="{{ user_info.username }}" type="text" readonly name="username"
                                               lay-verify="title"
                                               autocomplete="off" placeholder="请输入标题" class="layui-input">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">姓名</label>
                                    <div class="layui-input-block">
                                        <input value="{{ user_info.realname }}" type="text" name="realName"
                                               lay-verify="title"
                                               autocomplete="off" placeholder="请输入标题" class="layui-input">
                                    </div>
                                </div>

                                <div class="layui-form-item layui-form-text">
                                    <label class="layui-form-label">描述</label>
                                    <div class="layui-input-block">
                                        <textarea placeholder="请输入描述" name="details"
                                                  class="layui-textarea">{{ user_info.remark }}</textarea>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-input-block">
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-submit
                                                lay-filter="user-update">修改资料
                                        </button>
                                        <button class="layui-btn layui-btn-sm edit-password">更改密码</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- {# 个人文章记录 #} -->
        <!-- <div class="layui-card">
            <div class="layui-card-header">
                我的文章
            </div>
            <div class="layui-card-body">
                <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div class="layui-row layui-col-space10" style="margin: 15px;">
                                <div class="layui-col-md1">
                                    <img src="{{ url_for('static', filename='system/admin/images/act.jpg') }}"
                                         style="width: 100%;height: 100%;border-radius: 5px;"/>
                                </div>
                                <div class="layui-col-md11" style="height: 80px;">
                                    <div class="title">为什么程序员们愿意在GitHub上开源自己的成果给别人免费使用和学习？</div>
                                    <div class="content">
                                        “Git的精髓在于让所有人的贡献无缝合并。而GitHub的天才之处，在于理解了Git的精髓。”来一句我们程序员们接地气的话：分享是一种快乐~
                                    </div>
                                    <div class="comment">2020-06-12 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 评论 5 点赞 12 转发
                                        4
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space10" style="margin: 15px;">
                                <div class="layui-col-md1">
                                    <img src="{{ url_for('static', filename='system/admin/images/act.jpg') }}"
                                         style="width: 100%;height: 100%;border-radius: 5px;"/>
                                </div>
                                <div class="layui-col-md11" style="height: 80px;">
                                    <div class="title">为什么程序员们愿意在GitHub上开源自己的成果给别人免费使用和学习？</div>
                                    <div class="content">
                                        “Git的精髓在于让所有人的贡献无缝合并。而GitHub的天才之处，在于理解了Git的精髓。”来一句我们程序员们接地气的话：分享是一种快乐~
                                    </div>
                                    <div class="comment">2020-06-12 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 评论 5 点赞 12 转发
                                        4
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space10" style="margin: 15px;">
                                <div class="layui-col-md1">
                                    <img src="{{ url_for('static', filename='system/admin/images/act.jpg') }}"
                                         style="width: 100%;height: 100%;border-radius: 5px;"/>
                                </div>
                                <div class="layui-col-md11" style="height: 80px;">
                                    <div class="title">为什么程序员们愿意在GitHub上开源自己的成果给别人免费使用和学习？</div>
                                    <div class="content">
                                        “Git的精髓在于让所有人的贡献无缝合并。而GitHub的天才之处，在于理解了Git的精髓。”来一句我们程序员们接地气的话：分享是一种快乐~
                                    </div>
                                    <div class="comment">2020-06-12 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 评论 5 点赞 12 转发
                                        4
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space10" style="margin: 15px;">
                                <div class="layui-col-md1">
                                    <img src="{{ url_for('static', filename='system/admin/images/act.jpg') }}"
                                         style="width: 100%;height: 100%;border-radius: 5px;"/>
                                </div>
                                <div class="layui-col-md11" style="height: 80px;">
                                    <div class="title">为什么程序员们愿意在GitHub上开源自己的成果给别人免费使用和学习？</div>
                                    <div class="content">
                                        “Git的精髓在于让所有人的贡献无缝合并。而GitHub的天才之处，在于理解了Git的精髓。”来一句我们程序员们接地气的话：分享是一种快乐~
                                    </div>
                                    <div class="comment">2020-06-12 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 评论 5 点赞 12 转发
                                        4
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
</div>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['element', 'jquery', 'layer', 'form'], function () {
    let $ = layui.jquery
    let layer = layui.layer
    let form = layui.form
    // 修改密码页面
    $('.edit-password').click(function () {
      layer.open({
        type: 2,
        title: '修改密码',
        shade: 0.1,
        area: ['550px', '280px'],
        content: '/system/user/editPassword'
      })
      return false
    })

    // 修改头像
    $('#avatar').click(function () {
      layer.open({
        type: 2,
        title: '更换图片',
        shade: 0.1,
        area: ['900px', '500px'],
        content: '/system/user/profile',
        btn: ['确定', '取消'],
        yes: function (index, layero) {
          window['layui-layer-iframe' + index].submitForm()
        }
      })
    })

    // 表单提交更改个人信息数据
    form.on('submit(user-update)', function (data) {
      $.ajax({
        url: '/system/user/updateInfo',
        data: JSON.stringify(data.field),
        dataType: 'json',
        contentType: 'application/json',
        type: 'put',
        success: function (result) {
          if (result.success) {
            layer.msg('修改成功', { icon: 1, time: 1000 })
          } else {
            layer.msg('修改失败', { icon: 2, time: 1000 })
          }
        }
      })
      return false
    })
  })
</script>
</body>
</html>
