from flask import Blueprint, request, jsonify
from applications.extensions import db
from applications.models import LogInfo  # 假设有一个 LogInfo 模型

bp = Blueprint('log_detail', __name__)

@bp.route('/get_log_detail', methods=['GET'])
def get_log_detail():
    log_id = request.args.get('id')
    try:
        log = LogInfo.query.get(log_id)
        if log:
            return jsonify({'data': {
                'id': log.id,
                'projectNumber': log.projectNumber,
                'projectPrefix': log.projectPrefix,
                'regularWorkingHours': log.regularWorkingHours,
                'overtimeWorkingHours': log.overtimeWorkingHours,
                'totalHours': log.totalHours,
                'content': log.content,
                'work_date': log.work_date.strftime('%Y-%m-%d') if log.work_date else None
            }, 'status': 200})
        else:
            return jsonify({'message': '日志不存在', 'status': 404})
    except Exception as e:
        return jsonify({'message': f'获取日志详情失败: {str(e)}', 'status': 500})

@bp.route('/update_log', methods=['POST'])
def update_log():
    data = request.get_json()
    log_id = data.get('id')
    try:
        log = LogInfo.query.get(log_id)
        if log:
            log.projectNumber = data.get('projectNumber')
            log.regularWorkingHours = float(data.get('regularWorkingHours', 0))
            log.overtimeWorkingHours = float(data.get('overtimeWorkingHours', 0))
            log.totalHours = float(data.get('totalHours', 0))
            log.content = data.get('content')
            log.work_date = data.get('work_date')
            db.session.commit()
            return jsonify({'message': '日志更新成功', 'status': 200})
        else:
            return jsonify({'message': '日志不存在', 'status': 404})
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'日志更新失败: {str(e)}', 'status': 500}) 