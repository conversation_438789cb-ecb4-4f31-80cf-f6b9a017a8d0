from flask import Blueprint, render_template, request, jsonify

from applications.common import curd
from applications.common.utils import validate
from applications.common.utils.http import success_api, fail_api, table_api
from applications.common.utils.rights import authorize
from applications.common.utils.validate import str_escape
from applications.extensions import db
from applications.schemas import ProjectManageDeptSchema
from applications.models import ProjectManageDept, Import_project

bp = Blueprint('project_management', __name__, url_prefix='/project_management')

@bp.get('/')
@authorize("system:project_management:main" , log=True)
def main():
    return render_template('system/project_management/main.html')


@bp.post('/data')
@authorize("system:project_management:main", log=True)
def data():
    project_manage_dept = ProjectManageDept.query.order_by(ProjectManageDept.sort).all()
    data = ProjectManageDeptSchema(many=True).dump(project_manage_dept)

    # 创建一个字典，用于存储每个节点的子节点
    tree = {}
    for item in data:
        item["children"] = []
        tree[item["id"]] = item

    # 构建树形结构
    root_nodes = []
    for item in data:
        parent_id = item["parent_id"] if item["parent_id"] != 0 else None
        if parent_id is None:
            root_nodes.append(item)
        else:
            if parent_id in tree:
                tree[parent_id]["children"].append(item)

    return table_api(msg="请求成功", data=root_nodes)


@bp.get('/add')
@authorize("system:project_management:add", log=True)
def add():
    return render_template('system/project_management/add.html')


@bp.get('/tree')
@authorize("system:project_management:main", log=True)
def tree():
    project_manage_dept = ProjectManageDept.query.order_by(ProjectManageDept.sort).all()
    power_data = curd.model_to_dicts(schema=ProjectManageDeptSchema, data=project_manage_dept)
    res = {
        "status": {"code": 200, "message": "默认"},
        "data": power_data

    }
    return jsonify(res)


@bp.post('/save')
@authorize("system:project_management:add", log=True)
def save():
    req_json = request.get_json(force=True)
    project_manage_dept = ProjectManageDept(
        parent_id=req_json.get('parentId'),
        dept_name=str_escape(req_json.get('deptName')),
        sort=str_escape(req_json.get('sort')),
        leader=str_escape(req_json.get('leader')),
        phone=str_escape(req_json.get('phone')),
        email=str_escape(req_json.get('email')),
        status=str_escape(req_json.get('status')),
        address=str_escape(req_json.get('address'))
    )
    r = db.session.add(project_manage_dept)
    db.session.commit()
    return success_api(msg="添加部门成功")


@bp.get('/edit')
@authorize("system:project_management:edit", log=True)
def edit():
    _id = request.args.get("deptId")
    project_manage_dept = curd.get_one_by_id(model=ProjectManageDept, id=_id)
    return render_template('system/project_management/edit.html', project_manage_dept=project_manage_dept)


# 启用
@bp.put('/enable')
@authorize("system:project_management:edit", log=True)
def enable():
    id = request.get_json(force=True).get('deptId')
    if id:
        enable = 1
        d = ProjectManageDept.query.filter_by(id=id).update({"status": enable})
        if d:
            db.session.commit()
            return success_api(msg="启用部门成功")
        return fail_api(msg="出错啦")
    return fail_api(msg="数据错误")


# 禁用
@bp.put('/disable')
@authorize("system:project_management:edit", log=True)
def dis_enable():
    id = request.get_json(force=True).get('deptId')
    if id:
        enable = 0
        d = ProjectManageDept.query.filter_by(id=id).update({"status": enable})
        if d:
            db.session.commit()
            return success_api(msg="禁用部门成功")
        return fail_api(msg="出错啦")
    return fail_api(msg="数据错误")


@bp.put('/update')
@authorize("system:project_management:edit", log=True)
def update():
    json = request.get_json(force=True)
    # id = json.get("deptId"),
    id = str_escape(json.get("deptId"))
    data = {
        "dept_name": validate.str_escape(json.get("deptName")),
        "sort": validate.str_escape(json.get("sort")),
        "leader": validate.str_escape(json.get("leader")),
        "phone": validate.str_escape(json.get("phone")),
        "email": validate.str_escape(json.get("email")),
        "status": validate.str_escape(json.get("status")),
        "address": validate.str_escape(json.get("address"))
    }
    d = ProjectManageDept.query.filter_by(id=id).update(data)
    if not d:
        return fail_api(msg="更新部门失败")
    db.session.commit()
    return success_api(msg="更新部门成功")


@bp.delete('/remove/<int:_id>')
@authorize("system:project_management:remove", log=True)
def remove(_id):
    d = ProjectManageDept.query.filter_by(id=_id).delete()

    if not d:
        return fail_api(msg="删除部门失败")

    Import_project.query.filter_by(dept_id=_id).update({"dept_id": None})
    db.session.commit()

    return success_api(msg="删除部门成功")

# 批量删除
@bp.delete('/batchRemove')
@authorize("system:project_management:remove", log=True)
def batch_remove():
    ids = request.form.getlist('ids[]')

    if not ids:
        return fail_api(msg="未提供删除 ID")

    for id in ids:

        if not id.isdigit():
            db.session.rollback()
            return fail_api(msg="参数提供错误")

        d = ProjectManageDept.query.filter_by(id=id).delete()

        if not d:
            return fail_api(msg="删除部门失败")

        Import_project.query.filter_by(dept_id=id).update({"dept_id": None})

    db.session.commit()
    return success_api(msg="删除部门成功")
