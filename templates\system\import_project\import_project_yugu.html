<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>预估项目</title>
        {% include 'system/common/header.html' %}
        <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
        <script src="{{ url_for('static', filename='index/js/jquery.min.js') }}"></script>
        <script src="{{ url_for('static', filename='index/layui/layui.js') }}"></script>
    </head>
    <body class="pear-container">
        <!-- 查询表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form" action="" lay-filter="user-query-form">
                    <div class="layui-form-item" style="margin-bottom: unset;">
                        <label class="layui-form-label">项目类型</label>
                        <div class="layui-input-inline">
                            <input type="text" name="project_name" placeholder="" class="layui-input">
                        </div>
                        <label class="layui-form-label">项目编号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="project_code" placeholder="" class="layui-input">
                        </div>
                        <label class="layui-form-label">部门</label>
                        <div class="layui-input-inline">
                            <select name="dept_id" lay-filter="dept">
                                <option value="">请选择部门</option>
                                {% for dept in all_depts %}
                                    <option value="{{ dept.id }}">{{ dept.dept_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <button class="layui-btn layui-btn-md" lay-submit lay-filter="user-query">
                            <i class="layui-icon layui-icon-search"></i>
                            查询
                        </button>
                        <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                            <i class="layui-icon layui-icon-refresh"></i>
                            重置
                        </button>
                    </div>
                </form>
            </div>

                <!-- <div class="layui-card-body">
                    <form class="layui-form" id="uploadForm" enctype="multipart/form-data">

                    </form>
                </div> -->
        </div>

        <!-- 导入表单 -->


        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="yugu-table" lay-filter="yugu-table"></table>
            </div>
        </div>

    </body>

    {% include 'system/common/footer.html' %}

    <!-- 表格操作 -->
    {# 表格操作 #}
    <script type="text/html" id="yugu-toolbar">
        {% if authorize("system:import_project:import") %}
            <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="import">
                <i class="pear-icon pear-icon-import"></i>
                导入
            </button>
        {% endif %}
        <!-- <button class="layui-btn layui-btn-sm" lay-event="collasped">
            <i class="pear-icon pear-icon-modular"></i>
            高级
        </button> -->
    </script>

    <!-- 表格列定义 -->
    <script type="text/html" id="yugu-createTime">
        {% raw %}
        {{ layui.util.toDateString(d.created_at, "yyyy-MM-dd HH:mm:ss") }}
        {% endraw %}
    </script>

    <script>
        layui.use(['table', 'form', 'upload'], function () {
            let table = layui.table;
            let form = layui.form;
            let upload = layui.upload;
            let $ = layui.jquery;

            // 表格数据
            let cols = [
                [
                    {title: '项目类型', field: 'project_type', align: 'center'},
                    {title: '项目编号', field: 'project_code', align: 'center'},
                    {title: '项目名称', field: 'project_name', align: 'center'},
                    {title: '部门名称', field: 'user_dept_name', align: 'center'},
                    {title: '地点', field: 'location', align: 'center'},
                    {title: '预估工时', field: 'estimate_hours', align: 'center'},
                    {title: '人工成本', field: 'labor_cost', align: 'center'},
                    {title: '机械BOM成本', field: 'mechanical_bom_cost', align: 'center'},
                    {title: '电气BOM成本', field: 'electrical_bom_cost', align: 'center'},
                    {title: '其他成本', field: 'other_cost', align: 'center'},
                    {title: '创建时间', field: 'created_at', templet: '#yugu-createTime', align: 'center'}
                ]
            ];

            // 渲染表格
            table.render({
                elem: '#yugu-table',
                url: '{{ url_for("system.import_project.yugu_data") }}',
                page: true,
                cols: cols,
                skin: 'line',
                toolbar: '#yugu-toolbar',
                defaultToolbar: ['filter', 'print', 'exports'],
                response: {
                    statusCode: 200 // 设置响应状态码为 200
                },
                parseData: function(res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data
                    };
                }
            });

            // 查询表单提交
            form.on('submit(user-query)', function (data) {
                table.reload('yugu-table', {
                    where: data.field
                });
                return false;
            });

            // 表格工具栏事件
            table.on('toolbar(yugu-table)', function (obj) {
                if (obj.event === 'import') {
                    layer.open({
                        type: 2,
                        title: '导入预估项目',
                        content: '{{ url_for("system.import_project.yugu_import_page") }}',
                        area: ['500px', '300px'],
                        btn: ['关闭'],
                        yes: function(index, layero){
                            layer.close(index);
                        }
                    });
                }
            });
        });
    </script>
</html>
