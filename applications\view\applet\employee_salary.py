from flask import Blueprint, request, jsonify, session, redirect
from applications.extensions import db
from applications.models import EmployeeSalary, ygong 

bp = Blueprint('employee_salary_list', __name__)

@bp.route('/employee_salary_list', methods=['GET'])
def employee_salary_list():
    # if 'admin' not in session:
    #     return redirect('/admin/login')
    try:
        openid = request.args.get('openid')
        if not openid:
            return jsonify({'message': '缺少openid参数', 'status': 400})
            
        # 先通过openid找到员工
        employee = ygong.query.filter_by(openid=openid).first()
        if not employee:
            return jsonify({'message': '未找到该员工', 'status': 404})
            
        # 通过员工ID查询工资信息，按月份降序排序并只返回最近3个月的数据
        logs = EmployeeSalary.query.filter_by(employee_id=employee.id).order_by(EmployeeSalary.month.desc()).limit(3).all()
        results = [{
            'id': log.id,
            'name': employee.name,
            'month': log.month,
            'base_salary': log.base_salary,
            'performance_salary': log.performance_salary,
            'supervisor_assessment': log.supervisor_assessment,
            'assessment_coefficient': log.assessment_coefficient,
            'position_allowance': log.position_allowance,
            'full_attendance': log.full_attendance,
            'overtime_pay': log.overtime_pay,
            'housing_subsidy': log.housing_subsidy,
            'project_assessment': log.project_assessment,
            'high_temp_allowance': log.high_temp_allowance,
            'other_allowance': log.other_allowance,
            'leave_deduction': log.leave_deduction,
            'other_deduction': log.other_deduction,
            'should_pay': log.should_pay,
            'insurance_deduction': log.insurance_deduction,
            'tax_deduction': log.tax_deduction,
            'year_end_tax': log.year_end_tax,
            'total_deduction': log.total_deduction,
            'actual_salary': log.actual_salary,
            'view_status': log.view_status
        } for log in logs]
        return jsonify({'data': results, 'status': 200})
    except Exception as e:
        return jsonify({'message': f'获取工资列表失败: {str(e)}', 'status': 500})

@bp.route('/employee_salary_detail/<int:salary_id>', methods=['GET'])
def employee_salary_detail(salary_id):
    try:
        salary = EmployeeSalary.query.get(salary_id)
        if not salary:
            return jsonify({'message': '未找到该工资记录', 'status': 404})
            
        return jsonify({
            'data': {
                'id': salary.id,
                'month': salary.month,
                'base_salary': salary.base_salary,
                'performance_salary': salary.performance_salary,
                'overtime_pay': salary.overtime_pay,
                'actual_salary': salary.actual_salary,
                'performance_salary': salary.performance_salary,
                'supervisor_assessment': salary.supervisor_assessment,
                'assessment_coefficient': salary.assessment_coefficient,
                'full_attendance': salary.full_attendance,
                'overtime_pay': salary.overtime_pay,
                'project_assessment': salary.project_assessment,
                'high_temp_allowance': salary.high_temp_allowance,
                'other_deduction': salary.other_deduction,
                'should_pay': salary.should_pay,
                'tax_deduction': salary.tax_deduction,
                'year_end_tax': salary.year_end_tax,
                'total_deduction': salary.total_deduction,
                'details': {
                'position_allowance': salary.position_allowance,
                'housing_subsidy': salary.housing_subsidy,
                'other_allowance': salary.other_allowance,
                'leave_deduction': salary.leave_deduction,
                'insurance_deduction': salary.insurance_deduction
                },
                'view_status': salary.view_status
            },
            'status': 200
        })
    except Exception as e:
        return jsonify({'message': f'获取工资详情失败: {str(e)}', 'status': 500})

@bp.route('/update_salary_status', methods=['POST'])
def update_salary_status():
    try:
        data = request.get_json()
        salary_id = data.get('id')
        print(f"Received data: {data}")  # 打印接收到的数据
        print(f"Salary ID from URL: {salary_id}")  # 打印 URL 中的 salary_id
        
        status = data.get('status')
        if status not in ['confirmed', 'disputed']:
            return jsonify({'message': '无效的状态值', 'status': 400})
            
        salary = EmployeeSalary.query.get(salary_id)
        if not salary:
            return jsonify({'message': '未找到该工资记录', 'status': 404})
            
        salary.view_status = status
        db.session.commit()
        
        return jsonify({'message': '状态更新成功', 'status': 200})
    except Exception as e:
        db.session.rollback()
        return jsonify({'message': f'状态更新失败: {str(e)}', 'status': 500})

@bp.route('/unread_salary_count', methods=['GET'])
def get_unread_salary_count():
    try:
        openid = request.args.get('openid')
        if not openid:
            return jsonify({'status': 400, 'message': '缺少openid参数'})

        # 先通过openid找到员工
        employee = ygong.query.filter_by(openid=openid).first()
        if not employee:
            return jsonify({'status': 404, 'message': '未找到该员工'})

        # 通过员工ID查询未读工资条数量
        unread_count = EmployeeSalary.query.filter_by(
            employee_id=employee.id,
            view_status='pending'
        ).count()

        return jsonify({
            'status': 200,
            'data': {
                'count': unread_count
            }
        })
    except Exception as e:
        return jsonify({
            'status': 500,
            'message': f'获取未读工资条数量失败: {str(e)}'
        })