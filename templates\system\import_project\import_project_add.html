<!DOCTYPE html>
<html>
<head>
    <title>项目导入</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">项目名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="project_name" lay-verify="title" autocomplete="off" placeholder="项目名称" class="layui-input">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">项目号</label>
                    <div class="layui-input-block">
                        <input type="text" name="project_code" lay-verify="title" autocomplete="off" placeholder="项目号" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">项目类型</label>
                    <div class="layui-input-block">
                        <ul id="selectParent" name="deptId" class="dtree" data-id="0"></ul>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">项目状态</label>
                    <div class="layui-input-block">
                        <select name="project_status" lay-verify="required">
                            {% for option in status_options %}
                            <option value="{{ option.data_value }}">{{ option.data_label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">项目价格</label>
                    <div class="layui-input-block">
                        <input type="text" name="price" lay-verify="title" autocomplete="off" placeholder="项目价格" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">币种</label>
                    <div class="layui-input-block">
                        <select name="currency" lay-verify="required">
                            {% for option in currency_options %}
                            <option value="{{ option.data_label }}" {% if option.is_default == 1 %}selected{% endif %}>{{ option.data_label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">机器数量</label>
                    <div class="layui-input-block">
                        <input type="text" name="machine_number" lay-verify="title" autocomplete="off" placeholder="机器数量" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">计划交货时间</label>
                    <div class="layui-input-block">
                        <input type="text" name="delivery_date" id="delivery_date" lay-verify="date" placeholder="yyyy-MM-dd" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">预付款(%)</label>
                    <div class="layui-input-block">
                        <input type="text" name="prepayment" lay-verify="percent" autocomplete="off" placeholder="预付款百分比" class="layui-input payment-percent">
                        <div class="layui-form-mid layui-word-aux payment-amount">金额: 0</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">预付款天数</label>
                    <div class="layui-input-block">
                        <input type="text" name="prepayment_days" lay-verify="days" autocomplete="off" placeholder="预付款天数" class="layui-input" value="0">
                        <div class="layui-form-mid layui-word-aux">合同开始日期后的天数</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发货款(%)</label>
                    <div class="layui-input-block">
                        <input type="text" name="delivery_payment" lay-verify="percent" autocomplete="off" placeholder="发货款百分比" class="layui-input payment-percent">
                        <div class="layui-form-mid layui-word-aux payment-amount">金额: 0</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发货款天数</label>
                    <div class="layui-input-block">
                        <input type="text" name="delivery_payment_days" lay-verify="days" autocomplete="off" placeholder="发货款天数" class="layui-input" value="0">
                        <div class="layui-form-mid layui-word-aux">发货完成日期后的天数</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">验收款(%)</label>
                    <div class="layui-input-block">
                        <input type="text" name="acceptance_payment" lay-verify="percent" autocomplete="off" placeholder="验收款百分比" class="layui-input payment-percent">
                        <div class="layui-form-mid layui-word-aux payment-amount">金额: 0</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">验收款天数</label>
                    <div class="layui-input-block">
                        <input type="text" name="acceptance_payment_days" lay-verify="days" autocomplete="off" placeholder="验收款天数" class="layui-input" value="0">
                        <div class="layui-form-mid layui-word-aux">验收完成日期后的天数</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">质保金(%)</label>
                    <div class="layui-input-block">
                        <input type="text" name="warranty_payment" lay-verify="percent" autocomplete="off" placeholder="质保金百分比" class="layui-input payment-percent">
                        <div class="layui-form-mid layui-word-aux payment-amount">金额: 0</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">质保金天数</label>
                    <div class="layui-input-block">
                        <input type="text" name="warranty_payment_days" lay-verify="days" autocomplete="off" placeholder="质保金天数" class="layui-input" value="365">
                        <div class="layui-form-mid layui-word-aux">验收完成日期后的天数</div>
                    </div>
                </div>
                <!-- <div class="layui-form-item">
                    <label class="layui-form-label">参与部门</label>
                    <div class="layui-input-block">
                        <div id="dept-select" style="max-height: 200px; overflow-y: auto;">
                            {% for dept in all_depts %}
                            <input type="checkbox" name="dept_ids[]" value="{{ dept.id }}" title="{{ dept.dept_name }}"
                                   lay-skin="primary">
                            {% endfor %}
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="user-save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['form', 'jquery', 'dtree', 'laydate'], function () {
    let form = layui.form
    let $ = layui.jquery
    let laydate = layui.laydate

    // 初始化日期选择器
    laydate.render({
        elem: '#delivery_date',
        trigger: 'click'
    });

    let dtree = layui.dtree
    // 项目类型树状图菜单
    dtree.renderSelect({
        elem: '#selectParent',
        url: '/system/project_management/tree',
        method: 'get',
        selectInputName: {nodeId: 'deptId', context: 'deptName'},
        skin: 'layui',
        dataFormat: 'list',
        response: {treeId: 'id', parentId: 'parent_id', title: 'dept_name'}
    })

    // 添加百分比和天数验证规则
    form.verify({
        percent: function(value, item) {
            if (value !== '' && (isNaN(value) || parseFloat(value) < 0 || parseFloat(value) > 100)) {
                return '百分比必须在0-100之间';
            }
        },
        days: function(value, item) {
            if (value !== '' && (isNaN(value) || parseFloat(value) < 0 || !Number.isInteger(parseFloat(value)))) {
                return '天数必须是非负整数';
            }
        }
    });

    // 计算付款金额的函数
    function calculatePaymentAmount() {
        let price = parseFloat($('input[name="price"]').val()) || 0;

        // 计算所有付款项的金额
        $('.payment-percent').each(function() {
            let percent = parseFloat($(this).val()) || 0;
            let amount = (price * percent / 100).toFixed(2);
            $(this).next('.payment-amount').text('金额: ' + amount);
        });

        // 检查总百分比是否超过100%
        let totalPercent = 0;
        $('.payment-percent').each(function() {
            totalPercent += parseFloat($(this).val()) || 0;
        });

        if (totalPercent > 100) {
            layer.msg('付款百分比总和不能超过100%', {icon: 2});
        }
    }

    // 监听项目价格和付款百分比字段的变化
    $('input[name="price"]').on('input', calculatePaymentAmount);
    $('.payment-percent').on('input', calculatePaymentAmount);

    form.on('submit(user-save)', function (data) {
        // 获取选中的部门ID
        let deptId = data.field.deptId;  // 确保 deptId 被正确获取
        let deptIds = [];
        $('input[name="dept_ids[]"]:checked').each(function() {
            deptIds.push($(this).val());
        });

        data.field.dept_id = deptId;  // 确保字段名与后端一致
        data.field.dept_ids = deptIds;  // 确保字段名与后端一致

        // 计算付款金额（将百分比转换为实际金额）
        let price = parseFloat(data.field.price) || 0;

        // 预付款
        let prepaymentPercent = parseFloat(data.field.prepayment) || 0;
        data.field.prepayment = (price * prepaymentPercent / 100).toFixed(2);

        // 发货款
        let deliveryPaymentPercent = parseFloat(data.field.delivery_payment) || 0;
        data.field.delivery_payment = (price * deliveryPaymentPercent / 100).toFixed(2);

        // 验收款
        let acceptancePaymentPercent = parseFloat(data.field.acceptance_payment) || 0;
        data.field.acceptance_payment = (price * acceptancePaymentPercent / 100).toFixed(2);

        // 质保金
        let warrantyPaymentPercent = parseFloat(data.field.warranty_payment) || 0;
        data.field.warranty_payment = (price * warrantyPaymentPercent / 100).toFixed(2);

        $.ajax({
            url: '/system/import_project/save',
            data: JSON.stringify(data.field),
            dataType: 'json',
            contentType: 'application/json',
            type: 'post',
            success: function (result) {
                if (result.success) {
                    layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                        parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
                        parent.layui.table.reload('import_project-table')
                    })
                } else {
                    layer.msg(result.msg, {icon: 2, time: 1000})
                }
            }
        });
        return false;
    })
  })
</script>

</body>
</html>