<!DOCTYPE html>
<html>
<head>
    <title>任务管理</title>
    {% include 'system/common/header.html' %}</head>
<body class="pear-container">

<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>

<script type="text/html" id="role-toolbar">
    {% if authorize("system:task:add") %}
        <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>
            新增
        </button>
    {% endif %}
</script>

<script type="text/html" id="role-bar">
    {% if authorize("system:task:edit") %}
        <button class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i
                class="layui-icon layui-icon-edit"></i>
        </button>
    {% endif %}
    {% if authorize("system:task:remove") %}
        <button class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i
                class="layui-icon layui-icon-delete"></i>
        </button>
    {% endif %}
</script>

<script type="text/html" id="task-status">
    <input type="checkbox" name="enable" value="{{ "{{d.id}}" }}" lay-skin="switch" lay-text="正在运行|已经暂停"
           lay-filter="role-enable" {{ "{{# if(d.next_run_time != null){ }} checked {{# } }}" }}>
</script>

<script type="text/html" id="task-createTime">
    {{ '  {{layui.util.toDateString(d.start_date,  "yyyy-MM-dd HH:mm:ss")}' |safe }}}
</script>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['table', 'form', 'jquery', 'popup', 'common'], function () {
    let table = layui.table
    let form = layui.form
    let $ = layui.jquery
    let popup = layui.popup

    let MODULE_PATH = '/system/task/'

    let cols = [
      [
        {% if authorize("system:role:remove") %}
          { type: 'checkbox' },
        {% endif %}
        { title: 'ID', field: 'id', align: 'center', width: 100 },
        { title: '函数', field: 'func', align: 'center' },
        { title: '名称', field: 'name', align: 'center' },
        { title: '开始时间', field: 'start_date', align: 'center',templet: '#task-createTime'},
        { title: '是否运行', field: 'next_run_time', align: 'center', templet: '#task-status' },
        { title: '类型', field: 'trigger', align: 'center' },
        { title: '操作', toolbar: '#role-bar', align: 'center', width: 195 }
      ]
    ]

    table.render({
      elem: '#role-table',
      url: MODULE_PATH + 'data',
      page: true,
      cols: cols,
      skin: 'line',
      toolbar: '#role-toolbar',
      defaultToolbar: [{
        layEvent: 'refresh',
        icon: 'layui-icon-refresh',
      }, 'filter', 'print', 'exports']
    })

    table.on('tool(role-table)', function (obj) {
      if (obj.event === 'remove') {
        window.remove(obj)
      } else if (obj.event === 'edit') {
        window.edit(obj)
      }
    })

    table.on('toolbar(role-table)', function (obj) {
      if (obj.event === 'add') {
        window.add()
      } else if (obj.event === 'refresh') {
        window.refresh()
      } else if (obj.event === 'batchRemove') {
        window.batchRemove(obj)
      }
    })

    form.on('submit(role-query)', function (data) {
      table.reload('role-table', { where: data.field })
      return false
    })

    form.on('switch(role-enable)', function (obj) {
      let operate
      if (obj.elem.checked) {
        operate = 'enable'
      } else {
        operate = 'disable'
      }
      let loading = layer.load()
      $.ajax({
        url: '/admin/task/' + operate,
        data: JSON.stringify({ id: this.value }),
        dataType: 'json',
        contentType: 'application/json',
        type: 'put',
        success: function (result) {
          layer.close(loading)
          if (result.success) {
            layer.msg(result.msg, { icon: 1, time: 1000 })
          } else {
            layer.msg(result.msg, { icon: 2, time: 1000 })
          }
        }
      })
    })

    window.add = function () {
      layer.open({
        type: 2,
        title: '新增',
        shade: 0.1,
        area: ['500px', '500px'],
        content: MODULE_PATH + 'add'
      })
    }



    window.edit = function (obj) {
      layer.open({
        type: 2,
        title: '修改',
        shade: 0.1,
        area: ['500px', '500px'],
        content: MODULE_PATH + 'edit/' + obj.data['id']
      })
    }

    window.remove = function (obj) {
      layer.confirm('确定要移除该任务', { icon: 3, title: '提示' }, function (index) {
        layer.close(index)
        let loading = layer.load()
        $.ajax({
          url: MODULE_PATH + 'remove/' + obj.data['id'],
          dataType: 'json',
          type: 'delete',
          success: function (result) {
            layer.close(loading)
            if (result.success) {
              layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
                obj.del()
              })
            } else {
              layer.msg(result.msg, { icon: 2, time: 1000 })
            }
          }
        })
      })
    }

    window.refresh = function () {
      table.reload('role-table')
    }
  })
</script>
</html>