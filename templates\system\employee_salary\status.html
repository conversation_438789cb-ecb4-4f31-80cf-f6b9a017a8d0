<!DOCTYPE html>
<html>
<head>
    <title>工资条确认</title>
    {% include 'system/common/header.html' %}
    <style>
        .salary-card {
            margin-bottom: 20px;
        }
        .salary-title {
            font-weight: bold;
            margin-bottom: 15px;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            margin-left: 10px;
        }
        .status-pending {
            background-color: #FFB800;
            color: #fff;
        }
        .status-confirmed {
            background-color: #5FB878;
            color: #fff;
        }
        .status-disputed {
            background-color: #FF5722;
            color: #fff;
        }
        .salary-info-item {
            margin-bottom: 10px;
        }
        .salary-info-label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
            text-align: right;
            margin-right: 10px;
        }
        .salary-info-value {
            display: inline-block;
        }
        .dispute-reason {
            display: none;
            margin-top: 15px;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-header">
            <div class="salary-title">
                {{ employee.name }} - {{ formatted_month }} 工资条确认
                {% if salary.view_status == 'pending' %}
                <span class="status-badge status-pending">未确认</span>
                {% elif salary.view_status == 'confirmed' %}
                <span class="status-badge status-confirmed">已确认</span>
                {% elif salary.view_status == 'disputed' %}
                <span class="status-badge status-disputed">有异议</span>
                {% endif %}
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md6">
                    <div class="salary-info-item">
                        <span class="salary-info-label">员工姓名：</span>
                        <span class="salary-info-value">{{ employee.name }}</span>
                    </div>
                    <div class="salary-info-item">
                        <span class="salary-info-label">工资月份：</span>
                        <span class="salary-info-value">{{ formatted_month }}</span>
                    </div>
                </div>
            </div>

            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-card salary-card">
                        <div class="layui-card-header">收入项目</div>
                        <div class="layui-card-body">
                            <div class="salary-info-item">
                                <span class="salary-info-label">基本工资：</span>
                                <span class="salary-info-value">{{ salary.base_salary }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">绩效工资：</span>
                                <span class="salary-info-value">{{ salary.performance_salary }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">主管考核项：</span>
                                <span class="salary-info-value">{{ salary.supervisor_assessment }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">考核系数：</span>
                                <span class="salary-info-value">{{ salary.assessment_coefficient }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">职务津贴：</span>
                                <span class="salary-info-value">{{ salary.position_allowance }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">全勤：</span>
                                <span class="salary-info-value">{{ salary.full_attendance }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">加班费：</span>
                                <span class="salary-info-value">{{ salary.overtime_pay }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">房补：</span>
                                <span class="salary-info-value">{{ salary.housing_subsidy }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">项目考核：</span>
                                <span class="salary-info-value">{{ salary.project_assessment }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">高温费：</span>
                                <span class="salary-info-value">{{ salary.high_temp_allowance }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">其他：</span>
                                <span class="salary-info-value">{{ salary.other_allowance }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-col-md6">
                    <div class="layui-card salary-card">
                        <div class="layui-card-header">扣款项目</div>
                        <div class="layui-card-body">
                            <div class="salary-info-item">
                                <span class="salary-info-label">请假扣款：</span>
                                <span class="salary-info-value">{{ salary.leave_deduction }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">其他扣款：</span>
                                <span class="salary-info-value">{{ salary.other_deduction }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">社保扣款：</span>
                                <span class="salary-info-value">{{ salary.insurance_deduction }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">个税：</span>
                                <span class="salary-info-value">{{ salary.tax_deduction }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">年终奖个税扣款：</span>
                                <span class="salary-info-value">{{ salary.year_end_tax }}</span>
                            </div>
                            <div class="salary-info-item">
                                <span class="salary-info-label">总扣款：</span>
                                <span class="salary-info-value">{{ salary.total_deduction }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-card salary-card">
                <div class="layui-card-header">工资合计</div>
                <div class="layui-card-body">
                    <div class="salary-info-item">
                        <span class="salary-info-label">应发工资：</span>
                        <span class="salary-info-value">{{ salary.should_pay }}</span>
                    </div>
                    <div class="salary-info-item">
                        <span class="salary-info-label">实发工资：</span>
                        <span class="salary-info-value">{{ salary.actual_salary }}</span>
                    </div>
                </div>
            </div>

            {% if salary.view_status == 'pending' %}
            <div class="layui-form">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <button type="button" class="layui-btn" id="confirm-btn">确认工资条</button>
                        <button type="button" class="layui-btn layui-btn-danger" id="dispute-btn">有异议</button>
                    </div>
                </div>

                <div class="layui-form-item dispute-reason" id="dispute-reason-div">
                    <label class="layui-form-label">异议原因：</label>
                    <div class="layui-input-block">
                        <textarea name="dispute_reason" placeholder="请输入异议原因" class="layui-textarea" id="dispute-reason-input"></textarea>
                    </div>
                    <div class="layui-input-block" style="margin-top: 10px;">
                        <button type="button" class="layui-btn layui-btn-danger" id="submit-dispute-btn">提交异议</button>
                        <button type="button" class="layui-btn layui-btn-primary" id="cancel-dispute-btn">取消</button>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}

<script>
layui.use(['form', 'jquery', 'popup'], function() {
    let form = layui.form;
    let $ = layui.jquery;
    let popup = layui.popup;

    // 确认工资条
    $('#confirm-btn').click(function() {
        updateStatus('confirmed');
    });

    // 显示异议输入框
    $('#dispute-btn').click(function() {
        $('#dispute-reason-div').show();
    });

    // 取消异议
    $('#cancel-dispute-btn').click(function() {
        $('#dispute-reason-div').hide();
        $('#dispute-reason-input').val('');
    });

    // 提交异议
    $('#submit-dispute-btn').click(function() {
        let reason = $('#dispute-reason-input').val();
        if (!reason) {
            popup.failure('请输入异议原因');
            return;
        }
        updateStatus('disputed', reason);
    });

    // 更新状态函数
    function updateStatus(status, reason) {
        let data = {
            id: {{ salary.id }},  // 模板变量会在服务器端渲染为具体的数值
            status: status
        };

        if (reason) {
            data.dispute_reason = reason;
        }

        $.ajax({
            url: '/employee_salary/update_status',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(res) {
                if (res.success) {
                    popup.success(res.msg);
                    // 刷新页面显示最新状态
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    popup.failure(res.msg);
                }
            },
            error: function() {
                popup.failure('操作失败，请稍后重试');
            }
        });
    }
});
</script>
</html>