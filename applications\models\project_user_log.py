import datetime
from applications.extensions import db
from flask_login import current_user

class ProjectUserLog(db.Model):
    """项目负责人变更日志表"""
    __tablename__ = 'project_user_log'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    project_id = db.Column(db.<PERSON>ger, db.<PERSON>ey('import_project.id'), nullable=False, comment='项目ID')
    old_manager = db.<PERSON>umn(db.String(50), comment='原负责人')
    new_manager = db.Column(db.String(50), comment='新负责人')
    operator_id = db.Column(db.Integer, comment='操作人ID')
    operator_name = db.Column(db.String(50), comment='操作人姓名')
    operation_time = db.Column(db.DateTime, default=datetime.datetime.now, comment='操作时间')
    remark = db.Column(db.String(255), comment='备注')

    # 关联项目表
    project = db.relationship('Import_project', backref='user_logs')

    def __init__(self, **kwargs):
        super(ProjectUserLog, self).__init__(**kwargs)
