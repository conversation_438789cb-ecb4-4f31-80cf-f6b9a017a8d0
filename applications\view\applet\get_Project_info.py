from flask import Blueprint, jsonify, session, redirect
from applications.extensions import db
from applications.models import Import_project
from applications.models import ProjectManageDept  # 新增导入



bp = Blueprint('get_Project_info', __name__)

# 新增接口，用于给前端传递项目信息   
@bp.route('/get_projects', methods=['GET'])
def get_projects():
    try:
        # 获取所有部门，并过滤掉 "所有项目类型"
        depts = ProjectManageDept.query.filter(ProjectManageDept.dept_name != "所有项目类型").all()
        project_dict = {}
        
        for dept in depts:
            # 根据 dept_id 查询对应的项目
            projects = Import_project.query.filter_by(dept_id=dept.id).all()
            project_codes = [project.project_code for project in projects]
            
            if dept.dept_name not in project_dict:
                project_dict[dept.dept_name] = {
                    "id": dept.id,
                    "name": dept.dept_name,
                    "numbers": project_codes  # 直接使用查询到的项目编码列表
                }
                
        project_list = list(project_dict.values())
        return jsonify({'data': project_list, 'status': 200})
    except Exception as e:
        return jsonify({'message': f'获取项目信息失败: {str(e)}', 'status': 500})
        
@bp.route('/test')
def test():
    return 'Test successful'