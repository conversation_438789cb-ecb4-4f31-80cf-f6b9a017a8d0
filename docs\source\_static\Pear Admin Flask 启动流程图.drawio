<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="26.0.6">
  <diagram name="第 1 页" id="MiUs9oOc82b6Rctnqw0G">
    <mxGraphModel dx="1838" dy="612" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="oXrC6FassujB1rqgt1jG-76" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-1" target="oXrC6FassujB1rqgt1jG-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-1" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;app = create_app()&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;app.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="-20" y="220" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-2" target="oXrC6FassujB1rqgt1jG-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-2" value="&lt;div&gt;初始化 Flask()&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="-20" y="130" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-3" target="oXrC6FassujB1rqgt1jG-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-23" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" parent="1" source="oXrC6FassujB1rqgt1jG-3" target="oXrC6FassujB1rqgt1jG-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-3" value="&lt;div&gt;载入配置&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="150" y="130" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-4" target="oXrC6FassujB1rqgt1jG-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-26" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" parent="1" source="oXrC6FassujB1rqgt1jG-4" target="oXrC6FassujB1rqgt1jG-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-4" value="&lt;div&gt;注册组件&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="330" y="130" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-5" target="oXrC6FassujB1rqgt1jG-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-50" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" parent="1" source="oXrC6FassujB1rqgt1jG-5" target="oXrC6FassujB1rqgt1jG-49" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-5" value="&lt;div&gt;注册应用蓝图&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="516" y="130" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-67" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" parent="1" source="oXrC6FassujB1rqgt1jG-6" target="oXrC6FassujB1rqgt1jG-66" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-70" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="oXrC6FassujB1rqgt1jG-6" target="oXrC6FassujB1rqgt1jG-71" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="910" y="351.33331298828125" as="targetPoint" />
            <Array as="points">
              <mxPoint x="860" y="150" />
              <mxPoint x="860" y="330" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-6" value="&lt;div&gt;注册命令&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="700" y="130" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-22" value="&lt;div&gt;读取 BaseConfig&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;配置来自&amp;nbsp;applications/config.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="140" y="190" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-28" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-25" target="oXrC6FassujB1rqgt1jG-27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-25" value="&lt;div&gt;初始化插件&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/extensions/init_plugins.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="310" y="190" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-30" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-27" target="oXrC6FassujB1rqgt1jG-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-27" value="&lt;div&gt;广播插件&amp;nbsp;event_begin 事件&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/extensions/init_plugins.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="310" y="250" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-29" target="oXrC6FassujB1rqgt1jG-31" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-29" value="&lt;div&gt;初始化 Flask LoginManager&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/extensions/init_login.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="310" y="310" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-42" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-31" target="oXrC6FassujB1rqgt1jG-37" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-31" value="&lt;div&gt;初始化 SQLAlchemy 数据库&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(77, 77, 77); font-size: 8px;&quot;&gt;applications/extensions/init_sqlalchemy.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="310" y="370" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-44" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-37" target="oXrC6FassujB1rqgt1jG-43" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-37" value="&lt;div&gt;初始化 Mail 邮件组件&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(77, 77, 77); font-size: 8px;&quot;&gt;applications/extensions/init_error_views.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="310" y="430" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-39" value="&lt;div&gt;初始化 服务器错误蓝图&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(77, 77, 77); font-size: 8px;&quot;&gt;applications/extensions/init_error_views.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="310" y="670" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-48" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-41" target="oXrC6FassujB1rqgt1jG-39" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-41" value="&lt;div&gt;初始化 网页模板公用函数&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(77, 77, 77); font-size: 7px;&quot;&gt;applications/extensions/init_template_directives.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="310" y="610" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-43" target="oXrC6FassujB1rqgt1jG-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1tN3H2LKsTGCSpzGhpWD-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" edge="1" parent="1" source="oXrC6FassujB1rqgt1jG-43" target="1tN3H2LKsTGCSpzGhpWD-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-43" value="&lt;div&gt;初始化&lt;font style=&quot;font-size: 9px;&quot;&gt; Migrate &lt;/font&gt;数据库迁移组件&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(77, 77, 77); font-size: 9px;&quot;&gt;applications/extensions/init_migrate.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="310" y="490" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-47" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-45" target="oXrC6FassujB1rqgt1jG-41" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-45" value="&lt;div&gt;初始化&lt;font size=&quot;1&quot;&gt;&amp;nbsp;Session 会话&lt;/font&gt;组件&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(77, 77, 77); font-size: 9px;&quot;&gt;applications/extensions/init_session.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="310" y="550" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-54" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" parent="1" source="oXrC6FassujB1rqgt1jG-49" target="oXrC6FassujB1rqgt1jG-52" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-49" value="&lt;div&gt;注册应用子页面路由&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/view/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="496" y="190" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-63" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" parent="1" source="oXrC6FassujB1rqgt1jG-51" target="oXrC6FassujB1rqgt1jG-62" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-51" value="&lt;div&gt;广播插件&amp;nbsp;event_init 事件&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(77, 77, 77); font-size: 9px;&quot;&gt;applications/view/__init__.py&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="496" y="610" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-56" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-52" target="oXrC6FassujB1rqgt1jG-55" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-52" value="&lt;div&gt;注册 用户管理 蓝图&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/view/system/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="496" y="250" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-58" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-55" target="oXrC6FassujB1rqgt1jG-57" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-55" value="&lt;div&gt;注册 文件上传 蓝图&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/view/system/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="496" y="310" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-60" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;endArrow=none;endFill=0;dashPattern=1 4;strokeWidth=4;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="575.66" y="428" as="sourcePoint" />
            <mxPoint x="575.66" y="478" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-57" value="&lt;div&gt;注册 系统监控 蓝图&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/view/system/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="496" y="370" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-61" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-59" target="oXrC6FassujB1rqgt1jG-51" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-59" value="&lt;div&gt;注册 后台首页 蓝图&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/view/system/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="496" y="550" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-62" value="&lt;div&gt;注册 启用插件所提供的 蓝图&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;plugins/*/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="496" y="670" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-65" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-64" target="oXrC6FassujB1rqgt1jG-59" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-64" value="&lt;div&gt;注册 部门管理 蓝图&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/view/system/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="496" y="490" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-69" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" parent="1" source="oXrC6FassujB1rqgt1jG-66" target="oXrC6FassujB1rqgt1jG-68" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-66" value="&lt;div&gt;注册 应用 cli 命令&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;font-size: 9px; color: rgb(77, 77, 77);&quot;&gt;applications/view/__init__.py&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="680" y="190" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-68" value="&lt;div&gt;广播插件&amp;nbsp;event_finish 事件&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;color: rgb(77, 77, 77); font-size: 9px;&quot;&gt;applications/view/__init__.py&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="680" y="250" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-81" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;" parent="1" source="oXrC6FassujB1rqgt1jG-71" target="oXrC6FassujB1rqgt1jG-80" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-71" value="&lt;div&gt;初始化完成&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="700" y="310" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-75" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="oXrC6FassujB1rqgt1jG-72" target="oXrC6FassujB1rqgt1jG-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-72" value="&lt;div&gt;初始化开始&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="-20" y="310" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-78" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=none;dashed=1;strokeColor=#007FFF;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="490" y="245" width="172" height="355" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-79" value="&lt;font style=&quot;color: rgb(0, 127, 255);&quot;&gt;注册项目的视图函数，&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(0, 127, 255);&quot;&gt;比如用户管理、文件上传等&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(0, 127, 255);&quot;&gt;功能都是在这里初始化的&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="720" y="550" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-80" value="&lt;div&gt;广播插件&amp;nbsp;event_context 事件&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="680" y="370" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-82" value="&lt;font style=&quot;color: rgb(153, 153, 153);&quot;&gt;等同于 with app.app_context()&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="800" y="430" width="170" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-84" value="&lt;font style=&quot;color: rgb(255, 128, 0);&quot;&gt;flask admin init 数据库&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(255, 128, 0);&quot;&gt;初始化的&lt;span style=&quot;background-color: transparent;&quot;&gt;命令是在这里注册的&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;strokeColor=none;strokeWidth=1;" parent="1" vertex="1">
          <mxGeometry x="880" y="230" width="170" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-86" value="" style="endArrow=none;html=1;rounded=0;dashed=1;endFill=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;strokeColor=#FF8000;" parent="1" target="oXrC6FassujB1rqgt1jG-84" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="840" y="210" as="sourcePoint" />
            <mxPoint x="880" y="240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-88" value="" style="endArrow=none;html=1;rounded=0;dashed=1;endFill=0;strokeColor=#999999;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="840" y="412.5" as="sourcePoint" />
            <mxPoint x="873" y="432.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-89" value="" style="endArrow=none;html=1;rounded=0;dashed=1;endFill=0;strokeColor=#007FFF;exitX=1;exitY=0.75;exitDx=0;exitDy=0;" parent="1" source="oXrC6FassujB1rqgt1jG-78" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="670" y="520" as="sourcePoint" />
            <mxPoint x="720" y="550" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-90" value="&lt;font style=&quot;color: rgb(0, 153, 0);&quot;&gt;开发插件的蓝图在这里注册&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="700" y="650" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-92" value="" style="endArrow=none;html=1;rounded=0;dashed=1;endFill=0;strokeColor=#009900;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="662" y="630" as="sourcePoint" />
            <mxPoint x="720" y="649" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-93" value="&lt;font style=&quot;color: rgb(150, 150, 150);&quot;&gt;默认读取的是&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(150, 150, 150);&quot;&gt;&amp;nbsp;BaseConfig 类中的配置。&lt;/font&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(150, 150, 150), rgb(108, 108, 108));&quot;&gt;修改其他类请先在文件&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(150, 150, 150);&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;applications/__init__.py&amp;nbsp;&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(150, 150, 150);&quot;&gt;&lt;span style=&quot;background-color: transparent;&quot;&gt;中进行修改&lt;/span&gt;&lt;/font&gt;&lt;/div&gt;&lt;/div&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="125" y="260" width="145" height="90" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-95" value="" style="endArrow=none;html=1;rounded=0;dashed=1;endFill=0;strokeColor=#999999;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="oXrC6FassujB1rqgt1jG-22" target="oXrC6FassujB1rqgt1jG-93" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="240" as="sourcePoint" />
            <mxPoint x="233" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-96" value="&lt;font style=&quot;color: rgb(204, 0, 0);&quot;&gt;404 500 等请求、服务器错误的视图会在这里注册&lt;/font&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="130" y="620" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="oXrC6FassujB1rqgt1jG-97" value="" style="endArrow=none;html=1;rounded=0;dashed=1;endFill=0;strokeColor=#CC0000;exitX=0.709;exitY=0.962;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitPerimeter=0;" parent="1" source="oXrC6FassujB1rqgt1jG-96" target="oXrC6FassujB1rqgt1jG-39" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="240" y="670" as="sourcePoint" />
            <mxPoint x="298" y="709" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="1tN3H2LKsTGCSpzGhpWD-3" value="导入所有数据库模型&lt;div&gt;&lt;span style=&quot;color: rgb(77, 77, 77); font-size: 9px;&quot;&gt;applications/models/__init__.py&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;" vertex="1" parent="1">
          <mxGeometry x="130" y="490" width="160" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
