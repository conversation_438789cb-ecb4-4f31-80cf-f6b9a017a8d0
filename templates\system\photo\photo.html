<!DOCTYPE html>
<html>
<head>
    <title>图片上传</title>
    {% include 'system/common/header.html' %}
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <table id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<script type="text/html" id="toolbar">
    {% if authorize("system:file:add") %}
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>
            新增
        </button>
    {% endif %}
    {% if authorize("system:file:delete") %}
        <button class="layui-btn layui-btn-sm" lay-event="batchRemove">
            <i class="layui-icon layui-icon-delete"></i>
            删除
        </button>
    {% endif %}
</script>

<script type="text/html" id="user-bar">
    {% if authorize("system:file:delete") %}
        <button class="layui-btn layui-btn-danger layui-btn-sm" lay-event="remove"><i
                class="layui-icon layui-icon-delete"></i></button>
    {% endif %}
</script>
<script type="text/html" id="file-uploadTime">
    {{ '  {{layui.util.toDateString(d.create_time,  "yyyy-MM-dd HH:mm:ss")}' |safe }}}
</script>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['table', 'form', 'jquery'], function () {
    let table = layui.table
    let form = layui.form
    let $ = layui.jquery
    let cols = [
      [
        {
          type: 'checkbox'
        },
        {
          field: 'id',
          title: 'ID',
          sort: true,
          align: 'center',
          unresize: true,
          width: 80
        },
        {
          field: 'name',
          title: '文件名称',
          unresize: true,
          align: 'center'
        },
        {
          field: 'href',
          title: '图片',
          unresize: true,
          align: 'center',
          templet: function (d) {
            return '<img class="photo" style="max-width: 100%;\n' +
              '  height: 100%;\n' +
              '  cursor: pointer;" lay-event="photo" src=" ' + d.href + '"></i>'
          }

        },
        {
          field: 'mime',
          title: 'mime类型',
          unresize: true,
          align: 'center'
        },
        {
          field: 'size',
          title: '文件大小',
          unresize: true,
          align: 'center'
        },
        {
          field: 'create_time',
          title: '创建时间',
          templet: '#file-uploadTime',
          unresize: true,
          align: 'center'
        },
        {
          title: '操作',
          toolbar: '#user-bar',
          align: 'center',
          unresize: true,
          width: 200
        }
      ]
    ]

    table.render({
      elem: '#dataTable',
      url: 'table',
      page: true,
      cols: cols,
      skin: 'line',
      toolbar: '#toolbar',
      defaultToolbar: [{
        layEvent: 'refresh',
        icon: 'layui-icon-refresh',
      }, 'filter', 'print', 'exports']
    })

    table.on('tool(dataTable)', function (obj) {
      if (obj.event === 'remove') {
        window.remove(obj)
      } else if (obj.event === 'photo') {
        window.photo(obj)
      }
    })

    table.on('toolbar(dataTable)', function (obj) {
      if (obj.event === 'add') {
        window.add()
      } else if (obj.event === 'refresh') {
        window.refresh()
      } else if (obj.event === 'batchRemove') {
        window.batchRemove(obj)
      }
    })

    //弹出窗设置 自己设置弹出百分比
    function screen () {
      if (typeof width !== 'number' || width === 0) {
        width = $(window).width() * 0.8
      }
      if (typeof height !== 'number' || height === 0) {
        height = $(window).height() - 20
      }
      return [width + 'px', height + 'px']
    }

    window.add = function () {
      layer.open({
        type: 2,
        maxmin: true,
        title: '新增图片',
        shade: 0.1,
        area: screen(),
        content: 'upload'
      })
    }

    window.remove = function (obj) {
      layer.confirm('确定要删除该图片', {
        icon: 3,
        title: '提示'
      }, function (index) {
        layer.close(index)
        let loading = layer.load()
        $.ajax({
          url: 'delete',
          data: { id: obj.data['id'] },
          dataType: 'json',
          type: 'POST',
          success: function (res) {
            layer.close(loading)
            if (res.success) {
              layer.msg(res.msg, {
                icon: 1,
                time: 1000
              }, function () {
                obj.del()
              })
            } else {
              layer.msg(res.msg, {
                icon: 2,
                time: 1000
              })
            }
          }
        })
      })
    }

    window.batchRemove = function (obj) {
      let data = table.checkStatus(obj.config.id).data
      if (data.length === 0) {
        layer.msg('未选中数据', {
          icon: 3,
          time: 1000
        })
        return false
      }
      var ids = []
      var hasCheck = table.checkStatus('dataTable')
      var hasCheckData = hasCheck.data
      if (hasCheckData.length > 0) {
        $.each(hasCheckData, function (index, element) {
          ids.push(element.id)
        })
      }
      layer.confirm('确定要删除这些图片', {
        icon: 3,
        title: '提示'
      }, function (index) {
        layer.close(index)
        let loading = layer.load()
        $.ajax({
          url: "{{ url_for('system.adminFile.batch_remove') }}",
          data: { ids: ids },
          dataType: 'json',
          type: 'POST',
          success: function (res) {
            layer.close(loading)
            if (res.success) {
              layer.msg(res.msg, {
                icon: 1,
                time: 1000
              }, function () {
                table.reload('dataTable')
              })
            } else {
              layer.msg(res.msg, {
                icon: 2,
                time: 1000
              })
            }
          }
        })
      })
    }

    window.refresh = function () {
      table.reload('dataTable')
    }
    // 查看大图
    window.photo = function (obj) {
      if (!obj.data.href || obj.data.href === '') {
        layer.msg('图片地址错误！')
        return
      }
      var auto_img = {}
      var img = new Image()
      img.src = obj.data.href
      img.onload = function () {
        var max_height = $(window).height() - 100
        var max_width = $(window).width()
        var rate1 = max_height / img.height
        var rate2 = max_width / img.width
        var rate3 = 1
        var rate = Math.min(rate1, rate2, rate3)
        auto_img.height = img.height * rate
        auto_img.width = img.width * rate
        layer.open({
          type: 1,
          title: false,
          area: ['auto'],
          skin: 'layui-layer-nobg', //没有背景色
          shadeClose: true,
          content: '<img src=\'' + obj.data['href'] + '\' width=\'' + auto_img.width + 'px\' height=\'' + auto_img.height + 'px\'>'
        })
      }
    }

  })
</script>