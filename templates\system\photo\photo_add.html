<!DOCTYPE html>
<html>
<head>
    {% include 'system/common/header.html' %}
    <style>
        .pear-container {
            background-color: white;
        }

        body {
            margin: 10px;
        }
    </style>
</head>
<body>
<div class="layui-row layui-col-space15">
    <div class="layui-col-md12">
        <div class="layui-card">
            <div class="layui-tab-content">
                <fieldset class="layui-elem-field layui-field-title">
                    <legend>新增图片</legend>
                </fieldset>
                <form class="layui-form edit-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">
                            新增图片
                        </label>
                        <button type="button" class="layui-btn layui-btn-normal" id="logo-img">选择文件</button>
                        <button type="button" class="layui-btn" id="logo-upload-button">开始上传</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% include 'system/common/footer.html' %}
<script>
    layui.use(['jquery', 'element', 'form', 'upload'], function () {
        var $ = layui.jquery;
        var element = layui.element;
        var form = layui.form;
        var upload = layui.upload;
        //选完文件后不自动上传
        upload.render({
            elem: '#logo-img'
            , url: "{{ url_for('system.adminFile.upload_api') }}"
            , auto: false
            , exts: 'jpg|png|gif|bmp|jpeg'
            , size: 1000
            , bindAction: '#logo-upload-button'
            , done: function (res) {
                if (res.success) {
                    layer.msg(res.msg, {icon: 1, time: 500}, function () {
                        parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                        window.parent.location.reload();
                    });
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
    });
</script>
</body>
</html>