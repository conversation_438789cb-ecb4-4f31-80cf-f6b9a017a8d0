from flask import Flask, Blueprint

from applications.view.system.dict import bp as dict_bp
from applications.view.system.file import bp as file_bp
from applications.view.system.index import bp as index_bp
from applications.view.system.log import bp as log_bp
from applications.view.system.mail import bp as mail_bp
from applications.view.system.monitor import bp as monitor_bp
from applications.view.system.passport import bp as passport_bp
from applications.view.system.power import bp as power_bp
from applications.view.system.rights import bp as right_bp
from applications.view.system.role import bp as role_bp
from applications.view.system.user import bp as user_bp
from applications.view.system.dept import bp as dept_bp
from applications.view.system.yg_info import bp as yg_info_bp
from applications.view.system.import_project import bp as import_project_bp
from applications.view.system.log_info import bp as log_info_bp
from applications.view.system.employee_salary import bp as employee_salary_bp
from applications.view.system.project_management import bp as project_management_bp
from applications.view.system.Statistical_data import bp as statistical_data_bp
from applications.view.system.Project_cost import bp as project_cost_bp
from applications.view.system.meeting_statistics import bp as meeting_statistics_bp
from applications.view.system.project_progress import bp as project_progress_bp
from applications.view.system.project_payment import bp as project_payment_bp
from applications.view.system.dingding import bp as dingding_bp
# 创建sys
system_bp = Blueprint('system', __name__, url_prefix='/system')


def register_system_bps(app: Flask):
    # 在admin_bp下注册子蓝图
    system_bp.register_blueprint(user_bp)
    system_bp.register_blueprint(file_bp)
    system_bp.register_blueprint(monitor_bp)
    system_bp.register_blueprint(log_bp)
    system_bp.register_blueprint(power_bp)
    system_bp.register_blueprint(role_bp)
    system_bp.register_blueprint(dict_bp)
    system_bp.register_blueprint(mail_bp)
    system_bp.register_blueprint(passport_bp)
    system_bp.register_blueprint(right_bp)
    system_bp.register_blueprint(dept_bp)
    system_bp.register_blueprint(yg_info_bp)
    system_bp.register_blueprint(import_project_bp)
    system_bp.register_blueprint(log_info_bp)
    system_bp.register_blueprint(employee_salary_bp)
    system_bp.register_blueprint(project_management_bp)
    system_bp.register_blueprint(statistical_data_bp)
    system_bp.register_blueprint(project_cost_bp)
    system_bp.register_blueprint(meeting_statistics_bp)
    system_bp.register_blueprint(project_progress_bp)
    system_bp.register_blueprint(project_payment_bp)
    system_bp.register_blueprint(dingding_bp)

    app.register_blueprint(index_bp)
    app.register_blueprint(system_bp)
