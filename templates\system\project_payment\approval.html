<!DOCTYPE html>
<html>
<head>
    <title>项目回款审批</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
    <script src="{{ url_for('static', filename='system/component/layui/layui.js') }}"></script>
    <style>
        /* 全局样式 */
        body {
            background-color: #f5f7fa;
        }
        .layui-card {
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        .layui-card:hover {
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #ebeef5;
            padding: 15px 20px;
            background-color: #fff;
            border-radius: 8px 8px 0 0;
        }
        .layui-card-body {
            padding: 20px;
            background-color: #fff;
            border-radius: 8px 8px 8px 8px;
        }

        /* 表格样式 */
        .layui-table-cell {
            height: auto;
            line-height: 28px;
            padding: 8px 15px;
            position: relative;
            box-sizing: border-box;
        }
        .layui-table-view .layui-table {
            width: 100%;
        }
        .layui-table-header {
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }
        .layui-table-view {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        .layui-table-cell .layui-btn+.layui-btn {
            margin-left: 5px;
        }

        /* 状态标签样式 */
        .status-tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            color: #fff;
            text-align: center;
            min-width: 80px;
        }
        .status-pending {
            background-color: #E6A23C;
        }
        .status-approved {
            background-color: #67C23A;
        }
        .status-rejected {
            background-color: #F56C6C;
        }

        /* 按钮样式 */
        .layui-btn {
            border-radius: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .layui-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .layui-btn-primary {
            border-color: #dcdfe6;
            color: #606266;
        }
        .layui-btn-primary:hover {
            border-color: #c6e2ff;
            color: #409eff;
            background-color: #ecf5ff;
        }
        .layui-btn-success {
            background-color: #67C23A;
        }
        .layui-btn-danger {
            background-color: #F56C6C;
        }

        /* 统计卡片样式 */
        .stat-card {
            text-align: center;
            padding: 20px 10px;
            position: relative;
            overflow: hidden;
            height: 180px;
            min-height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }

        .stat-card-prepayment {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .stat-card-delivery {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .stat-card-acceptance {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .stat-card-warranty {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-icon {
            margin-bottom: 15px;
        }

        .stat-icon i {
            font-size: 36px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 70px;
            height: 70px;
            line-height: 70px;
            display: inline-block;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin: 12px 0 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
            font-size: 15px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            margin-bottom: 5px;
        }
    </style>
</head>
<body class="pear-container">

    <!-- 页面标题 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md6">
                    <h2 style="margin: 5px 0; font-size: 22px; color: #333; font-weight: 600;">
                        <i class="layui-icon layui-icon-form" style="margin-right: 8px; font-size: 22px; color: #409EFF;"></i>项目回款审批
                    </h2>
                </div>
                <div class="layui-col-md6" style="text-align: right;">
                    <button class="layui-btn layui-btn-primary" id="refresh-btn">
                        <i class="layui-icon layui-icon-refresh"></i> 刷新列表
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="layui-row layui-col-space20" style="margin-bottom: 20px;">
        <!-- 预付款待审批 -->
        <div class="layui-col-md3">
            <div class="layui-card" id="prepayment-card">
                <div class="layui-card-body stat-card stat-card-prepayment">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-rmb"></i>
                    </div>
                    <div class="stat-title">预付款未付项目</div>
                    <div class="stat-number" id="prepayment-count">0</div>
                </div>
            </div>
        </div>

        <!-- 发货款待审批 -->
        <div class="layui-col-md3">
            <div class="layui-card" id="delivery-payment-card">
                <div class="layui-card-body stat-card stat-card-delivery">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-cart"></i>
                    </div>
                    <div class="stat-title">发货款未付项目</div>
                    <div class="stat-number" id="delivery-payment-count">0</div>
                </div>
            </div>
        </div>

        <!-- 验收款待审批 -->
        <div class="layui-col-md3">
            <div class="layui-card" id="acceptance-payment-card">
                <div class="layui-card-body stat-card stat-card-acceptance">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-ok"></i>
                    </div>
                    <div class="stat-title">验收款未付项目</div>
                    <div class="stat-number" id="acceptance-payment-count">0</div>
                </div>
            </div>
        </div>

        <!-- 质保金待审批 -->
        <div class="layui-col-md3">
            <div class="layui-card" id="warranty-payment-card">
                <div class="layui-card-body stat-card stat-card-warranty">
                    <div class="stat-icon">
                        <i class="layui-icon layui-icon-auz"></i>
                    </div>
                    <div class="stat-title">质保金未付项目</div>
                    <div class="stat-number" id="warranty-payment-count">0</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审批列表 -->
    <div class="layui-card">
        <div class="layui-card-header">
            <i class="layui-icon layui-icon-list" style="margin-right: 8px; color: #409EFF;"></i>待审批回款列表
        </div>
        <div class="layui-card-body">
            <table id="approval-table" lay-filter="approval-table"></table>
        </div>
    </div>

    <!-- 表格工具栏模板 -->
    <script type="text/html" id="approval-toolbar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm layui-btn-success" lay-event="approve">
                <i class="layui-icon layui-icon-ok"></i> 批准
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="reject">
                <i class="layui-icon layui-icon-close"></i> 拒绝
            </button>
        </div>
    </script>

    <!-- 表格状态模板 -->
    <script type="text/html" id="status-template">
        {% raw %}
        {{#  if(d.status === 0){ }}
            <span class="status-tag status-pending">待审批</span>
        {{#  } else if(d.status === 1){ }}
            <span class="status-tag status-approved">已批准</span>
        {{#  } else if(d.status === 2){ }}
            <span class="status-tag status-rejected">已拒绝</span>
        {{#  } }}
        {% endraw %}
    </script>

    <!-- 表格金额模板 -->
    <script type="text/html" id="amount-template">
        {% raw %}
        <span>{{ d.currency_symbol }} {{ d.actual_amount }}</span>
        {% endraw %}
    </script>

    <!-- 表格累计回款模板 -->
    <script type="text/html" id="accumulate-template">
        {% raw %}
        {{#  if(d.is_accumulate){ }}
            <span style="color: #409EFF;">累计回款</span>
        {{#  } else { }}
            <span style="color: #F56C6C;">覆盖回款</span>
        {{#  } }}
        {% endraw %}
    </script>

</body>

<script>
    layui.use(['table', 'layer', 'jquery', 'form'], function() {
        let table = layui.table;
        let layer = layui.layer;
        let $ = layui.jquery;
        let form = layui.form;

        // 渲染表格
        table.render({
            elem: '#approval-table',
            url: '/system/project_payment/api/approval/pending',
            page: true,
            limit: 10,
            limits: [10, 20, 50],
            cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field: 'id', title: 'ID', width: 80, align: 'center'},
                {field: 'project_name', title: '项目名称', align: 'center', minWidth: 200},
                {field: 'project_code', title: '项目编号', align: 'center', width: 150},
                {field: 'payment_type_name', title: '付款类型', align: 'center', width: 120},
                {field: 'actual_amount', title: '回款金额', align: 'center', width: 150, templet: '#amount-template'},
                {field: 'is_accumulate', title: '回款方式', align: 'center', width: 120, templet: '#accumulate-template'},
                {field: 'payment_date', title: '付款日期', align: 'center', width: 120},
                {field: 'created_at', title: '申请时间', align: 'center', width: 180},
                {field: 'status', title: '状态', align: 'center', width: 120, templet: '#status-template'},
                {field: 'remark', title: '备注', align: 'center'},
                {title: '操作', toolbar: '#approval-toolbar', width: 180, align: 'center', fixed: 'right'}
            ]],
            text: {
                none: '暂无待审批的回款请求'
            },
            done: function() {
                // 表格加载完成后的回调
            }
        });

        // 表格工具栏事件
        table.on('tool(approval-table)', function(obj) {
            let data = obj.data;
            let event = obj.event;

            if (event === 'approve') {
                // 批准回款请求
                processApproval(data.id, 'approve');
            } else if (event === 'reject') {
                // 拒绝回款请求
                processApproval(data.id, 'reject');
            }
        });

        // 处理审批请求
        function processApproval(approvalId, action) {
            let title = action === 'approve' ? '批准回款请求' : '拒绝回款请求';
            let btnText = action === 'approve' ? '批准' : '拒绝';
            let btnClass = action === 'approve' ? 'layui-btn-success' : 'layui-btn-danger';
            let iconClass = action === 'approve' ? 'layui-icon-ok' : 'layui-icon-close';

            // 构建弹窗内容
            let content =
                '<div class="layui-form" style="padding: 25px;">' +
                    '<input type="hidden" name="action" value="' + action + '">' +
                    '<div class="layui-form-item">' +
                        '<label class="layui-form-label" style="width: 100px; font-weight: 600;"><i class="layui-icon layui-icon-about" style="margin-right: 5px;"></i>备注</label>' +
                        '<div class="layui-input-block" style="margin-left: 100px;">' +
                            '<textarea name="remark" placeholder="请输入备注" class="layui-textarea" style="min-height: 120px; border-radius: 4px;"></textarea>' +
                        '</div>' +
                    '</div>' +
                    '<div class="layui-form-item" style="margin-top: 30px; text-align: center;">' +
                        '<button class="layui-btn ' + btnClass + '" lay-submit lay-filter="processApproval" style="width: 120px;">' +
                            '<i class="layui-icon ' + iconClass + '"></i> ' + btnText +
                        '</button>' +
                        '<button type="button" class="layui-btn layui-btn-primary" style="width: 120px; margin-left: 20px;" onclick="layer.closeAll();">' +
                            '<i class="layui-icon layui-icon-close"></i> 取消' +
                        '</button>' +
                    '</div>' +
                '</div>';

            layer.open({
                type: 1,
                title: '<i class="layui-icon ' + iconClass + '" style="margin-right: 8px; font-size: 16px;"></i>' + title,
                area: ['500px', '300px'],
                skin: 'layui-layer-molv',
                shade: 0.3,
                anim: 1,
                content: content,
                success: function() {
                    // 渲染表单
                    form.render();

                    // 监听表单提交
                    form.on('submit(processApproval)', function(data) {
                        let loadingIndex = layer.load(2, {shade: [0.1, '#fff']});

                        // 提交审批处理
                        $.ajax({
                            url: '/system/project_payment/api/approval/' + approvalId + '/process',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                action: action,
                                remark: data.field.remark
                            }),
                            success: function(response) {
                                layer.close(loadingIndex);

                                if (response.success) {
                                    layer.msg(response.msg, {icon: 1, time: 2000}, function() {
                                        // 关闭弹窗
                                        layer.closeAll();
                                        // 刷新表格
                                        table.reload('approval-table');
                                        // 刷新统计卡片数据
                                        loadStatistics();
                                    });
                                } else {
                                    layer.msg(response.msg, {icon: 2, time: 3000});
                                }
                            },
                            error: function() {
                                layer.close(loadingIndex);
                                layer.msg('处理审批请求失败，请稍后重试', {icon: 2, time: 3000});
                            }
                        });

                        return false;
                    });
                }
            });
        }

        // 获取统计数据
        function loadStatistics() {
            $.ajax({
                url: '/system/project_payment/api/approval/statistics',
                type: 'GET',
                success: function(res) {
                    if (res.code === 0) {
                        // 更新统计数据
                        $('#prepayment-count').text(res.data.prepayment_count);
                        $('#delivery-payment-count').text(res.data.delivery_payment_count);
                        $('#acceptance-payment-count').text(res.data.acceptance_payment_count);
                        $('#warranty-payment-count').text(res.data.warranty_payment_count);
                    } else {
                        layer.msg('获取统计数据失败: ' + res.msg, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('获取统计数据失败，请稍后重试', {icon: 2});
                }
            });
        }

        // 页面加载完成后获取统计数据
        loadStatistics();

        // 刷新按钮点击事件，同时刷新表格和统计数据
        $('#refresh-btn').on('click', function() {
            table.reload('approval-table');
            loadStatistics();
        });

        // 卡片点击事件，筛选对应类型的待审批项目
        $('#prepayment-card').on('click', function() {
            table.reload('approval-table', {
                where: {
                    payment_type: 'prepayment'
                }
            });
        });

        $('#delivery-payment-card').on('click', function() {
            table.reload('approval-table', {
                where: {
                    payment_type: 'delivery_payment'
                }
            });
        });

        $('#acceptance-payment-card').on('click', function() {
            table.reload('approval-table', {
                where: {
                    payment_type: 'acceptance_payment'
                }
            });
        });

        $('#warranty-payment-card').on('click', function() {
            table.reload('approval-table', {
                where: {
                    payment_type: 'warranty_payment'
                }
            });
        });
    });
</script>
</html>