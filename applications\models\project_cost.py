import datetime
from applications.extensions import db


class ProjectActualCost(db.Model):
    """项目实际成本表"""
    __tablename__ = 'project_actual_cost'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    project_id = db.Column(db.Integer, db.<PERSON>ey('import_project.id'), comment='项目ID')
    year_month = db.Column(db.String(7), comment='年月，格式：YYYY-MM')
    total_cost = db.Column(db.Float, default=0, comment='总成本')
    bom_cost = db.Column(db.Float, default=0, comment='BOM成本')
    labor_cost = db.Column(db.Float, default=0, comment='人工成本')
    other_cost = db.Column(db.Float, default=0, comment='其他成本')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    # 关联项目表
    project = db.relationship('Import_project', backref='actual_costs')
    
    # 唯一约束确保每个项目每月只有一条记录
    __table_args__ = (db.UniqueConstraint('project_id', 'year_month', name='uix_project_month'),)


class BOMCostImport(db.Model):
    """BOM成本导入记录表"""
    __tablename__ = 'bom_cost_import'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    project_id = db.Column(db.Integer, db.ForeignKey('import_project.id'), comment='项目ID')
    year_month = db.Column(db.String(7), comment='年月，格式：YYYY-MM')
    amount = db.Column(db.Float, default=0, comment='金额')
    import_file = db.Column(db.String(255), comment='导入文件名')
    import_by = db.Column(db.Integer, db.ForeignKey('admin_user.id'), comment='导入人ID')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    
    # 关联项目表
    project = db.relationship('Import_project', backref='bom_imports')


class OtherCostImport(db.Model):
    """其他成本导入记录表"""
    __tablename__ = 'other_cost_import'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    project_id = db.Column(db.Integer, db.ForeignKey('import_project.id'), comment='项目ID')
    year_month = db.Column(db.String(7), comment='年月，格式：YYYY-MM')
    cost_type = db.Column(db.String(50), comment='成本类型')
    amount = db.Column(db.Float, default=0, comment='金额')
    import_file = db.Column(db.String(255), comment='导入文件名')
    import_by = db.Column(db.Integer, db.ForeignKey('admin_user.id'), comment='导入人ID')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    
    # 关联项目表
    project = db.relationship('Import_project', backref='other_imports')


class ProjectLaborCost(db.Model):
    """项目月度人工成本表"""
    __tablename__ = 'project_labor_cost'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    project_id = db.Column(db.Integer, db.ForeignKey('import_project.id'), comment='项目ID')
    work_month = db.Column(db.String(7), comment='工作月份，格式：YYYY-MM')
    salary_month = db.Column(db.String(7), comment='工资发放月份，格式：YYYY-MM')
    total_regular_hours = db.Column(db.Float, default=0, comment='总正常工时')
    total_overtime_hours = db.Column(db.Float, default=0, comment='总加班工时')
    total_hours = db.Column(db.Float, default=0, comment='总工时')
    total_cost = db.Column(db.Float, default=0, comment='总人工成本')
    cost_details = db.Column(db.Text, comment='成本详情，JSON格式')
    calculation_method = db.Column(db.String(20), default='timesheet', comment='计算方法：timesheet-基于工时，salary-基于薪资，actual_salary-基于实际薪资')
    calculated_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='计算时间')
    
    # 关联项目表
    project = db.relationship('Import_project', backref='labor_costs')
    
    # 唯一约束确保每个项目每月每种计算方法只有一条记录
    __table_args__ = (db.UniqueConstraint('project_id', 'work_month', 'calculation_method', name='uix_project_month_method'),)
    
    def __init__(self, **kwargs):
        super(ProjectLaborCost, self).__init__(**kwargs)
        # 确保total_hours是正常工时和加班工时的总和
        self.total_hours = self.total_regular_hours + self.total_overtime_hours 