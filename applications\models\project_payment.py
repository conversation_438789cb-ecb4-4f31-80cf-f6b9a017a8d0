import datetime
from applications.extensions import db
from sqlalchemy import UniqueConstraint


class ProjectPayment(db.Model):
    """项目付款记录表"""
    __tablename__ = 'project_payment'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='付款ID')
    project_id = db.Column(db.Integer, db.ForeignKey('import_project.id'), comment='项目ID')
    prepayment = db.Column(db.Float, default=0, comment='预付款金额')
    delivery_payment = db.Column(db.Float, default=0, comment='发货款金额')
    acceptance_payment = db.Column(db.Float, default=0, comment='验收款金额')
    warranty_payment = db.Column(db.Float, default=0, comment='质保金金额')
    actual_prepayment = db.Column(db.Float, default=0, comment='实际预付款回款金额')
    actual_delivery_payment = db.Column(db.Float, default=0, comment='实际发货款回款金额')
    actual_acceptance_payment = db.Column(db.Float, default=0, comment='实际验收款回款金额')
    actual_warranty_payment = db.Column(db.Float, default=0, comment='实际质保金回款金额')

    # 各类型付款日期
    prepayment_date = db.Column(db.Date, comment='预付款付款日期')
    delivery_payment_date = db.Column(db.Date, comment='发货款付款日期')
    acceptance_payment_date = db.Column(db.Date, comment='验收款付款日期')
    warranty_payment_date = db.Column(db.Date, comment='质保金付款日期')

    # 各类型付款备注
    prepayment_remark = db.Column(db.String(255), comment='预付款备注')
    delivery_payment_remark = db.Column(db.String(255), comment='发货款备注')
    acceptance_payment_remark = db.Column(db.String(255), comment='验收款备注')
    warranty_payment_remark = db.Column(db.String(255), comment='质保金备注')

    # 保留原有字段，用于兼容旧数据
    payment_date = db.Column(db.Date, comment='付款日期（旧字段，保留兼容）')
    payment_status = db.Column(db.Integer, default=0, comment='付款状态（0:未付款, 1:已付款）')
    remark = db.Column(db.String(255), comment='备注（旧字段，保留兼容）')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')

    # 关联项目表
    project = db.relationship('Import_project', backref='payment')

    # 添加唯一约束，确保每个项目只有一条付款记录
    __table_args__ = (UniqueConstraint('project_id', name='uix_project_payment_project_id'),)

    def __init__(self, **kwargs):
        super(ProjectPayment, self).__init__(**kwargs)
