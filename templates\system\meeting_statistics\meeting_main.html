<!DOCTYPE html>
<html>
<head>
    <title>统计数据</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
</head>
<style>
        .layui-col-md2 {
            width: 14%;
            font-size: 13px;
        }
        .layui-laypage {
            margin: 20px 0;
            text-align: center;
        }
        .layui-laypage a, .layui-laypage span {
            padding: 0 15px;
            height: 30px;
            line-height: 30px;
            margin: 0 3px;
            border: 1px solid #e2e2e2;
            background-color: #fff;
            display: inline-block;
            vertical-align: middle;
            color: #333;
            text-decoration: none;
        }
        .layui-laypage .layui-laypage-curr {
            background-color: #009688;
            color: #fff;
        }
        .layui-laypage .layui-laypage-curr em {
            color: #fff;
        }
        .layui-laypage .layui-disabled {
            color: #c2c2c2;
            cursor: not-allowed;
            pointer-events: none;
            background-color: #f5f5f5;
            border-color: #e6e6e6;
        }
        .layui-laypage-skip {
            margin-left: 20px;
        }
        .layui-select {
            height: 30px;
            padding: 0 10px;
            border: 1px solid #e2e2e2;
            border-radius: 2px;
        }
        .chart-container {
            position: relative;
            min-height: 400px;
        }
        .chart-tooltip {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: #fff;
            padding: 5px 10px;
            border-radius: 3px;
            display: none;
        }
        .layui-form-item {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .layui-form-label {
            padding: 9px 8px;
            width: auto;
        }
        .layui-input, .layui-select {
            height: 32px;
            line-height: 32px;
        }
        .layui-btn-sm {
            height: 32px;
            line-height: 32px;
            padding: 0 10px;
        }
        .search-bar {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 0;
        }
        .search-label {
            white-space: nowrap;
            color: #666;
        }
        .search-input {
            min-width: 120px;
        }
        .search-input input,
        .search-input select {
            width: 100%;
            height: 32px;
            line-height: 32px;
            padding: 0 10px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
        }
        .search-buttons {
            margin-left: 10px;
            white-space: nowrap;
        }
        .search-buttons .layui-btn {
            height: 32px;
            line-height: 32px;
            padding: 0 15px;
        }
        .layui-form select {
            height: 32px;
        }
</style>

<body class="pear-container">
    <!-- <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">当前筛选条件</div>
                        <div class="layui-card-body">
                            <span class="layui-badge layui-bg-blue">项目前缀：{{ project_prefix or '全部' }}</span>
                            <span class="layui-badge layui-bg-blue">项目编号：{{ project_number or '全部' }}</span>
                            <span class="layui-badge layui-bg-blue">开始时间：{{ start_date if start_date else '未选择' }}</span>
                            <span class="layui-badge layui-bg-blue">结束时间：{{ end_date if end_date else '未选择' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> -->

    <!-- 在搜索栏上方添加功能开关区域，只对admin用户可见 -->
    {% if current_user.username == 'admin' %}
    <div class="layui-card">
        <div class="layui-card-header">功能配置</div>
        <div class="layui-card-body">
            <div class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">启用售后部数据处理</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="enable_after_sales" lay-skin="switch" lay-text="启用|禁用" lay-filter="enable-after-sales" {% if after_sales_enabled == 1 %}checked{% endif %}>
                        <div class="layui-word-aux" style="margin-left: 5px;">
                            启用后，生产部和电控部的厂外数据将被算入售后部，同时这些部门不会重复计算厂外数据
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="layui-card">
        <div class="layui-card-body">
            <form class="layui-form" action="" method="get">
                <div class="search-bar">
                    <div class="search-label">时间范围</div>
                    <div class="search-input">
                                <select name="time_range" lay-filter="time_range">
                                    <option value="year" {% if time_range == 'year' %}selected{% endif %}>当年</option>
                                    <option value="quarter" {% if time_range == 'quarter' %}selected{% endif %}>当前季度</option>
                                    <option value="month" {% if time_range == 'month' %}selected{% endif %}>当前上月</option>
                                    <option value="custom" {% if time_range == 'custom' %}selected{% endif %}>自定义</option>
                                </select>
                    </div>

                    <div class="search-label">部门</div>
                    <div class="search-input">
                                <select name="dept_id" {% if not has_sub_depts and current_user.username != 'admin' %}disabled{% endif %}>
                                    <option value="">全部</option>
                                    {% for dept in depts %}
                                    <option value="{{ dept.id }}" {% if dept_id == dept.id|string %}selected{% endif %}>{{ dept.dept_name }}</option>
                                    {% endfor %}
                                </select>
                    </div>

                    <div class="search-label">工作地点</div>
                    <div class="search-input">
                        <select name="location" id="location-select">
                            <option value="">全部</option>
                            <option value="厂内" {% if location == '厂内' %}selected{% endif %}>厂内</option>
                            <option value="厂外" {% if location == '厂外' %}selected{% endif %}>厂外</option>
                        </select>
                    </div>

                    <div class="search-label">工时类型</div>
                    <div class="search-input">
                                <input type="text" name="project_prefix" placeholder="请输入工时类型" class="layui-input" value="{{ project_prefix or '' }}">
                    </div>

                    <div class="search-label">项目编号</div>
                    <div class="search-input">
                                <input type="text" name="project_number" placeholder="请输入项目编号" class="layui-input" value="{{ project_number or '' }}">
                    </div>

                    <div class="search-label">开始时间</div>
                    <div class="search-input">
                        <input type="text" name="start_date" placeholder="开始时间" class="layui-input" id="start_date" value="{{ start_date or '' }}" {% if time_range != 'custom' %}disabled{% endif %}>
                    </div>

                    <div class="search-label">结束时间</div>
                    <div class="search-input">
                        <input type="text" name="end_date" placeholder="结束时间" class="layui-input" id="end_date" value="{{ end_date or '' }}" {% if time_range != 'custom' %}disabled{% endif %}>
                    </div>

                    <div class="search-buttons">
                        <button class="layui-btn" lay-submit>
                            <i class="layui-icon layui-icon-search"></i>查询
                                </button>
                        <button type="reset" class="layui-btn layui-btn-primary">
                            <i class="layui-icon layui-icon-refresh"></i>重置
                                </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <!-- 员工日志数据 -->
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            员工日志数据
                            <small style="color: #999; margin-left: 10px;">(显示所有工时类型，包括项目、会议和其他)</small>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="exportEmployeeLogs()" style="float: right; margin-right: 5px;">
                                <i class="layui-icon layui-icon-export"></i>导出Excel
                            </button>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>员工姓名</th>
                                        <th>月份</th>
                                        <th>工作地点</th>
                                        <th>项目前缀</th>
                                        <th>项目编号</th>
                                        <th>正工时</th>
                                        <th>加班工时</th>
                                        <th>加班工资</th>
                                        <th>正工时费用</th>
                                        <th>总费用</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if employee_logs %}
                                        {% for log in employee_logs %}
                                        <tr>
                                            <td>{{ log.name }}</td>
                                            <td>{{ log.month }}</td>
                                            <td>{{ log.location if log.location else '未知' }}</td>
                                            <td>{{ log.projectPrefix }}</td>
                                            <td>{{ log.projectNumber if log.projectNumber and log.projectNumber.strip() else '无' }}</td>
                                            <td>{{ log.regularWorkingHours }}</td>
                                            <td>{{ log.overtimeWorkingHours }}</td>
                                            <td>{{ log.overtime_pay }}</td>
                                            <td>{{ log.regular_pay }}</td>
                                            <td>{{ log.total_pay }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="10" style="text-align: center; color: #999;">未查询到数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 部门费用数据 -->
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            部门费用
                            <small style="color: #999; margin-left: 10px;">(显示所有工时类型，包括项目、会议和其他)</small>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="exportDeptPay()" style="float: right; margin-right: 5px;">
                                <i class="layui-icon layui-icon-export"></i>导出Excel
                            </button>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>部门</th>
                                        <th>月份</th>
                                        <th>工作地点</th>
                                        <th>工时类型</th>
                                        <th>项目编号</th>
                                        <th>正工时时长</th>
                                        <th>加班工时时长</th>
                                        <th>正工时费用</th>
                                        <th>加班费用</th>
                                        <th>总费用</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if dept_pay %}
                                        {% for dept in dept_pay %}
                                        {% if dept.total_regular_pay > 0 %}
                                        <tr>
                                            <td>{{ dept.dept_name if dept.dept_name else '未知部门' }}</td>
                                            <td>{{ dept.month }}</td>
                                            <td>{{ dept.location if dept.location else '未知' }}</td>
                                            <td>{{ dept.projectPrefix }}</td>
                                            <td>{{ dept.projectNumber if dept.projectNumber and dept.projectNumber.strip() else '无' }}</td>
                                            <td>{{ dept.total_regular_hours }}</td>
                                            <td>{{ dept.total_overtime_hours }}</td>
                                            <td>{{ dept.total_regular_pay }}</td>
                                            <td>{{ dept.total_overtime_pay }}</td>
                                            <td>{{ dept.total_pay }}</td>
                                        </tr>
                                        {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="10" style="text-align: center; color: #999;">未查询到数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 修改月度费用汇总表格 -->
            <div class="layui-card">
                <div class="layui-card-header">
                    月度费用汇总（按部门）
                    <small style="color: #999; margin-left: 10px;">(显示所有工时类型，包括项目、会议和其他)</small>
                    <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="exportMonthlySummary()" style="float: right; margin-right: 5px;">
                        <i class="layui-icon layui-icon-export"></i>导出Excel
                    </button>
                </div>
                <div class="layui-card-body">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>月份</th>
                                <th>部门</th>
                                <th>正工时费用</th>
                                <th>加班工时费用</th>
                                <th>总费用</th>
                                <th>应发工资</th>
                                <th>差额</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if monthly_summary %}
                                {% for summary in monthly_summary %}
                                <tr>
                                    <td>{{ summary.month }}</td>
                                    <td>{{ summary.dept_name }}</td>
                                    <td>{{ summary.total_regular_pay }}</td>
                                    <td>{{ summary.total_overtime_pay }}</td>
                                    <td>{{ summary.total_pay }}</td>
                                    <td>{{ summary.should_pay }}</td>
                                    <td {% if summary.difference != 0 %}style="color: red;"{% endif %}>
                                        {{ summary.difference * -1 }}
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" style="text-align: center; color: #999;">未查询到数据</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 项目日志数据 -->
            <!-- <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">项目日志数据</div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>员工姓名</th>
                                        <th>项目前缀</th>
                                        <th>项目编号</th>
                                        <th>正工时</th>
                                        <th>加班工时</th>
                                        <th>日期</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for log in all_project_logs %}
                                    <tr>
                                        <td>{{ log.name }}</td>
                                        <td>{{ log.projectPrefix }}</td>
                                        <td>{{ log.projectNumber }}</td>
                                        <td>{{ log.regularWorkingHours }}</td>
                                        <td>{{ log.overtimeWorkingHours }}</td>
                                        <td>{{ log.created_at }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>
    </div>

    <!-- 将分页控件移动到图表上方 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div id="pagination" class="layui-box layui-laypage layui-laypage-default">
                <a href="?page={{ page - 1 }}&per_page={{ per_page }}"
                   class="layui-laypage-prev {% if page == 1 %}layui-disabled{% endif %}"
                   {% if page == 1 %}onclick="return false;"{% endif %}>上一页</a>
                {% for p in range(1, (total // per_page) + 2) %}
                    {% if p == page %}
                        <span class="layui-laypage-curr">
                            <em class="layui-laypage-em"></em>
                            <em>{{ p }}</em>
                        </span>
                    {% else %}
                        <a href="?page={{ p }}&per_page={{ per_page }}">{{ p }}</a>
                    {% endif %}
                {% endfor %}
                <a href="?page={{ page + 1 }}&per_page={{ per_page }}"
                   class="layui-laypage-next {% if page * per_page >= total %}layui-disabled{% endif %}"
                   {% if page * per_page >= total %}onclick="return false;"{% endif %}>下一页</a>
                <span class="layui-laypage-skip">
                    每页
                    <select class="" onchange="changePerPage(this.value)">
                        <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                        <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                        <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                    条，共 {{ total }} 条
                </span>
            </div>
        </div>
    </div>



    <!-- 在删除的图表位置添加新的可视化图表区域 -->
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <!-- 合并工时分布和员工费用对比图表 -->
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            工时分布与费用对比
                            <small style="color: #999; margin-left: 10px;">(仅包含会议和其他类型工时，未在正工时时间段发生不展示)</small>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="exportCombinedChart()" style="float: right; margin-right: 5px;">
                                <i class="layui-icon layui-icon-chart"></i>导出图表
                            </button>
                        </div>
                        <div class="layui-card-body">
                            <div id="combinedChart" style="width: 100%; height: 500px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row" style="margin-top: 20px;">
                <!-- 部门费用分布图表 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            部门费用分布
                            <small style="color: #999; margin-left: 10px;">(仅包含会议和其他类型工时，未在正工时时间段发生不展示)</small>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="exportDeptCostChart()" style="float: right; margin-right: 5px;">
                                <i class="layui-icon layui-icon-chart"></i>导出图表
                            </button>
                        </div>
                        <div class="layui-card-body">
                            <div id="deptCostChart" style="width: 100%; height: 400px;"></div>
                        </div>
        </div>
    </div>

                <!-- 加班/正工时对比图表 -->
                <div class="layui-col-md6">
    <div class="layui-card">
                        <div class="layui-card-header">
                            工时类型分布
                            <small style="color: #999; margin-left: 10px;">(仅包含会议和其他类型工时，未在正工时时间段发生不展示)</small>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="exportWorkTypeChart()" style="float: right; margin-right: 5px;">
                                <i class="layui-icon layui-icon-chart"></i>导出图表
                            </button>
                        </div>
        <div class="layui-card-body">
                            <div id="workTypeChart" style="width: 100%; height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目工时费用图表 -->
            <div class="layui-row" style="margin-top: 20px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            项目工时费用分析
                            <small style="color: #999; margin-left: 10px;">(仅包含会议和其他类型工时，未在正工时时间段发生不展示)</small>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="exportProjectCostChart()" style="float: right; margin-right: 5px;">
                                <i class="layui-icon layui-icon-chart"></i>导出图表
                            </button>
                        </div>
                        <div class="layui-card-body">
                            <div id="projectCostChart" style="width: 100%; height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 在其他图表后面添加月度费用图表 -->
            <div class="layui-row" style="margin-top: 20px;">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            月度费用汇总图表
                            <small style="color: #999; margin-left: 10px;">(仅包含会议和其他类型工时，未在正工时时间段发生不展示)</small>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="exportMonthlyExpenseChart()" style="float: right; margin-right: 5px;">
                                <i class="layui-icon layui-icon-chart"></i>导出图表
                            </button>
                        </div>
                        <div class="layui-card-body">
                            <div id="monthlyExpenseChart" style="width: 100%; height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 调试信息区域，在开发环境中可见 -->
    <div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0; background-color: #f9f9f9;" id="debug-info">
        <h3>调试信息</h3>
        <p>后端传递的location: {{ location }}</p>
        <p>URL中的location: <span id="url-location"></span></p>
        <script>
            document.getElementById('url-location').textContent = new URLSearchParams(window.location.search).get('location') || '未设置';
        </script>
    </div>

    {% include 'system/common/footer.html' %}
<!--
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->
    <script src="{{ url_for('static', filename='index/js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='index/js/echarts.min.js') }}"></script>
    <script>
        layui.use(['element', 'table', 'laydate', 'form'], function(){
            var element = layui.element;
            var table = layui.table;
            var laydate = layui.laydate;
            var form = layui.form;

            // 初始化折叠面板
            element.render('collapse');

            // 初始化日期选择器
            laydate.render({
                elem: '#start_date',
                format: 'yyyy-MM-dd',
                max: new Date().toISOString().split('T')[0]  // 设置最大可选日期为今天
            });
            laydate.render({
                elem: '#end_date',
                format: 'yyyy-MM-dd',
                max: new Date().toISOString().split('T')[0]  // 设置最大可选日期为今天
            });

            // 监听时间范围选择器的变化
            form.on('select(time_range)', function(data){
                var timeRange = data.value;
                if (timeRange === 'custom') {
                    // 如果选择自定义，启用日期选择器
                    document.getElementById('start_date').disabled = false;
                    document.getElementById('end_date').disabled = false;
                } else {
                    // 如果选择其他范围，禁用日期选择器
                    document.getElementById('start_date').disabled = true;
                    document.getElementById('end_date').disabled = true;
                }
            });

            // 监听售后部数据处理开关
            form.on('switch(enable-after-sales)', function(data){
                var value = this.checked ? 1 : 0;

                // 发送AJAX请求更新配置
                $.ajax({
                    url: 'update_after_sales_config',
                    type: 'POST',
                    data: JSON.stringify({enable: value}),
                    contentType: 'application/json',
                    dataType: 'json',
                    success: function(res){
                        if(res.success){
                            layer.msg('配置已更新', {icon: 1});
                            // 刷新页面以应用新配置
                            setTimeout(function(){
                                location.reload();
                            }, 1000);
                        } else {
                            layer.msg(res.msg || '更新失败', {icon: 2});
                        }
                    },
                    error: function(){
                        layer.msg('网络错误，请稍后重试', {icon: 2});
                    }
                });
            });

            // 初始化图表
            initCharts();

            // 监听表单提交，更新图表
            form.on('submit', function(data) {
                // 在表单提交前检查location参数
                var locationValue = $('#location-select').val();
                console.log('表单提交，location值:', locationValue);

                // 将location值添加到调试信息区域
                $('#debug-info').append('<p>表单提交时的location值: ' + locationValue + '</p>');

                // 表单提交后会刷新页面，所以这里不需要额外处理
                return true;
            });
        });

        function changePerPage(perPage) {
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.set('per_page', perPage);
            window.location.search = urlParams.toString();
        }

        // 导出员工日志数据
        function exportEmployeeLogs() {
            // 获取当前URL所有参数
            const urlParams = new URLSearchParams(window.location.search);

            // 构建导出URL，包含所有参数
            let exportUrl = 'export_employee_logs?' + urlParams.toString();

            // 显示加载提示
            layer.msg('正在导出数据，请稍候...', {icon: 16, shade: 0.3, time: 0});

            // 执行下载
            let link = document.createElement('a');
            link.href = exportUrl;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 关闭加载提示，显示成功消息
            setTimeout(function() {
                layer.closeAll();
                layer.msg('导出成功', {icon: 1, time: 2000});
            }, 1000);
        }

        // 导出部门费用数据
        function exportDeptPay() {
            // 获取当前URL所有参数
            const urlParams = new URLSearchParams(window.location.search);

            // 构建导出URL，包含所有参数
            let exportUrl = 'export_dept_pay?' + urlParams.toString();

            // 显示加载提示
            layer.msg('正在导出数据，请稍候...', {icon: 16, shade: 0.3, time: 0});

            // 执行下载
            let link = document.createElement('a');
            link.href = exportUrl;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 关闭加载提示，显示成功消息
            setTimeout(function() {
                layer.closeAll();
                layer.msg('导出成功', {icon: 1, time: 2000});
            }, 1000);
        }

        // 导出月度费用汇总
        function exportMonthlySummary() {
            // 获取当前URL所有参数
            const urlParams = new URLSearchParams(window.location.search);

            // 构建导出URL，包含所有参数
            let exportUrl = 'export_monthly_summary?' + urlParams.toString();

            // 显示加载提示
            layer.msg('正在导出数据，请稍候...', {icon: 16, shade: 0.3, time: 0});

            // 执行下载
            let link = document.createElement('a');
            link.href = exportUrl;
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 关闭加载提示，显示成功消息
            setTimeout(function() {
                layer.closeAll();
                layer.msg('导出成功', {icon: 1, time: 2000});
            }, 1000);
        }

        // 初始化所有图表
        function initCharts() {
            initCombinedChart();
            initDeptCostChart();
            initWorkTypeChart();
            initProjectCostChart();
            initMonthlyExpenseChart();
        }

        // 修改 initCombinedChart 函数
        function initCombinedChart() {
            var chartDom = document.getElementById('combinedChart');
            var myChart = echarts.init(chartDom);
            var option;

            // 准备数据
            var employeeNames = [];
            var regularHours = [];
            var overtimeHours = [];
            var regularPay = [];
            var overtimePay = [];

            {% for log in employee_logs %}
                employeeNames.push('{{ log.name }}');
                regularHours.push({{ log.regularWorkingHours }});
                overtimeHours.push({{ log.overtimeWorkingHours }});
                regularPay.push({{ log.regular_pay }});
                overtimePay.push({{ log.overtime_pay }});
            {% endfor %}

            // 计算总费用（不包含加班费）
            var totalPay = regularPay;

            option = {
            title: {
                    text: '',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                        type: 'cross'
                },
                formatter: function(params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function(param) {
                            var marker = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + param.color + '"></span>';
                            if (param.seriesName.includes('工时')) {
                                result += marker + param.seriesName + ': ' + param.value + ' 小时<br/>';
                            } else {
                                result += marker + param.seriesName + ': ' + param.value + ' 元<br/>';
                            }
                        });
                        return result;
                    }
                },
                toolbox: {
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        restore: {},
                        saveAsImage: {
                            name: '工时分布与费用对比',
                            pixelRatio: 2  // 提高导出图片的清晰度
                        },
                        magicType: {
                            type: ['bar', 'line', 'stack']
                        }
                    }
                },
            legend: {
                    data: ['正工时', '加班工时', '总费用'],
                    selected: {
                        '正工时': true,
                        '加班工时': true,
                        '总费用': true
                    }
                },
                dataZoom: [
                    {
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        start: 0,
                        end: 20  // 默认显示前20%的数据
                    },
                    {
                        type: 'inside',
                        xAxisIndex: [0],
                        start: 0,
                        end: 20
                    }
                ],
            grid: {
                left: '3%',
                    right: '8%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                    data: employeeNames,
                axisLabel: {
                    rotate: 45,
                        interval: 'auto'
                    }
                },
                yAxis: [
                    {
                type: 'value',
                        name: '工时（小时）',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: '费用（元）',
                        position: 'right'
                }
            ],
            series: [
                {
                    name: '正工时',
                    type: 'bar',
                        stack: '工时',
                        data: regularHours,
                    itemStyle: {
                            color: '#5FB878'
                    },
                    label: {
                        show: true,
                        position: 'inside',
                        formatter: '{c} h'
                    }
                },
                {
                    name: '加班工时',
                    type: 'bar',
                        stack: '工时',
                        data: overtimeHours,
                    itemStyle: {
                            color: '#FF5722'
                        },
                    label: {
                        show: true,
                        position: 'inside',
                        formatter: '{c} h'
                    }
                },
                    {
                        name: '总费用',
                        type: 'line',
                        yAxisIndex: 1,
                        data: totalPay,
                        itemStyle: {
                            color: '#1E9FFF'
                        },
                        symbol: 'circle',
                        symbolSize: 6,
                        sampling: 'average',  // 数据采样，优化性能
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c} 元'
                        }
                    }
                ]
            };

            option && myChart.setOption(option);

            // 添加图表点击事件
            myChart.on('click', function(params) {
                if (params.componentType === 'series') {
                    // 点击时可以显示详细信息或进行其他操作
                    console.log(params);
                }
            });

            // 性能优化：防抖处理窗口调整
            var resizeTimeout;
            window.addEventListener('resize', function() {
                if (resizeTimeout) clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    myChart.resize();
                }, 100);
            });
        }

        // 修改部门费用分布图表初始化函数
        function initDeptCostChart() {
            var chartDom = document.getElementById('deptCostChart');
            var myChart = echarts.init(chartDom);
            var option;

            // 添加调试输出，查看数据情况
            console.log("部门费用数据:", {{ dept_chart_data|tojson }});

            // 准备数据
            var deptData = [];

            {% for dept in dept_chart_data %}
                deptData.push({
                    name: '{{ dept.name }}',
                    value: {{ dept.value }},
                    dept_name: '{{ dept.dept_name if dept.dept_name else "未知部门" }}'
                });
            {% endfor %}

            // 再次添加调试输出，确认转换后的数据
            console.log("图表使用的数据:", deptData);

            if (deptData.length === 0) {
                // 如果没有数据，显示无数据提示
                myChart.setOption({
            title: {
                        text: '暂无部门费用数据',
                        left: 'center',
                        top: 'center',
                        textStyle: {
                            color: '#999',
                            fontSize: 16,
                            fontWeight: 'normal'
                        }
                    }
                });
                return;
            }

            option = {
            tooltip: {
                trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} 元 ({d}%)'
                },
                legend: {
                    type: 'scroll',
                    orient: 'vertical',
                    left: 10,
                    top: 20,
                    bottom: 20,
                    data: deptData.map(function(item) { return item.name; })
                },
                series: [
                    {
                        name: '部门费用',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            formatter: function(params) {
                                return params.name + '\n' + params.value.toLocaleString() + ' 元\n' +
                                       '(' + params.percent.toFixed(1) + '%)';
                            }
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: deptData
                    }
                ]
            };

            // 如果是柱状图类型，则改变图表配置
            {% if chart_type == 'bar' %}
            option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        // 显示部门名称和费用
                        var deptItem = deptData.find(function(item) { return item.name === params[0].name; });
                        var deptName = deptItem ? deptItem.dept_name : '';
                        return deptName + '<br/>' + params[0].name + ': ' + params[0].value + ' 元';
                    }
                },
            xAxis: {
                type: 'category',
                    data: deptData.map(function(item) { return item.name; }),
                axisLabel: {
                        interval: 0,
                    rotate: 45,
                        formatter: function(value) {
                            // 查找该工作地点对应的部门名称
                            var deptItem = deptData.find(function(item) { return item.name === value; });
                            var deptName = deptItem ? deptItem.dept_name : '';
                            // 显示部门名称和工作地点
                            var label = deptName + '-' + value;
                            if (label.length > 10) {
                                return label.substring(0, 8) + '...';
                            }
                            return label;
                        }
                }
            },
            yAxis: {
                type: 'value',
                    name: '费用(元)'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                series: [
                    {
                        name: '部门费用',
                type: 'bar',
                        data: deptData.map(function(item) { return item.value; }),
                itemStyle: {
                    color: function(params) {
                        var colorList = [
                                    '#5470c6', '#91cc75', '#fac858', '#ee6666',
                                    '#73c0de', '#3ba272', '#fc8452', '#9a60b4',
                                    '#ea7ccc', '#4ec1c1'
                        ];
                        return colorList[params.dataIndex % colorList.length];
                    }
                },
                label: {
                    show: true,
                    position: 'top',
                            formatter: '{c} 元'
                        }
                    }
                ]
            };
            {% endif %}

            // 添加调试信息
            console.log("ECharts选项:", option);

            option && myChart.setOption(option);

            // 窗口大小变化时，调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // 初始化加班/正工时对比图表
        function initWorkTypeChart() {
            var chartDom = document.getElementById('workTypeChart');
            var myChart = echarts.init(chartDom);
            var option;

            // 计算总工时
            var totalRegular = 0;
            var totalOvertime = 0;

            {% for log in employee_logs %}
                totalRegular += {{ log.regularWorkingHours }};
                totalOvertime += {{ log.overtimeWorkingHours }};
            {% endfor %}

            var data = [
                { value: totalRegular, name: '正工时' },
                { value: totalOvertime, name: '加班工时' }
            ];

            option = {
                title: {
                    text: '工时类型分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} 小时 ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['正工时', '加班工时']
                },
                series: [
                    {
                        name: '工时类型',
                type: 'pie',
                radius: '50%',
                        data: data,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                        itemStyle: {
                            color: function(params) {
                                var colorList = ['#5FB878', '#FF5722'];
                                return colorList[params.dataIndex];
                            }
                        },
                        label: {
                            show: true,
                            formatter: function(params) {
                                return params.name + '\n' +
                                       params.value.toLocaleString() + ' h\n' +
                                       '(' + params.percent.toFixed(1) + '%)';
                            }
                        }
                    }
                ]
            };

            option && myChart.setOption(option);

            // 窗口大小变化时，调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // 添加项目工时费用图表初始化函数
        function initProjectCostChart() {
            var chartDom = document.getElementById('projectCostChart');
            var myChart = echarts.init(chartDom);
            var option;

            // 准备数据
            var projectNames = [];
            var projectRegularHours = [];
            var projectOvertimeHours = [];
            var projectRegularPay = [];
            var projectOvertimePay = [];

            {% for project in top_projects %}
                projectNames.push('{{ project.name }}');
                projectRegularHours.push({{ project.regular_hours }});
                projectOvertimeHours.push({{ project.overtime_hours }});
                // 计算费用数据 - 假设这些值已经在后端计算好了
                {% if project.regular_pay is defined %}
                projectRegularPay.push({{ project.regular_pay }});
                {% else %}
                projectRegularPay.push(0);
            {% endif %}

                {% if project.overtime_pay is defined %}
                projectOvertimePay.push({{ project.overtime_pay }});
                {% else %}
                projectOvertimePay.push(0);
                {% endif %}
            {% endfor %}

            option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function(param) {
                            var marker = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + param.color + '"></span>';
                            if (param.seriesName.includes('工时')) {
                                result += marker + param.seriesName + ': ' + param.value + ' 小时<br/>';
                            } else {
                                result += marker + param.seriesName + ': ' + param.value + ' 元<br/>';
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['正工时', '加班工时', '正工时费用'],
                    selected: {
                        '正工时': true,
                        '加班工时': true,
                        '正工时费用': false
                    }
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        magicType: {type: ['bar', 'line']},
                        restore: {},
                        saveAsImage: {}
                    }
                },
                xAxis: [
                    {
                        type: 'category',
                        data: projectNames,
                        axisLabel: {
                            rotate: 45,
                            interval: 0,
                            formatter: function(value) {
                                if (value.length > 15) {
                                    return value.substring(0, 12) + '...';
                                }
                                return value;
                            }
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '工时（小时）',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: '费用（元）',
                        position: 'right'
                    }
                ],
                grid: {
                    left: '3%',
                    right: '8%',
                    bottom: '15%',
                    containLabel: true
                },
                dataZoom: [
                    {
                        type: 'slider',
                        show: true,
                        xAxisIndex: [0],
                        start: 0,
                        end: 100
                    }
                ],
                series: [
                    {
                        name: '正工时',
                        type: 'bar',
                        stack: '工时',
                        emphasis: {
                            focus: 'series'
                        },
                        data: projectRegularHours,
                        itemStyle: {
                            color: '#5FB878'
                        },
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: '{c} h'
                        }
                    },
                    {
                        name: '加班工时',
                        type: 'bar',
                        stack: '工时',
                        emphasis: {
                            focus: 'series'
                        },
                        data: projectOvertimeHours,
                        itemStyle: {
                            color: '#FF5722'
                        },
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: '{c} h'
                        }
                    },
                    {
                        name: '正工时费用',
                        type: 'line',
                        yAxisIndex: 1,
                        data: projectRegularPay,
                        itemStyle: {
                            color: '#1E9FFF'
                        },
                        symbol: 'circle',
                        symbolSize: 8,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c} 元'
                        }
                    },

                ]
            };

            option && myChart.setOption(option);

            // 点击事件处理
            myChart.on('click', function(params) {
                if (params.componentType === 'series') {
                    // 当点击图表中的项目时，自动填充项目信息到筛选表单
                    var projectInfo = params.name.split('-');
                    if (projectInfo.length >= 2) {
                        var prefix = projectInfo[0];
                        var number = projectInfo[1];

                        // 填充表单
                        var prefixInput = document.querySelector('input[name="project_prefix"]');
                        var numberInput = document.querySelector('input[name="project_number"]');

                        if (prefixInput) prefixInput.value = prefix;
                        if (numberInput) numberInput.value = number;

                        // 可选：自动提交表单
                        // document.querySelector('.layui-form').submit();
                    }
                }
            });

            // 窗口大小变化时，调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // 修改月度费用图表的初始化函数
        function initMonthlyExpenseChart() {
            var chartDom = document.getElementById('monthlyExpenseChart');
            var myChart = echarts.init(chartDom);
            var option;

            // 定义部门颜色映射
            var deptColors = {
                '生产部': '#5470c6',
                '电控部': '#91cc75',
                '售后部': '#fac858',
                '研发部': '#ee6666',
                '采购部': '#73c0de',
                '品质部': '#3ba272'
                // 可以继续添加更多部门的颜色
            };

            // 准备数据
            var months = [];
            var deptNames = new Set();
            var deptData = {};

            {% for summary in monthly_summary %}
                if (!months.includes('{{ summary.month }}')) {
                    months.push('{{ summary.month }}');
                }
                if (!deptNames.has('{{ summary.dept_name }}')) {
                    deptNames.add('{{ summary.dept_name }}');
                }
                if (!deptData['{{ summary.dept_name }}']) {
                    deptData['{{ summary.dept_name }}'] = {};
                }
                deptData['{{ summary.dept_name }}']['{{ summary.month }}'] = {
                    regular: {{ summary.total_regular_pay }},
                    overtime: {{ summary.total_overtime_pay }},
                    total: {{ summary.total_pay }},
                    should: {{ summary.should_pay }}
                };
            {% endfor %}

            // 转换数据为图表所需格式
            var series = [];
            var legendData = [];

            // 对月份进行排序
            months.sort();

            // 为每个部门创建系列
            deptNames.forEach(function(dept) {
                var color = deptColors[dept] || '#' + Math.floor(Math.random()*16777215).toString(16);

                // 正工时数据
                var regularData = months.map(function(month) {
                    return deptData[dept][month] ? deptData[dept][month].regular : 0;
                });

                // 加班费用数据
                var overtimeData = months.map(function(month) {
                    return deptData[dept][month] ? deptData[dept][month].overtime : 0;
                });

                // 应发工资数据
                var shouldData = months.map(function(month) {
                    return deptData[dept][month] ? deptData[dept][month].should : 0;
                });

                // 计算差额数据
                var differenceData = months.map(function(month) {
                    if (!deptData[dept][month]) return 0;
                    return deptData[dept][month].total - deptData[dept][month].should;
                });

                // 添加正工时柱状图
                series.push({
                    name: dept + '-正工时',
                    type: 'bar',
                    stack: dept,
                    barWidth: '50%',
                    itemStyle: {
                        color: color,
                        opacity: 0.8
                    },
                    data: regularData,
                    label: {
                        show: true,
                        position: 'inside',
                        formatter: function(params) {
                            return params.value > 0 ? params.value.toLocaleString() + '元' : '';
                        }
                    }
                });



                // 添加应发工资折线图
                series.push({
                    name: dept + '-应发工资',
                    type: 'line',
                    symbol: 'circle',
                    symbolSize: 8,
                    itemStyle: {
                        color: color
                    },
                    lineStyle: {
                        type: 'dashed'
                    },
                    data: shouldData,
                    label: {
                        show: true,
                        position: 'top',
                        formatter: '{c} 元'
                    }
                });

                // 添加差额折线图
                series.push({
                    name: dept + '-差额',
                    type: 'line',
                    symbol: 'triangle',
                    symbolSize: 8,
                    itemStyle: {
                        color: function(params) {
                            return params.value >= 0 ? '#91cc75' : '#ee6666';
                        }
                    },
                    lineStyle: {
                        type: 'solid',
                        width: 2,
                        color: function(params) {
                            return params.value >= 0 ? '#91cc75' : '#ee6666';
                        }
                    },
                    data: differenceData,
                    label: {
                        show: true,
                        position: 'top',
                        formatter: function(params) {
                            var value = params.value.toLocaleString();
                            return params.value >= 0 ? '+' + value + '元' : value + '元';
                        },
                        color: function(params) {
                            return params.value >= 0 ? '#91cc75' : '#ee6666';
                        }
                    }
                });

                // 添加图例数据
                legendData.push(dept + '-正工时');
                legendData.push(dept + '-应发工资');
                legendData.push(dept + '-差额');
            });

            option = {
                title: {
                    text: '部门月度费用对比',
                    left: 'center',
                    top: 0
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        var month = params[0].axisValue;
                        var result = month + '<br/>';
                        var deptTotals = {};
                        var differences = {};

                        params.forEach(function(param) {
                            var deptName = param.seriesName.split('-')[0];
                            var type = param.seriesName.split('-')[1];

                            if (!deptTotals[deptName]) {
                                deptTotals[deptName] = 0;
                            }

                            if (param.seriesType === 'bar') {
                                deptTotals[deptName] += param.value;
                            }

                            if (type === '差额') {
                                differences[deptName] = param.value;
                            }

                            var marker = '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + param.color + '"></span>';
                            result += marker + param.seriesName + ': ' + param.value.toLocaleString() + ' 元<br/>';
                        });

                        // 添加每个部门的总计和差额
                        result += '<br/>部门汇总:<br/>';
                        for (var dept in deptTotals) {
                            result += dept + ' 总费用: ' + deptTotals[dept].toLocaleString() + ' 元<br/>';
                            if (differences[dept] !== undefined) {
                                var diffColor = differences[dept] >= 0 ? 'green' : 'red';
                                result += '<span style="color:' + diffColor + '">差额: ' +
                                         differences[dept].toLocaleString() + ' 元</span><br/>';
                            }
                        }

                        return result;
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: 30,
                    data: legendData,
                    selected: legendData.reduce((acc, curr) => {
                        // 默认显示正工时、加班和差额，隐藏应发工资
                        acc[curr] = !curr.includes('应发工资');
                        return acc;
                    }, {})
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true,
                    top: 100
                },
                xAxis: {
                    type: 'category',
                    data: months,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '金额（元）',
                    axisLabel: {
                        formatter: function(value) {
                            return value.toLocaleString();
                        }
                    }
                },
                series: series,
                dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    bottom: 10
                }, {
                    type: 'inside',
                    start: 0,
                    end: 100
                }],
                toolbox: {
                    feature: {
                        saveAsImage: {},
                        dataView: {},
                        magicType: {
                            type: ['line', 'bar', 'stack']
                        },
                        restore: {}
                    }
                }
            };

            option && myChart.setOption(option);

            // 窗口大小变化时，调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // 页面加载完成后立即执行
        document.addEventListener('DOMContentLoaded', function() {
            // 获取URL中的location参数
            const urlParams = new URLSearchParams(window.location.search);
            const locationParam = urlParams.get('location');

            // 获取下拉框元素
            const locationSelect = document.getElementById('location-select');

            // 根据URL参数设置下拉框的选中值
            if (locationParam === '厂内') {
                locationSelect.value = '厂内';
            } else if (locationParam === '厂外') {
                locationSelect.value = '厂外';
            } else if (locationParam === '') {
                locationSelect.value = '';
            }

            // 如果URL中的location参数与下拉框不一致，则同步它们
            if ((locationParam === '厂外' && locationSelect.value !== '厂外') ||
                (locationParam === '厂内' && locationSelect.value !== '厂内') ||
                (locationParam === '' && locationSelect.value !== '')) {
                // 创建新的表单并提交，以更新URL参数和页面显示
                const form = document.querySelector('form');
                form.submit();
            }

            // 在控制台输出调试信息
            console.log("后端传递的location:", {{ location|tojson }});
            console.log("URL中的location:", new URLSearchParams(window.location.search).get('location'));
        });

        // 图表导出函数
        function exportCombinedChart() {
            // 获取图表实例
            let chartDom = document.getElementById('combinedChart');
            let chart = echarts.getInstanceByDom(chartDom);

            if (!chart) {
                layer.msg('图表不存在', {icon: 2});
                return;
            }

            // 显示加载提示
            layer.msg('正在导出图表，请稍候...', {icon: 16, shade: 0.3, time: 0});

            try {
                // 触发ECharts导出图片功能
                let url = chart.getDataURL({
                    type: 'png',
                    pixelRatio: 2,  // 提高导出图片的清晰度
                    backgroundColor: '#fff',
                    excludeComponents: ['toolbox']  // 导出时排除工具箱
                });

                // 创建下载链接
                let link = document.createElement('a');
                link.href = url;
                link.download = '工时分布与费用对比_' + new Date().getTime() + '.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功消息
                layer.closeAll();
                layer.msg('导出成功', {icon: 1, time: 2000});
            } catch (error) {
                console.error('导出图表失败:', error);
                layer.closeAll();
                layer.msg('导出失败: ' + error.message, {icon: 2});
            }
        }

        // 部门费用分布图表导出
        function exportDeptCostChart() {
            let chartDom = document.getElementById('deptCostChart');
            let chart = echarts.getInstanceByDom(chartDom);

            if (!chart) {
                layer.msg('图表不存在', {icon: 2});
                return;
            }

            // 显示加载提示
            layer.msg('正在导出图表，请稍候...', {icon: 16, shade: 0.3, time: 0});

            try {
                // 触发ECharts导出图片功能
                let url = chart.getDataURL({
                    type: 'png',
                    pixelRatio: 2,
                    backgroundColor: '#fff',
                    excludeComponents: ['toolbox']
                });

                // 创建下载链接
                let link = document.createElement('a');
                link.href = url;
                link.download = '部门费用分布_' + new Date().getTime() + '.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功消息
                layer.closeAll();
                layer.msg('导出成功', {icon: 1, time: 2000});
            } catch (error) {
                console.error('导出图表失败:', error);
                layer.closeAll();
                layer.msg('导出失败: ' + error.message, {icon: 2});
            }
        }

        // 加班/正工时对比图表导出
        function exportWorkTypeChart() {
            let chartDom = document.getElementById('workTypeChart');
            let chart = echarts.getInstanceByDom(chartDom);

            if (!chart) {
                layer.msg('图表不存在', {icon: 2});
                return;
            }

            // 显示加载提示
            layer.msg('正在导出图表，请稍候...', {icon: 16, shade: 0.3, time: 0});

            try {
                // 触发ECharts导出图片功能
                let url = chart.getDataURL({
                    type: 'png',
                    pixelRatio: 2,
                    backgroundColor: '#fff',
                    excludeComponents: ['toolbox']
                });

                // 创建下载链接
                let link = document.createElement('a');
                link.href = url;
                link.download = '加班正工时对比_' + new Date().getTime() + '.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功消息
                layer.closeAll();
                layer.msg('导出成功', {icon: 1, time: 2000});
            } catch (error) {
                console.error('导出图表失败:', error);
                layer.closeAll();
                layer.msg('导出失败: ' + error.message, {icon: 2});
            }
        }

        // 项目工时费用分析图表导出
        function exportProjectCostChart() {
            let chartDom = document.getElementById('projectCostChart');
            let chart = echarts.getInstanceByDom(chartDom);

            if (!chart) {
                layer.msg('图表不存在', {icon: 2});
                return;
            }

            // 显示加载提示
            layer.msg('正在导出图表，请稍候...', {icon: 16, shade: 0.3, time: 0});

            try {
                // 触发ECharts导出图片功能
                let url = chart.getDataURL({
                    type: 'png',
                    pixelRatio: 2,
                    backgroundColor: '#fff',
                    excludeComponents: ['toolbox']
                });

                // 创建下载链接
                let link = document.createElement('a');
                link.href = url;
                link.download = '项目工时费用分析_' + new Date().getTime() + '.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功消息
                layer.closeAll();
                layer.msg('导出成功', {icon: 1, time: 2000});
            } catch (error) {
                console.error('导出图表失败:', error);
                layer.closeAll();
                layer.msg('导出失败: ' + error.message, {icon: 2});
            }
        }

        // 月度费用汇总图表导出
        function exportMonthlyExpenseChart() {
            let chartDom = document.getElementById('monthlyExpenseChart');
            let chart = echarts.getInstanceByDom(chartDom);

            if (!chart) {
                layer.msg('图表不存在', {icon: 2});
                return;
            }

            // 显示加载提示
            layer.msg('正在导出图表，请稍候...', {icon: 16, shade: 0.3, time: 0});

            try {
                // 触发ECharts导出图片功能
                let url = chart.getDataURL({
                    type: 'png',
                    pixelRatio: 2,
                    backgroundColor: '#fff',
                    excludeComponents: ['toolbox']
                });

                // 创建下载链接
                let link = document.createElement('a');
                link.href = url;
                link.download = '月度费用汇总_' + new Date().getTime() + '.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功消息
                layer.closeAll();
                layer.msg('导出成功', {icon: 1, time: 2000});
            } catch (error) {
                console.error('导出图表失败:', error);
                layer.closeAll();
                layer.msg('导出失败: ' + error.message, {icon: 2});
            }
        }
    </script>
</body>
</html>

