<!DOCTYPE html>
<html>
<head>
    <title>邮件管理</title>
    {% include 'system/common/header.html' %}
</head>
<body>
<form class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">收件人邮箱</label>
                    <div class="layui-input-block">
                        <input type="text" name="receiver" lay-verify="title" autocomplete="off"
                               placeholder="请输入收件人邮箱，多个邮箱使用英文;分割" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">主题</label>
                    <div class="layui-input-block">
                        <input type="text" name="subject" lay-verify="title" autocomplete="off" placeholder="请输入标题"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">正文</label>
                    <div class="layui-input-block">
                        <textarea placeholder="请输入内容" class="layui-textarea" name="content" ></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="mail-save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
{% include 'system/common/footer.html' %}
<script>
  layui.use(['form', 'jquery'], function () {
    let form = layui.form
    let $ = layui.jquery
    form.on('submit(mail-save)', function (data) {
      let loading = layer.load()
      $.ajax({
        url: '/system/mail/save',
        data: JSON.stringify(data.field),
        dataType: 'json',
        contentType: 'application/json',
        type: 'post',
        success: function (result) {
          layer.close(loading)
          if (result.success) {
            layer.msg(result.msg, { icon: 1, time: 1000 }, function () {
              parent.layer.close(parent.layer.getFrameIndex(window.name))//关闭当前页
              parent.layui.table.reload('mail-table')
            })
          } else {
            layer.msg(result.msg, { icon: 2, time: 1000 })
          }
        }
      })
      return false
    })
  })
</script>
<script>
</script>
</body>
</html>