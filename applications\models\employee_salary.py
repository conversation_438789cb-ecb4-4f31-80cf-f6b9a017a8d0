import datetime
from applications.extensions import db

class Employee<PERSON><PERSON>ry(db.Model):
    __tablename__ = 'employee_salary'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    employee_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('admin_yg.id'), comment='员工ID')
    month = db.Column(db.String(7), comment='月份', index=True)
    base_salary = db.Column(db.Float, comment='基本工资')
    performance_salary = db.Column(db.Float, comment='绩效工资')
    supervisor_assessment = db.Column(db.Float, comment='主管考核项')
    assessment_coefficient = db.Column(db.Float, comment='考核系数')
    position_allowance = db.Column(db.Float, comment='职务津贴')
    full_attendance = db.Column(db.Float, comment='全勤')
    overtime_pay = db.Column(db.Float, comment='加班费')
    housing_subsidy = db.Column(db.Float, comment='房补')
    project_assessment = db.Column(db.Float, comment='项目考核')
    high_temp_allowance = db.Column(db.Float, comment='高温费')
    other_allowance = db.Column(db.Float, comment='其他')
    leave_deduction = db.Column(db.Float, comment='请假扣款')
    other_deduction = db.Column(db.Float, comment='其他扣款')
    should_pay = db.Column(db.Float, comment='应发工资')
    insurance_deduction = db.Column(db.Float, comment='社保扣款')
    tax_deduction = db.Column(db.Float, comment='个税')
    year_end_tax = db.Column(db.Float, comment='年终奖个税扣款')
    total_deduction = db.Column(db.Float, comment='扣款')
    actual_salary = db.Column(db.Float, comment='实发工资')
    create_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    update_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    view_status = db.Column(db.String(20), default='pending', nullable=False, comment='查看状态: pending-未查看, confirmed-已确认, disputed-有异议')

    # 关联员工表
    employee = db.relationship('ygong', backref='salaries')


