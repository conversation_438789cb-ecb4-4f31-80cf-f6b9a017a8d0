import datetime
from datetime import date
from typing import Dict, List, Optional
from applications.models.admin_yg import ygong
from applications.models.admin_dept import Dept
from applications.models.save_log import LogInfo
from applications.services.working_hours_calculator import WorkingHoursCalculator
from applications.extensions import db


class DingTalkLogConverter:
    """钉钉日志数据转换器"""
    
    def __init__(self):
        self.working_hours_calculator = WorkingHoursCalculator()
    
    def parse_project_type(self, project_type_str: str) -> Dict[str, str]:
        """
        解析项目类型字符串
        
        Args:
            project_type_str (str): 项目类型字符串，如"GS"或"厂外GS"
            
        Returns:
            dict: {
                'projectPrefix': str,
                'projectLocation': str
            }
        """
        if not project_type_str:
            return {
                'projectPrefix': '',
                'projectLocation': '厂内'  # 默认为厂内
            }
        
        project_type_str = project_type_str.strip()
        
        if "厂外" in project_type_str:
            return {
                'projectPrefix': project_type_str.replace("厂外", "").strip(),
                'projectLocation': '厂外'
            }
        else:
            return {
                'projectPrefix': project_type_str,
                'projectLocation': '厂内'
            }
    
    def find_employee_info(self, creator_name: str, department_name: str = None) -> Dict[str, str]:
        """
        通过姓名和部门查找员工信息
        
        Args:
            creator_name (str): 创建人姓名
            department_name (str): 部门名称（可选）
            
        Returns:
            dict: {
                'name': str,
                'employee_id': str,
                'openid': str,
                'dept_id': int
            }
        """
        try:
            # 首先尝试精确匹配姓名
            query = ygong.query.filter_by(name=creator_name, enable=1)
            
            # 如果提供了部门名称，加入部门筛选
            if department_name:
                dept = Dept.query.filter_by(dept_name=department_name, status=1).first()
                if dept:
                    query = query.filter_by(dept_id=dept.id)
            
            employee = query.first()
            
            if employee:
                return {
                    'name': employee.name,
                    'employee_id': employee.employee_id or '',
                    'openid': employee.openid or '',
                    'dept_id': employee.dept_id
                }
            else:
                # 如果找不到员工，返回默认值
                print(f"警告：未找到员工信息 - 姓名: {creator_name}, 部门: {department_name}")
                return {
                    'name': creator_name,
                    'employee_id': '',
                    'openid': '',
                    'dept_id': None
                }
                
        except Exception as e:
            print(f"查找员工信息时发生错误: {str(e)}")
            return {
                'name': creator_name,
                'employee_id': '',
                'openid': '',
                'dept_id': None
            }
    
    def convert_dingtalk_log_to_model(self, dingtalk_log_data: Dict, creator_name: str) -> Dict:
        """
        将钉钉日志数据转换为模型数据
        
        Args:
            dingtalk_log_data (dict): 钉钉日志内容数据
            creator_name (str): 创建人姓名
            
        Returns:
            dict: 转换后的模型数据
        """
        try:
            # 从contents中提取字段值
            content_dict = {}
            for content in dingtalk_log_data.get('contents', []):
                key = content.get('key', '')
                value = content.get('value', '')
                content_dict[key] = value
            
            # 提取基础数据
            work_date_str = content_dict.get('日期', '')
            total_hours_str = content_dict.get('工时', '0')
            project_type = content_dict.get('项目类型', '')
            project_number = content_dict.get('项目编号', '')
            department = content_dict.get('部门', '')
            work_content = content_dict.get('工作内容', '')
            
            # 数据验证和转换
            if not work_date_str:
                raise ValueError("缺少工作日期")
            
            try:
                work_date = datetime.datetime.strptime(work_date_str, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError(f"工作日期格式错误: {work_date_str}")
            
            try:
                total_hours = float(total_hours_str)
            except (ValueError, TypeError):
                total_hours = 0.0
            
            if total_hours <= 0:
                raise ValueError("工时必须大于0")
            
            # 计算工时分配
            hours_allocation = self.working_hours_calculator.calculate_working_hours(
                total_hours, work_date
            )
            
            # 解析项目类型
            project_info = self.parse_project_type(project_type)
            
            # 查找员工信息
            employee_info = self.find_employee_info(creator_name, department)
            
            # 构建模型数据
            model_data = {
                'name': employee_info.get('name'),
                'employee_id': employee_info.get('employee_id'),
                'projectPrefix': project_info.get('projectPrefix'),
                'projectNumber': project_number,
                'regularWorkingHours': hours_allocation['regularWorkingHours'],
                'overtimeWorkingHours': hours_allocation['overtimeWorkingHours'],
                'projectLocation': project_info.get('projectLocation'),
                'openid': employee_info.get('openid'),
                'content': work_content,
                'totalHours': total_hours,
                'work_date': work_date,
                'status': '待确认'  # 默认状态
            }
            
            return model_data
            
        except Exception as e:
            raise Exception(f"转换钉钉日志数据失败: {str(e)}")
    
    def validate_model_data(self, model_data: Dict) -> Dict[str, str]:
        """
        验证转换后的模型数据
        
        Args:
            model_data (dict): 转换后的模型数据
            
        Returns:
            dict: {
                'valid': bool,
                'errors': list,
                'warnings': list
            }
        """
        errors = []
        warnings = []
        
        # 必填字段检查
        required_fields = ['name', 'projectNumber', 'totalHours', 'work_date']
        for field in required_fields:
            if not model_data.get(field):
                errors.append(f"缺少必填字段: {field}")
        
        # 工时验证
        if model_data.get('totalHours'):
            hours_validation = self.working_hours_calculator.validate_working_hours(
                model_data.get('totalHours', 0),
                model_data.get('regularWorkingHours', 0),
                model_data.get('overtimeWorkingHours', 0)
            )
            if not hours_validation['valid']:
                errors.append(f"工时验证失败: {hours_validation['message']}")
        
        # 员工信息检查
        if not model_data.get('employee_id'):
            warnings.append("未找到员工工号")
        
        if not model_data.get('openid'):
            warnings.append("未找到员工openid")
        
        # 项目信息检查
        if not model_data.get('projectPrefix'):
            warnings.append("项目前缀为空")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def save_log_to_database(self, model_data: Dict) -> Dict[str, str]:
        """
        将转换后的数据保存到数据库
        
        Args:
            model_data (dict): 转换后的模型数据
            
        Returns:
            dict: {
                'success': bool,
                'message': str,
                'log_id': int (if success)
            }
        """
        try:
            # 验证数据
            validation_result = self.validate_model_data(model_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': f"数据验证失败: {', '.join(validation_result['errors'])}"
                }
            
            # 检查是否已存在相同的日志记录
            existing_log = LogInfo.query.filter_by(
                name=model_data['name'],
                work_date=model_data['work_date'],
                projectNumber=model_data['projectNumber']
            ).first()
            
            if existing_log:
                return {
                    'success': False,
                    'message': f"已存在相同的日志记录: {model_data['name']} - {model_data['work_date']} - {model_data['projectNumber']}"
                }
            
            # 创建新的日志记录
            new_log = LogInfo(
                name=model_data['name'],
                employee_id=model_data['employee_id'],
                projectPrefix=model_data['projectPrefix'],
                projectNumber=model_data['projectNumber'],
                regularWorkingHours=model_data['regularWorkingHours'],
                overtimeWorkingHours=model_data['overtimeWorkingHours'],
                projectLocation=model_data['projectLocation'],
                openid=model_data['openid'],
                content=model_data['content'],
                totalHours=model_data['totalHours'],
                work_date=model_data['work_date'],
                status=model_data['status']
            )
            
            db.session.add(new_log)
            db.session.commit()
            
            message = f"日志保存成功: {model_data['name']} - {model_data['work_date']}"
            if validation_result['warnings']:
                message += f" (警告: {', '.join(validation_result['warnings'])})"
            
            return {
                'success': True,
                'message': message,
                'log_id': new_log.id
            }
            
        except Exception as e:
            db.session.rollback()
            return {
                'success': False,
                'message': f"保存日志失败: {str(e)}"
            }
    
    def batch_convert_and_save(self, dingtalk_logs: List[Dict]) -> Dict[str, any]:
        """
        批量转换并保存钉钉日志
        
        Args:
            dingtalk_logs (list): 钉钉日志列表
            
        Returns:
            dict: {
                'total': int,
                'success': int,
                'failed': int,
                'results': list
            }
        """
        results = []
        success_count = 0
        failed_count = 0
        
        for log_data in dingtalk_logs:
            try:
                creator_name = log_data.get('creator_name', '')
                model_data = self.convert_dingtalk_log_to_model(log_data, creator_name)
                save_result = self.save_log_to_database(model_data)
                
                if save_result['success']:
                    success_count += 1
                else:
                    failed_count += 1
                
                results.append({
                    'creator_name': creator_name,
                    'report_id': log_data.get('report_id', ''),
                    'success': save_result['success'],
                    'message': save_result['message']
                })
                
            except Exception as e:
                failed_count += 1
                results.append({
                    'creator_name': log_data.get('creator_name', ''),
                    'report_id': log_data.get('report_id', ''),
                    'success': False,
                    'message': f"处理失败: {str(e)}"
                })
        
        return {
            'total': len(dingtalk_logs),
            'success': success_count,
            'failed': failed_count,
            'results': results
        }
