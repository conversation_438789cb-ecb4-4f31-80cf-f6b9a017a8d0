from applications.extensions import db
from datetime import date

class LogInfo(db.Model):
    __tablename__ = 'log_info'  # 表名

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(50), nullable=False)
    employee_id = db.Column(db.String(15), nullable=False)
    projectPrefix = db.Column(db.String(50), nullable=False)
    projectNumber = db.Column(db.String(50), nullable=False)
    regularWorkingHours = db.Column(db.Float, nullable=False)
    overtimeWorkingHours = db.Column(db.Float, nullable=False)
    projectLocation = db.Column(db.String(255), nullable=False)
    openid = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=True)
    totalHours = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    status = db.Column(db.String(50), nullable=False)
    work_date = db.Column(db.Date, nullable=True)

    def __repr__(self):
        return f"<LogInfo {self.projectPrefix}-{self.projectNumber}>"