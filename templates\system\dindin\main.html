{% extends 'base/base.html' %}

{% block title %}钉钉日志管理{% endblock %}

{% block content %}
<div class="layui-card">
    <div class="layui-card-header">
        <span class="layui-icon layui-icon-list"></span>
        钉钉日志管理
    </div>
    <div class="layui-card-body">
        <!-- 搜索区域 -->
        <div class="layui-form layui-row layui-col-space15">
            <div class="layui-col-md3">
                <label class="layui-form-label">时间范围</label>
                <div class="layui-input-block">
                    <select name="days" id="days" lay-filter="days">
                        <option value="1">最近1天</option>
                        <option value="3">最近3天</option>
                        <option value="7" selected>最近7天</option>
                        <option value="15">最近15天</option>
                        <option value="30">最近30天</option>
                    </select>
                </div>
            </div>
            <div class="layui-col-md3">
                <label class="layui-form-label">模板名称</label>
                <div class="layui-input-block">
                    <input type="text" name="template_name" id="template_name" placeholder="请输入模板名称" class="layui-input">
                </div>
            </div>
            <div class="layui-col-md3">
                <label class="layui-form-label">用户ID</label>
                <div class="layui-input-block">
                    <input type="text" name="userid" id="userid" placeholder="请输入用户ID" class="layui-input">
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-input-block">
                    <button class="layui-btn" id="search-btn">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button class="layui-btn layui-btn-normal" id="test-connection-btn">
                        <i class="layui-icon layui-icon-link"></i> 测试连接
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <table class="layui-hide" id="dingtalk-logs-table" lay-filter="dingtalk-logs-table"></table>
    </div>
</div>

<!-- 日志详情模板 -->
<script type="text/html" id="log-detail-tpl">
    <div class="layui-card">
        <div class="layui-card-header">日志详情</div>
        <div class="layui-card-body">
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>字段值</th>
                    </tr>
                </thead>
                <tbody>
                    {{# layui.each(d.contents, function(index, item){ }}
                    <tr>
                        <td>{{ item.key }}</td>
                        <td>{{ item.value }}</td>
                    </tr>
                    {{# }); }}
                </tbody>
            </table>
        </div>
    </div>
</script>

<!-- 操作按钮模板 -->
<script type="text/html" id="toolbar-tpl">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>
</script>

{% endblock %}

{% block script %}
<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 初始化表格
    table.render({
        elem: '#dingtalk-logs-table',
        url: '/system/dindin/logs',
        method: 'GET',
        where: {
            days: 7
        },
        cols: [[
            {field: 'report_id', title: '报告ID', width: 200},
            {field: 'creator_name', title: '创建人', width: 120},
            {field: 'create_time', title: '创建时间', width: 180},
            {field: 'contents', title: '内容数量', width: 100, templet: function(d){
                return d.contents ? d.contents.length : 0;
            }},
            {title: '操作', toolbar: '#toolbar-tpl', width: 120}
        ]],
        page: true,
        limit: 15,
        limits: [15, 30, 50, 100],
        text: {
            none: '暂无相关数据'
        }
    });

    // 搜索功能
    $('#search-btn').on('click', function(){
        var days = $('#days').val();
        var template_name = $('#template_name').val();
        var userid = $('#userid').val();

        var where = {
            days: days
        };

        if(template_name) {
            where.template_name = template_name;
        }
        if(userid) {
            where.userid = userid;
        }

        table.reload('dingtalk-logs-table', {
            where: where,
            page: {
                curr: 1
            }
        });
    });

    // 测试连接功能
    $('#test-connection-btn').on('click', function(){
        var btn = $(this);
        btn.addClass('layui-btn-disabled').text('测试中...');

        $.ajax({
            url: '/system/dindin/test-connection',
            type: 'POST',
            dataType: 'json',
            success: function(res){
                if(res.success){
                    layer.msg('连接测试成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '连接测试失败', {icon: 2});
                }
            },
            error: function(){
                layer.msg('请求失败', {icon: 2});
            },
            complete: function(){
                btn.removeClass('layui-btn-disabled').html('<i class="layui-icon layui-icon-link"></i> 测试连接');
            }
        });
    });

    // 监听工具条
    table.on('tool(dingtalk-logs-table)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            layer.open({
                type: 1,
                title: '日志详情 - ' + data.creator_name,
                area: ['600px', '400px'],
                content: $('#log-detail-tpl').html().replace(/\{\{#[\s\S]*?\}\}/g, function(match){
                    return layui.laytpl(match).render(data);
                })
            });
        }
    });

    // 监听下拉选择
    form.on('select(days)', function(data){
        // 可以在这里添加自动搜索逻辑
    });
});
</script>
{% endblock %}