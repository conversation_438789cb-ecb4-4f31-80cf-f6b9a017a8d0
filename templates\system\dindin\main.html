<!DOCTYPE html>
<html>
<head>
    <title>钉钉日志管理</title>
    {% include 'system/common/header.html' %}
    <style>
        .search-form {
            background: #fff;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .search-form .layui-form-item {
            margin-bottom: 15px;
        }
        .search-form .layui-form-label {
            width: 80px;
            padding: 9px 10px;
        }
        .search-form .layui-input-block {
            margin-left: 90px;
        }
        .table-container {
            background: #fff;
            padding: 15px;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .page-header {
            background: #fff;
            padding: 15px 20px;
            margin-bottom: 15px;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
            border-left: 3px solid #009688;
        }
        .page-header h2 {
            margin: 0;
            font-size: 16px;
            color: #333;
            font-weight: 400;
        }
        .btn-group {
            text-align: right;
            margin-top: 10px;
        }
    </style>
</head>

<body class="pear-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h2><i class="layui-icon layui-icon-list"></i> 钉钉日志管理</h2>
    </div>

    <!-- 搜索区域 -->
    <div class="search-form">
        <form class="layui-form" lay-filter="search-form">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">时间范围</label>
                        <div class="layui-input-block">
                            <select name="days" id="days" lay-filter="days">
                                <option value="1">最近1天</option>
                                <option value="3">最近3天</option>
                                <option value="7" selected>最近7天</option>
                                <option value="15">最近15天</option>
                                <option value="30">最近30天</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">日志模板</label>
                        <div class="layui-input-block">
                            <select name="template_name" id="template_name" lay-filter="template_name" lay-search>
                                <option value="">请选择日志模板</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户ID</label>
                        <div class="layui-input-block">
                            <input type="text" name="userid" id="userid" placeholder="请输入用户ID" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-form-item">
                        <div class="layui-input-block btn-group">
                            <button type="button" class="layui-btn layui-btn-warm" id="load-templates-btn">
                                <i class="layui-icon layui-icon-template-1"></i> 加载模板
                            </button>
                            <button type="button" class="layui-btn" id="search-btn">
                                <i class="layui-icon layui-icon-search"></i> 搜索
                            </button>
                            <button type="button" class="layui-btn layui-btn-normal" id="test-connection-btn">
                                <i class="layui-icon layui-icon-link"></i> 测试连接
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
        <table class="layui-hide" id="dingtalk-logs-table" lay-filter="dingtalk-logs-table"></table>
    </div>

<!-- 日志详情模板 -->
<script type="text/html" id="log-detail-tpl">
    {% raw %}
    <div style="padding: 15px;">
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 80px;">报告ID:</label>
            <div class="layui-input-block" style="margin-left: 90px; line-height: 38px;">
                {{ d.report_id }}
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 80px;">创建时间:</label>
            <div class="layui-input-block" style="margin-left: 90px; line-height: 38px;">
                {{ d.create_time }}
            </div>
        </div>
        <fieldset class="layui-elem-field layui-field-title">
            <legend>日志内容</legend>
        </fieldset>
        <table class="layui-table" lay-size="sm">
            <thead>
                <tr>
                    <th style="width: 30%;">字段名</th>
                    <th>字段值</th>
                </tr>
            </thead>
            <tbody>
                {{# layui.each(d.contents, function(index, item){ }}
                <tr>
                    <td style="font-weight: bold; color: #009688;">{{ item.key }}</td>
                    <td>{{ item.value }}</td>
                </tr>
                {{# }); }}
            </tbody>
        </table>
    </div>
    {% endraw %}
</script>

<!-- 操作按钮模板 -->
<script type="text/html" id="toolbar-tpl">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>
</script>

{% include 'system/common/footer.html' %}

<script>
layui.use(['table', 'form', 'layer', 'laytpl', 'jquery'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var $ = layui.jquery;

    // 初始化表格
    table.render({
        elem: '#dingtalk-logs-table',
        url: '/system/dindin/logs',
        method: 'GET',
        where: {
            days: 7
        },
        cols: [[
            {field: 'report_id', title: '报告ID', width: 200, sort: true},
            {field: 'creator_name', title: '创建人', width: 120},
            {field: 'create_time', title: '创建时间', width: 180, sort: true},
            {field: 'contents', title: '内容数量', width: 100, align: 'center', templet: function(d){
                var count = d.contents ? d.contents.length : 0;
                return '<span class="layui-badge layui-bg-blue">' + count + '</span>';
            }},
            {title: '操作', toolbar: '#toolbar-tpl', width: 120, align: 'center'}
        ]],
        page: true,
        limit: 15,
        limits: [15, 30, 50, 100],
        loading: true,
        text: {
            none: '暂无相关数据'
        },
        done: function(res, curr, count){
            // 表格渲染完成回调
            if(res.code !== 0){
                layer.msg(res.msg || '数据加载失败', {icon: 2});
            }
        }
    });

    // 加载模板功能
    $('#load-templates-btn').on('click', function(){
        var btn = $(this);
        btn.addClass('layui-btn-disabled').text('加载中...');

        var userid = $('#userid').val();
        var params = {};
        if(userid) {
            params.userid = userid;
        }

        $.ajax({
            url: '/system/dindin/templates',
            type: 'GET',
            data: params,
            dataType: 'json',
            success: function(res){
                if(res.code === 0 && res.data){
                    // 清空现有选项
                    $('#template_name').empty();
                    $('#template_name').append('<option value="">请选择日志模板</option>');

                    // 过滤并添加有效的模板选项
                    var validTemplates = 0;
                    $.each(res.data, function(index, template){
                        // 检查模板数据是否有效
                        if(template.template_name && template.template_name !== 'null' && template.template_name.trim() !== '') {
                            $('#template_name').append(
                                '<option value="' + template.template_name + '" data-id="' + template.template_id + '">' +
                                template.template_name +
                                '</option>'
                            );
                            validTemplates++;
                        }
                    });

                    // 重新渲染select
                    form.render('select');

                    if(validTemplates > 0) {
                        layer.msg('模板加载成功，共' + validTemplates + '个有效模板', {icon: 1});
                    } else {
                        layer.msg('未找到有效的日志模板', {icon: 0});
                    }
                } else {
                    layer.msg(res.msg || '模板加载失败', {icon: 2});
                }
            },
            error: function(){
                layer.msg('请求失败', {icon: 2});
            },
            complete: function(){
                btn.removeClass('layui-btn-disabled').html('<i class="layui-icon layui-icon-template-1"></i> 加载模板');
            }
        });
    });

    // 搜索功能
    $('#search-btn').on('click', function(){
        var days = $('#days').val();
        var template_name = $('#template_name').val();
        var userid = $('#userid').val();

        var where = {
            days: days
        };

        if(template_name) {
            where.template_name = template_name;
        }
        if(userid) {
            where.userid = userid;
        }

        table.reload('dingtalk-logs-table', {
            where: where,
            page: {
                curr: 1
            }
        });
    });

    // 测试连接功能
    $('#test-connection-btn').on('click', function(){
        var btn = $(this);
        btn.addClass('layui-btn-disabled').text('测试中...');

        $.ajax({
            url: '/system/dindin/test-connection',
            type: 'POST',
            dataType: 'json',
            success: function(res){
                if(res.success){
                    layer.msg('连接测试成功', {icon: 1});
                } else {
                    layer.msg(res.msg || '连接测试失败', {icon: 2});
                }
            },
            error: function(){
                layer.msg('请求失败', {icon: 2});
            },
            complete: function(){
                btn.removeClass('layui-btn-disabled').html('<i class="layui-icon layui-icon-link"></i> 测试连接');
            }
        });
    });

    // 监听工具条
    table.on('tool(dingtalk-logs-table)', function(obj){
        var data = obj.data;
        if(obj.event === 'detail'){
            // 使用layui模板引擎渲染详情内容
            var getTpl = $('#log-detail-tpl').html();
            var view = laytpl(getTpl).render(data);

            layer.open({
                type: 1,
                title: '日志详情 - ' + data.creator_name,
                area: ['700px', '500px'],
                content: view,
                maxmin: true,
                shadeClose: true,
                shade: 0.3
            });
        }
    });

    // 监听下拉选择
    form.on('select(days)', function(data){
        // 可以在这里添加自动搜索逻辑
    });

    // 监听模板选择
    form.on('select(template_name)', function(data){
        // 模板选择变化时的处理逻辑
        console.log('选择的模板：', data.value);
    });

    // 页面加载完成后自动加载模板
    $('#load-templates-btn').trigger('click');
});
</script>

</body>
</html>