<!DOCTYPE html>
<html>
<head>
    <title>项目成本详情</title>
    {% include 'system/common/header.html' %}
    <style>
        .project-info {
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .project-info .project-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .project-info p {
            margin: 5px 0;
            color: #666;
        }
        .project-info .code {
            color: #009688;
            font-weight: 600;
        }
        .project-info .status {
            color: #fff;
            background-color: #01AAED;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .cost-card {
            padding: 15px;
            margin-bottom: 15px;
            border-left: 3px solid #009688;
            background-color: #f8f8f8;
        }
        .cost-card .title {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }
        .cost-card .amount {
            font-size: 24px;
            font-weight: 600;
            color: #009688;
            margin-bottom: 5px;
        }
        .cost-card .change {
            font-size: 14px;
            font-weight: 600;
        }
        .change.increase {
            color: #5FB878;
        }
        .change.decrease {
            color: #FF5722;
        }
        /* 差异占比颜色 */
        .change.red {
            color: #FF5722; /* 红色，超出预估 */
        }
        .change.yellow {
            color: #FFB800; /* 黄色，达到预估的80%以上但未超出 */
        }
        .change.orange {
            color: #FF9966; /* 橙色，达到预估的60%以上但未达到80% */
        }
        .change.green {
            color: #5FB878; /* 绿色，低于预估的60% */
        }
        .chart-container {
            height: 350px;
            margin-bottom: 20px;
        }
        .cost-detail-table .layui-table-cell {
            height: auto;
            line-height: 24px;
        }
        /* 项目状态颜色样式 */
        .status-draft {
            background-color: #BDBDBD !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-reviewing {
            background-color: #1E9FFF !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-pending {
            background-color: #FFB800 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-production {
            background-color: #5FB878 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-completed {
            background-color: #2F4056 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-cancelled {
            background-color: #FF5722 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-acceptance {
            background-color: #01AAED !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-debugging {
            background-color: #9966CC !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        .status-modification {
            background-color: #FF9966 !important;
            color: #fff;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
    </style>
</head>
<body class="pear-container">
    <!-- 项目基本信息 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="project-info">
                <div class="project-title">
                    项目编号：<span class="code"><span id="projectType">--</span>-{{ project.project_code }}</span> - {{ project.project_name }}
                    <span class="status" id="projectStatus">--</span>
                </div>
                <div class="layui-row">
                    <div class="layui-col-md3">
                        <p><i class="layui-icon layui-icon-rmb"></i> 项目价格：¥<span id="projectPrice">--</span></p>
                    </div>
                    <div class="layui-col-md3">
                        <p><i class="layui-icon layui-icon-component"></i> 机台数量：<span id="machineNumber">--</span></p>
                    </div>
                    <div class="layui-col-md3">
                        <p><i class="layui-icon layui-icon-chart"></i> 预估成本：¥<span id="estimateCost">--</span></p>
                    </div>
                    <div class="layui-col-md3">
                        <p><i class="layui-icon layui-icon-chart-screen"></i> 成本占比：<span id="costPercent">--</span>%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 实际成本卡片 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md3">
            <div class="cost-card">
                <div class="title">实际总成本</div>
                <div class="amount" id="totalCost">¥--</div>
                <div class="change" id="totalCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card">
                <div class="title">实际BOM成本</div>
                <div class="amount" id="bomCost">¥--</div>
                <div class="change" id="bomCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card">
                <div class="title">实际人工成本</div>
                <div class="amount" id="laborCost">¥--</div>
                <div class="change" id="laborCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card">
                <div class="title">实际其他成本</div>
                <div class="amount" id="otherCost">¥--</div>
                <div class="change" id="otherCostChange">--</div>
            </div>
        </div>
    </div>

    <!-- 预估成本卡片 -->
    <div class="layui-row layui-col-space10" style="margin-top: 10px;">
        <div class="layui-col-md3">
            <div class="cost-card" style="border-left: 3px solid #FFB800;">
                <div class="title">预估总成本</div>
                <div class="amount" id="estimateTotalCost" style="color: #FFB800;">¥--</div>
                <div class="change" id="estimateTotalCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card" style="border-left: 3px solid #FFB800;">
                <div class="title">预估BOM成本</div>
                <div class="amount" id="estimateBomCost" style="color: #FFB800;">¥--</div>
                <div class="change" id="estimateBomCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card" style="border-left: 3px solid #FFB800;">
                <div class="title">预估人工成本</div>
                <div class="amount" id="estimateLaborCost" style="color: #FFB800;">¥--</div>
                <div class="change" id="estimateLaborCostChange">--</div>
            </div>
        </div>
        <div class="layui-col-md3">
            <div class="cost-card" style="border-left: 3px solid #FFB800;">
                <div class="title">预估其他成本</div>
                <div class="amount" id="estimateOtherCost" style="color: #FFB800;">¥--</div>
                <div class="change" id="estimateOtherCostChange">--</div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">成本构成分析</div>
                <div class="layui-card-body chart-container">
                    <div id="costStructureChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            <div class="layui-card">
                <div class="layui-card-header">月度成本趋势</div>
                <div class="layui-card-body chart-container">
                    <div id="costTrendChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 成本明细表格 -->
    <div class="layui-row">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">成本明细</div>
                <div class="layui-card-body">
                    <table id="cost-detail-table" lay-filter="cost-detail-table"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 人工成本明细部分 -->
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">
                    <span>月度人工成本明细</span>
                </div>
                <div class="layui-card-body">
                    <table id="labor-cost-table" lay-filter="labor-cost-table"></table>
                </div>
            </div>
        </div>
    </div>
</body>

{% include 'system/common/footer.html' %}
<script>
    layui.use(['echarts', 'table', 'jquery', 'element'], function() {
        let echarts = layui.echarts;
        let table = layui.table;
        let $ = layui.jquery;
        let element = layui.element;

        // 将项目ID作为数值传递给JavaScript
        let projectId = parseInt('{{ project.id }}');

        // 加载项目详情数据
        loadProjectDetails();

        // 加载成本趋势图表
        loadCostTrendChart();

        // 加载成本明细表格
        loadCostDetailsTable();

        // 加载月度人工成本数据
        loadLaborCostTable();

        function loadProjectDetails() {
            $.ajax({
                url: '/system/project_cost/api/project/' + projectId,
                success: function(data) {
                    // 更新项目基本信息
                    $('#projectStatus').text(data.status_name);

                    // 根据状态设置不同的样式类
                    const $statusElement = $('#projectStatus');
                    $statusElement.removeClass('status'); // 移除默认的status类

                    // 尝试从多个可能的属性中获取状态值
                    let statusValue = null;
                    if (data.status_value !== undefined) {
                        statusValue = data.status_value;
                    } else if (data.project_status !== undefined) {
                        statusValue = data.project_status;
                    } else if (data.status !== undefined) {
                        statusValue = data.status;
                    } else if (data.data_value !== undefined) {
                        statusValue = data.data_value;
                    }

                    // 状态映射 - 根据状态值
                    const statusValueMapping = {
                        '0': 'status-pending',    // 未开始
                        '1': 'status-production', // 生产中
                        '2': 'status-production', // 安装中
                        '3': 'status-acceptance', // 验收中
                        '4': 'status-debugging',  // 调试中
                        '5': 'status-modification', // 整改中
                        '7': 'status-completed'   // 已完成
                    };

                    // 状态映射 - 根据状态名称
                    const statusNameMapping = {
                        '未开始': 'status-pending',
                        '生产中': 'status-production',
                        '安装中': 'status-production',
                        '验收中': 'status-acceptance',
                        '调试中': 'status-debugging',
                        '整改中': 'status-modification',
                        '已完成': 'status-completed'
                    };

                    // 确定使用哪个样式类
                    let statusClass = '';

                    // 优先使用状态值匹配
                    if (statusValue !== null) {
                        statusClass = statusValueMapping[statusValue] || '';
                    }

                    // 如果没有匹配到，使用状态名称匹配
                    if (!statusClass) {
                        statusClass = statusNameMapping[data.status_name] || '';
                    }

                    // 如果还没有匹配到，尝试使用包含关键词的方式
                    if (!statusClass) {
                        if (data.status_name.includes('草稿')) {
                            statusClass = 'status-draft';
                        } else if (data.status_name.includes('审核中')) {
                            statusClass = 'status-reviewing';
                        } else if (data.status_name.includes('待生产')) {
                            statusClass = 'status-pending';
                        } else if (data.status_name.includes('已取消')) {
                            statusClass = 'status-cancelled';
                        }
                    }

                    // 如果找到了匹配的样式类，则应用它，否则使用默认的status类
                    if (statusClass) {
                        $statusElement.addClass(statusClass);
                    } else {
                        $statusElement.addClass('status');
                    }

                    $('#projectType').text(data.project_type_name || '');
                    $('#projectPrice').text(data.price ? data.price.toLocaleString() : 0);
                    $('#machineNumber').text(data.machine_number || 0);
                    $('#estimateCost').text(data.estimate_cost ? data.estimate_cost.toLocaleString() : 0);
                    $('#costPercent').text(data.cost_percent);

                    // 更新实际成本卡片
                    $('#totalCost').text('¥' + data.costs.total.value.toLocaleString());
                    updateEstimateDiffDisplay('#totalCostChange', data.costs.total.value, data.estimate_costs.total.value);

                    $('#bomCost').text('¥' + data.costs.bom.value.toLocaleString());
                    updateEstimateDiffDisplay('#bomCostChange', data.costs.bom.value, data.estimate_costs.bom.value);

                    $('#laborCost').text('¥' + data.costs.labor.value.toLocaleString());
                    updateEstimateDiffDisplay('#laborCostChange', data.costs.labor.value, data.estimate_costs.labor.value);

                    $('#otherCost').text('¥' + data.costs.other.value.toLocaleString());
                    updateEstimateDiffDisplay('#otherCostChange', data.costs.other.value, data.estimate_costs.other.value);

                    // 更新预估成本卡片
                    $('#estimateTotalCost').text('¥' + data.estimate_costs.total.value.toLocaleString());
                    $('#estimateTotalCostChange').text(''); // 移除同比增长信息

                    $('#estimateBomCost').text('¥' + data.estimate_costs.bom.value.toLocaleString());
                    $('#estimateBomCostChange').text(''); // 移除同比增长信息

                    $('#estimateLaborCost').text('¥' + data.estimate_costs.labor.value.toLocaleString());
                    $('#estimateLaborCostChange').text(''); // 移除同比增长信息

                    $('#estimateOtherCost').text('¥' + data.estimate_costs.other.value.toLocaleString());
                    $('#estimateOtherCostChange').text(''); // 移除同比增长信息

                    // 渲染成本构成图表
                    renderCostStructureChart(data.costs);
                },
                error: function() {
                    layer.msg('加载项目详情数据失败', {icon: 2});
                }
            });
        }

        function updateChangeDisplay(selector, change, changeType) {
            let $el = $(selector);
            $el.removeClass('increase decrease');

            if (changeType === 'increase') {
                $el.addClass('increase');
                $el.html('<i class="layui-icon layui-icon-up"></i> ' + Math.abs(change).toFixed(1) + '% 同比增长');
            } else {
                $el.addClass('decrease');
                $el.html('<i class="layui-icon layui-icon-down"></i> ' + Math.abs(change).toFixed(1) + '% 同比下降');
            }
        }

        // 计算实际成本与预估成本的差异占比
        function updateEstimateDiffDisplay(selector, actualValue, estimateValue) {
            let $el = $(selector);
            $el.removeClass('increase decrease red yellow orange green');

            if (estimateValue === 0) {
                // 预估成本为0时，无法计算差异占比
                $el.html('无法计算');
                return;
            }

            // 计算差异占比
            let diff = actualValue - estimateValue;
            let diffPercent = (diff / estimateValue) * 100;
            let ratio = actualValue / estimateValue;

            if (diff >= 0) {
                // 实际成本大于等于预估成本，显示红色
                $el.addClass('red');
                $el.html('<i class="layui-icon layui-icon-up"></i> ' + Math.abs(diffPercent).toFixed(1) + '% 超出预估');
            } else {
                // 实际成本小于预估成本，根据比例设置不同颜色
                if (ratio >= 0.8) {
                    // 达到预估的80%以上但未超出，显示黄色
                    $el.addClass('yellow');
                } else if (ratio >= 0.6) {
                    // 达到预估的60%以上但未达到80%，显示橙色
                    $el.addClass('orange');
                } else {
                    // 低于预估的60%，显示绿色
                    $el.addClass('green');
                }
                $el.html('<i class="layui-icon layui-icon-down"></i> ' + Math.abs(diffPercent).toFixed(1) + '% 低于预估');
            }
        }

        function renderCostStructureChart(costs) {
            let chartDom = document.getElementById('costStructureChart');
            let myChart = echarts.init(chartDom);

            let option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    data: ['BOM成本', '人工成本', '其他成本']
                },
                series: [
                    {
                        name: '成本构成',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            {value: costs.bom.value, name: 'BOM成本'},
                            {value: costs.labor.value, name: '人工成本'},
                            {value: costs.other.value, name: '其他成本'}
                        ]
                    }
                ]
            };

            myChart.setOption(option);

            // 窗口大小变化时自动调整图表大小
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        function loadCostTrendChart() {
            $.ajax({
                url: '/system/project_cost/api/project/' + projectId + '/trend',
                success: function(data) {
                    let chartDom = document.getElementById('costTrendChart');
                    let myChart = echarts.init(chartDom);

                    let months = data.months;
                    let bomCosts = data.bom_costs;
                    let laborCosts = data.labor_costs;
                    let otherCosts = data.other_costs;
                    let totalCosts = data.total_costs;

                    let option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        legend: {
                            data: ['BOM成本', '人工成本', '其他成本', '总成本']
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: months
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: '¥{value}'
                            }
                        },
                        series: [
                            {
                                name: 'BOM成本',
                                type: 'bar',
                                stack: '成本',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: bomCosts
                            },
                            {
                                name: '人工成本',
                                type: 'bar',
                                stack: '成本',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: laborCosts
                            },
                            {
                                name: '其他成本',
                                type: 'bar',
                                stack: '成本',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: otherCosts
                            },
                            {
                                name: '总成本',
                                type: 'line',
                                data: totalCosts
                            }
                        ]
                    };

                    myChart.setOption(option);

                    // 窗口大小变化时自动调整图表大小
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                },
                error: function() {
                    layer.msg('加载成本趋势数据失败', {icon: 2});
                }
            });
        }

        function loadCostDetailsTable() {
            table.render({
                elem: '#cost-detail-table',
                url: '/system/project_cost/api/project/' + projectId + '/cost_details',
                page: true,
                limit: 10,
                limits: [10, 20, 50],
                cols: [[
                    {field: 'date', title: '导入日期', align: 'center'},
                    {field: 'year_month', title: '发生年月', align: 'center'},
                    {field: 'type', title: '类型', align: 'center'},
                    {field: 'amount', title: '金额', align: 'center', templet: function(d) {
                        return '¥' + d.amount.toLocaleString();
                    }},
                    {field: 'remark', title: '备注', align: 'center'}
                ]]
            });
        }

        function loadLaborCostTable() {
            $.ajax({
                url: '/system/project_cost/api/labor_cost/result?project_id=' + projectId,
                success: function(data) {
                    if (data && data.monthly_costs) {
                        // 渲染表格
                        table.render({
                            elem: '#labor-cost-table',
                            data: data.monthly_costs,
                            page: false,
                            cols: [[
                                {field: 'year_month', title: '年月', align: 'center'},
                                {field: 'cost', title: '人工成本', align: 'center', templet: function(d) {
                                    return '¥' + d.cost.toLocaleString();
                                }}
                            ]],
                            done: function() {
                                // 添加表格底部合计行
                                let totalCost = data.total_cost || 0;

                                // 在表格下方添加合计信息
                                $('#labor-cost-table').next().find('.layui-table-total').remove(); // 移除已有的合计行
                                $('#labor-cost-table').next().append(
                                    '<div class="layui-table-total">' +
                                    '<table cellspacing="0" cellpadding="0" border="0" class="layui-table">' +
                                    '<tbody><tr><td align="center">合计</td><td align="center">¥' + totalCost.toLocaleString() + '</td></tr></tbody>' +
                                    '</table></div>'
                                );
                            }
                        });
                    } else {
                        // 如果没有数据，显示空表格
                        table.render({
                            elem: '#labor-cost-table',
                            data: [],
                            page: false,
                            cols: [[
                                {field: 'work_month', title: '年月', align: 'center'},
                                {field: 'cost', title: '人工成本', align: 'center'}
                            ]]
                        });
                    }
                },
                error: function() {
                    layer.msg('加载人工成本数据失败', {icon: 2});
                }
            });
        }
    });
</script>
</html>