<!DOCTYPE html>
<html>
<head>
    <title>当前月统计数据</title>
    {% include 'system/common/header.html' %}
    <link rel="stylesheet" href="{{ url_for('static', filename='system/admin/css/other/user.css') }}"/>
    <style>
        .layui-card {
            margin-bottom: 15px;
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: bold;
        }
        .layui-card-body {
            padding: 15px;
        }
        .layui-table {
            margin-top: 0;
        }
        .echarts-chart {
            height: 300px;
        }
    </style>
</head>
<body class="pear-container">
    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 筛选表单 -->
            <form class="layui-form" action="" method="get">
                <div class="layui-row">
                    <!-- 项目前缀 -->
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目前缀</label>
                            <div class="layui-input-block">
                                <input type="text" name="project_prefix" placeholder="请输入项目前缀" class="layui-input" value="{{ project_prefix or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- 项目编号 -->
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="project_number" placeholder="请输入项目编号" class="layui-input" value="{{ project_number or '' }}">
                            </div>
                        </div>
                    </div>

                    <!-- 工作地点 -->
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <label class="layui-form-label">工作地点</label>
                            <div class="layui-input-block">
                                <select name="location" class="layui-input">
                                    <option value="">全部</option>
                                    <option value="厂内" {% if location == '厂内' %}selected{% endif %}>厂内</option>
                                    <option value="厂外" {% if location == '厂外' %}selected{% endif %}>厂外</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 查询和重置按钮 -->
                    <div class="layui-col-md3">
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn layui-btn-md" lay-submit>
                                    <i class="layui-icon layui-icon-search"></i>
                                    查询
                                </button>
                                <button type="reset" class="layui-btn layui-btn-primary layui-btn-md">
                                    <i class="layui-icon layui-icon-refresh"></i>
                                    重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="layui-card">
        <div class="layui-card-body">
            <!-- 第一行：员工数据 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            当前月员工费用预估
                            <small style="color: #999; margin-left: 10px;">(不含会议和其他类型工时)</small>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>员工姓名</th>
                                        <th>工作地点</th>
                                        <th>项目前缀</th>
                                        <th>项目编号</th>
                                        <th>正工时</th>
                                        <th>加班工时</th>
                                        <th>加班工资</th>
                                        <th>正工时费用</th>
                                        <th>总费用</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if employee_data %}
                                        {% for emp in employee_data %}
                                        <tr>
                                            <td>{{ emp.name }}</td>
                                            <td>{{ emp.location }}</td>
                                            <td>{{ emp.projectPrefix }}</td>
                                            <td>{{ emp.projectNumber }}</td>
                                            <td>{{ emp.regularWorkingHours }}</td>
                                            <td>{{ emp.overtimeWorkingHours }}</td>
                                            <td>{{ emp.overtime_pay }}</td>
                                            <td>{{ emp.regular_pay }}</td>
                                            <td>{{ emp.total_pay }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="9" style="text-align: center;">
                                                <div style="display: flex; flex-direction: column; align-items: center; padding: 20px;">
                                                    <i class="layui-icon layui-icon-face-cry" style="font-size: 30px; color: #999;"></i>
                                                    <p style="margin-top: 10px; color: #666;">暂无数据</p>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页控件 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div id="pagination" class="layui-box layui-laypage"></div>
                </div>
            </div>

            <!-- 第二行：部门数据 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            当前月部门预估费用
                            <small style="color: #999; margin-left: 10px;">(不含会议和其他类型工时)</small>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-table">
                                <thead>
                                    <tr>
                                        <th>工作地点</th>
                                        <th>项目前缀</th>
                                        <th>项目编号</th>
                                        <th>正工时</th>
                                        <th>加班工时</th>
                                        <th>加班工资</th>
                                        <th>正工时费用</th>
                                        <th>总费用</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if dept_pay %}
                                        {% for dept in dept_pay %}
                                        <tr>
                                            <td>{{ dept.location }}</td>
                                            <td>{{ dept.projectPrefix }}</td>
                                            <td>{{ dept.projectNumber }}</td>
                                            <td>{{ dept.total_regular_hours }}</td>
                                            <td>{{ dept.total_overtime_hours }}</td>
                                            <td>{{ dept.total_overtime_pay }}</td>
                                            <td>{{ dept.total_regular_pay }}</td>
                                            <td>{{ dept.total_pay }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="8" style="text-align: center;">
                                                <div style="display: flex; flex-direction: column; align-items: center; padding: 20px;">
                                                    <i class="layui-icon layui-icon-face-cry" style="font-size: 30px; color: #999;"></i>
                                                    <p style="margin-top: 10px; color: #666;">暂无数据</p>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 部门费用分页控件 -->
            <div class="layui-row">
                <div class="layui-col-md12">
                    <div id="deptPagination" class="layui-box layui-laypage"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 在部门数据表格下方添加饼图 -->
    {% if employee_data %}
    <div class="layui-card">
        <div class="layui-card-body">
            <div class="layui-row">
                <!-- 员工数据饼图 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            员工数据分布
                            <small style="color: #999; margin-left: 10px;">(不含会议和其他类型工时)</small>
                        </div>
                        <div class="layui-card-body">
                            <div id="employeePieChart" style="height: 400px;"></div>
                        </div>
                    </div>
                </div>

                <!-- 部门费用饼图 -->
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            部门费用分布
                            <small style="color: #999; margin-left: 10px;">(不含会议和其他类型工时)</small>
                        </div>
                        <div class="layui-card-body">
                            <div id="deptPieChart" style="height: 400px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="layui-card">
        <div class="layui-card-body">
            <div style="text-align: center; padding: 30px;">
                <i class="layui-icon layui-icon-chart" style="font-size: 50px; color: #999;"></i>
                <p style="margin-top: 10px; color: #666;">暂无数据可供图表展示</p>
            </div>
        </div>
    </div>
    {% endif %}

    {% include 'system/common/footer.html' %}

    <script>
        layui.use(['element', 'table', 'laypage'], function(){
            var element = layui.element;
            var table = layui.table;
            var laypage = layui.laypage;
            
            // 初始化折叠面板
            element.render('collapse');

            // 初始化分页
            laypage.render({
                elem: 'pagination',
                count: {{ total }},  // 总数据量
                limit: {{ per_page }},  // 每页显示条数
                curr: {{ page }},  // 当前页
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],  // 分页布局
                jump: function(obj, first){
                    if (!first) {
                        // 跳转到新页面
                        window.location.href = "{{ url_for('system.Statistical_data.current') }}?page=" + obj.curr + "&per_page=" + obj.limit;
                    }
                }
            });
        });
    </script>

    <!-- 在页面底部添加饼图脚本 -->
    <script src="{{ url_for('static', filename='index/js/echarts.min.js') }}"></script>
    <script>
        // 员工数据饼图
        var employeePieChart = echarts.init(document.getElementById('employeePieChart'));
        var employeePieOption = {
            title: {
                text: '员工数据分布',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    return params.name + ': ' + params.value + ' 元 (' + params.percent + '%)';
                }
            },
            series: [{
                type: 'pie',
                radius: '50%',
                data: [
                    {% for emp in employee_data %}
                    {
                        name: '{{ emp.name }}',
                        value: {{ emp.total_pay }},
                        itemStyle: {
                            color: '#' + Math.floor(Math.random() * 16777215).toString(16)  // 随机颜色
                        }
                    },
                    {% endfor %}
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    formatter: function(params) {
                        return params.name + ': ' + params.value + ' 元 (' + params.percent + '%)';
                    }
                }
            }]
        };
        employeePieChart.setOption(employeePieOption);

        // 部门费用饼图
        var deptPieChart = echarts.init(document.getElementById('deptPieChart'));
        var deptPieOption = {
            title: {
                text: '部门费用分布',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    return params.name + ': ' + params.value + ' 元 (' + params.percent + '%)';
                }
            },
            series: [{
                type: 'pie',
                radius: '50%',
                data: [
                    {% for dept in dept_pay %}
                    {
                        name: '{{ dept.location }} (加班工资)',
                        value: {{ dept.total_overtime_pay }},
                        itemStyle: {
                            color: '#' + Math.floor(Math.random() * 16777215).toString(16)  // 随机颜色
                        }
                    },
                    {
                        name: '{{ dept.location }} (正工时费用)',
                        value: {{ dept.total_regular_pay }},
                        itemStyle: {
                            color: '#' + Math.floor(Math.random() * 16777215).toString(16)  // 随机颜色
                        }
                    },
                    // {
                    //     name: '{{ dept.location }} (总费用)',
                    //     value: {{ dept.total_pay }},
                    //     itemStyle: {
                    //         color: '#' + Math.floor(Math.random() * 16777215).toString(16)  // 随机颜色
                    //     }
                    // },
                    {% endfor %}
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    formatter: function(params) {
                        return params.name + ': ' + params.value + ' 元 (' + params.percent + '%)';
                    }
                }
            }]
        };
        deptPieChart.setOption(deptPieOption);

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            employeePieChart.resize();
            deptPieChart.resize();
        });
    </script>

    <!-- 在页面底部添加部门费用分页脚本 -->
    <script>
        layui.use(['element', 'table', 'laypage'], function(){
            var element = layui.element;
            var table = layui.table;
            var laypage = layui.laypage;
            
            // 初始化部门费用分页
            laypage.render({
                elem: 'deptPagination',
                count: {{ dept_total }},  // 部门费用总数据量
                limit: {{ dept_per_page }},  // 每页显示条数
                curr: {{ dept_page }},  // 当前页
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],  // 分页布局
                jump: function(obj, first){
                    if (!first) {
                        // 跳转到新页面
                        window.location.href = "{{ url_for('system.Statistical_data.current') }}?page={{ page }}&per_page={{ per_page }}&dept_page=" + obj.curr + "&dept_per_page=" + obj.limit;
                    }
                }
            });
        });
    </script>
</body>
</html>

